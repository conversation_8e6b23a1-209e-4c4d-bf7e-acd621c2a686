# Telegram Bot 投稿逻辑流程文档

## 系统架构

### 核心组件

- **主入口**: `src/index.js` - Webhook 处理和消息路由
- **媒体转发**: `src/handlers/forwardMedia.js` - 自动转发逻辑
- **命令处理**: `src/handlers/commandCallbacks.js` - /0 命令投稿逻辑
- **投稿群处理**: `src/handlers/callbackQuery.js` - 投稿群内操作
- **统一删除**: `src/utils/submissionDeletion.js` - 统一的数据清理逻辑
- **按钮工具**: `src/utils/buttonUtils.js` - 投稿按钮生成和状态管理

### 数据库表

- `tg_log_media_group`: 媒体组原始数据存储
- `tg_log_forwarded_submissions`: 投稿记录和状态跟踪
- `tg_media_group_queue`: 媒体组处理队列

## 投稿流程详解

### 1. 自动转发流程

#### 1.1 媒体消息检测

```
源群组消息 → 媒体类型判断 → 群组权限检查 → 转发处理
```

**触发条件**:

- 消息来自配置的转发群组
- 消息包含媒体内容（图片、视频、文档等）
- 发送者非 Telegram 官方账号

**处理逻辑**:

```javascript
// 单个媒体消息
if (!message.media_group_id) {
    await forwardMedia(botCtx);
}

// 媒体组消息
if (message.media_group_id) {
    await queueMediaGroupForward(botCtx);  // 加入队列
    ctx.waitUntil(processAllPendingQueues(env));  // 异步处理
}
```

#### 1.2 媒体组队列处理

```
媒体组消息 → 加入队列 → 等待完整性 → 批量转发 → 生成按钮
```

**关键特性**:

- **延迟处理**: 等待 3 秒确保媒体组完整性
- **去重机制**: 防止重复添加相同媒体组
- **批量转发**: 所有媒体一次性转发到投稿群

#### 1.3 话题分配逻辑

```javascript
// 话题映射规则
const TOPIC_MAPPING = {
    'normal': 6,     // 普通内容 → 话题6 (未分级)
    'submission': 4   // #投稿内容 → 话题4 (已投稿)
};

// 检测#投稿标签
function hasSubmissionTag(message) {
    const caption = message.caption || '';
    return caption.includes('#投稿');
}
```

### 2. 手动投稿流程 (/0 命令)

#### 2.1 命令触发和消息检测

```
/0 命令 → 权限验证 → 消息来源检测 → 生成投稿界面或显示错误
```

**权限验证**:

- Bot 管理员
- 群组管理员
- 匿名管理员

**消息来源检测**:

```javascript
// 检测机器人消息
if (replyMessage.from.is_bot) {
    alertText = '🥺 无法取得消息内容，可能是其他机器人发送的消息';
}

// 检测Telegram官方频道消息 (ID: 777000)
if (replyMessage.from.id === 777000) {
    alertText = '🤔 不该处理的消息，这是群内绑定频道发送的推送';
}
```

**错误处理**:

- 显示相应提示消息
- 5秒后自动删除提示
- 阻止进一步处理

#### 2.2 投稿界面生成

```
消息检测通过 → 检查媒体类型 → 生成对应按钮布局
```

**按钮布局逻辑**:

```javascript
// 检测是否有原始说明文字
function hasOriginalCaption(message) {
    if (!message.caption) return false;
  
    // 排除仅包含via/from信息的情况
    const cleanCaption = message.caption
        .replace(/via @\w+/g, '')
        .replace(/from .+/g, '')
        .trim();
  
    return cleanCaption.length > 0;
}

// 按钮布局
const buttonLayout = [
    ['立即投稿'],
    hasOriginalCaption(message) ? ['去除说明'] : null,
    ['🔄取消分级', 'NSFW'],
    ['奇闻异录', '三重警告'],
    ['等英雄', '迷惑'],
    ['取消']
].filter(Boolean);
```

#### 2.3 去除说明功能

```
用户点击去除说明 → 切换选择状态 → 更新显示 → 独立于分级选项
```

**功能特性**:

- **独立切换**: 不影响分级按钮选择
- **状态显示**: 显示当前选择的组合状态
- **媒体专用**: 仅对有原始说明的媒体消息显示

**状态管理**:

```javascript
// 获取当前选择状态
function getCurrentSelectedOption(callbackData) {
    return {
        gradeOption: extractGradeOption(callbackData),
        removeCaption: callbackData.includes('removeCaption:true')
    };
}

// 状态显示示例
const displayStates = {
    'nsfw + removeCaption': '[NSFW + 去除说明]',
    'removeCaption only': '[去除说明]',
    'nsfw only': '[NSFW]'
};
```

#### 2.4 分级处理

```
用户选择分级 → 更新按钮状态 → 应用分级标记 → 预览效果
```

**分级配置**:

```javascript
const gradeConfigs = {
    normal: { displayName: '普通', needsSpoiler: false },
    nsfw: { displayName: 'NSFW', needsSpoiler: true, prefix: '🔞NSFW⚠️' },
    qwyl: { displayName: '奇闻异录', needsSpoiler: false, prefix: '🤔奇闻异录🤔' },
    R18G: { displayName: '三重警告', needsSpoiler: true, prefix: '💀三重警告💀' },
    waitHeroes: { displayName: '等一位英雄', needsSpoiler: true, prefix: '🦸等一位英雄🦸' }
};
```

#### 2.5 投稿执行

```
点击投稿 → 媒体组检查 → 应用分级和去除说明 → 发送到频道 → 清理数据
```

**内容处理逻辑**:

```javascript
// 应用去除说明和分级
function applyGradeToMessage(message, gradeOption, removeCaption = false) {
    let newCaption = '';
  
    if (!removeCaption && message.caption) {
        // 保留原始说明
        newCaption = message.caption;
    } else if (removeCaption) {
        // 去除说明，仅保留via/from信息
        const viaMatch = message.caption?.match(/via @\w+/);
        const fromMatch = message.caption?.match(/from .+/);
  
        if (viaMatch) newCaption += viaMatch[0];
        if (fromMatch) newCaption += (newCaption ? '\n' : '') + fromMatch[0];
    }
  
    // 添加分级前缀
    if (gradeOption && gradeOption !== 'normal') {
        const config = gradeConfigs[gradeOption];
        if (config.prefix) {
            newCaption = config.prefix + (newCaption ? '\n' + newCaption : '');
        }
    }
  
    return { ...message, caption: newCaption };
}
```

**媒体组处理**:

1. 从 `tg_log_media_group` 获取完整数据
2. 对目标消息应用选中分级和去除说明选项
3. 对其他媒体应用遮罩（如需要）
4. 批量发送到目标频道

**数据清理**:

```javascript
// 统一删除逻辑
await deleteSubmissionData(env, originalMessageId, sourceGroupId, {
    deleteMessages: true,    // 删除投稿群消息
    deleteDatabase: true,    // 删除数据库记录
    buttonMessage: message   // 删除操作按钮
});
```

### 3. #投稿文字处理

#### 3.1 触发条件

```
用户回复 "#投稿" → 群组检查 → 媒体验证 → 状态转换
```

**限制条件**:

- 仅在沙雕英雄群 (`-1001143091022`) 生效
- 必须回复包含媒体的消息

#### 3.2 状态转换逻辑

```javascript
// 状态检查和转换
if (!existingRecord) {
    // 无记录：直接转发到话题4
    shouldForwardToTopic4 = true;
} else if (existingRecord.sub_topic_id === 4) {
    // 已在话题4：跳过处理
    return;
} else if (existingRecord.sub_topic_id === 6) {
    // 在话题6：删除后转发到话题4
    await deleteMessage(env, TARGET_GROUP_ID, existingRecord.sub_message_id, 6);
    shouldForwardToTopic4 = true;
}
```

### 4. 投稿群内操作

#### 4.1 分级操作

```
点击分级按钮 → 编辑消息内容 → 更新按钮状态 → 保留按钮
```

**处理特点**:

- 实时编辑消息内容
- 保留操作按钮供后续使用
- 支持媒体组同步分级
- 支持去除说明功能
- 统一的按钮状态管理

#### 4.2 去除说明操作

```
点击去除说明按钮 → 切换状态 → 更新按钮显示 → 独立于分级选择
```

**功能特性**:

- **完全统一**: 手动投稿（/0命令）和自动转发投稿群内都支持去除说明功能，逻辑完全一致
- **独立切换**: 去除说明按钮与分级按钮完全独立，互不影响
- **智能显示**: 基于原始消息对象判断，仅对有原始说明文字的媒体消息显示去除说明按钮
- **状态保持**: 切换分级时保持去除说明的选择状态
- **数据源统一**: 优先使用传递的原始消息对象，降级时才查询数据库

**状态管理**:

```javascript
// 投稿群内按钮状态管理
function getCurrentContributeButtonState(replyMarkup) {
    let gradeOption = null;
    let removeCaption = false;
  
    for (const row of replyMarkup.inline_keyboard) {
        for (const button of row) {
            if (button.callback_data.startsWith('co:') && button.text.startsWith('✅')) {
                const option = button.callback_data.split(':')[1];
                if (option === 'removeCaption') {
                    removeCaption = true;
                } else {
                    gradeOption = option;
                }
            }
        }
    }
  
    return { gradeOption, removeCaption };
}

// 按钮状态更新逻辑
function updateContributeButtonStates(buttons, selectedOption) {
    return buttons.map(row => 
        row.map(button => {
            if (button.callback_data.startsWith('co:')) {
                const buttonOption = button.callback_data.split(':')[1];
                let cleanText = button.text.replace('✅', '').trim();
      
                // 去除说明按钮：独立切换
                if (buttonOption === 'removeCaption') {
                    if (selectedOption === 'removeCaption') {
                        cleanText = button.text.startsWith('✅') ? cleanText : '✅' + cleanText;
                    } else {
                        cleanText = button.text.startsWith('✅') ? '✅' + cleanText : cleanText;
                    }
                }
                // 分级按钮：只有点击分级时才更新
                else if (selectedOption !== 'removeCaption') {
                    cleanText = buttonOption === selectedOption ? '✅' + cleanText : cleanText;
                } else {
                    cleanText = button.text.startsWith('✅') ? '✅' + cleanText : cleanText;
                }
      
                return { ...button, text: cleanText };
            }
            return button;
        })
    );
}
```

#### 4.3 投稿到频道

```
点击投稿按钮 → 获取媒体数据 → 应用分级和去除说明 → 发送频道 → 添加反应 → 清理数据
```

**成功后操作**:

1. 给原始消息添加 反应表情
2. 删除投稿群内的所有相关消息
3. 删除数据库中的投稿记录

#### 4.4 取消操作

```
点击取消 → 删除投稿群消息 → 删除数据库记录
```

## 数据流转

### 消息状态追踪

```mermaid
stateDiagram-v2
    [*] --> 源群组
    源群组 --> 话题6: 自动转发
    源群组 --> 话题4: #投稿直接
    话题6 --> 话题4: #投稿转换
    话题6 --> 频道: 投稿群操作
    话题4 --> 频道: 投稿群操作
    话题6 --> [*]: 取消/删除
    话题4 --> [*]: 取消/删除
    频道 --> [*]: 投稿完成
```

### 数据库关系

```
tg_log_media_group
├── message_id (主键)
├── chat_id
├── media_group_id
├── raw_json (原始消息数据)
└── timestamp

tg_log_forwarded_submissions  
├── id (主键)
├── msg_id (源消息ID)
├── group_id (源群组ID)
├── sub_message_id (投稿群消息ID)
├── sub_topic_id (话题ID: 4=已投稿, 6=未分级)
├── media_group_id (媒体组ID)
└── timestamp
```

## 权限系统

### 权限层级

1. **Bot 管理员**: 环境变量 `ADMIN_USER_ID` 配置
2. **群组管理员**: Telegram API 检测
3. **匿名管理员**: 匿名发送者且有管理员权限

### 权限检查点

- /0 命令执行
- /0 命令回调操作
- 投稿群内按钮点击（继承消息权限）

## 消息检测和过滤

### 不可处理消息类型

1. **机器人消息** (`is_bot: true`)

   - 提示: "🥺 无法取得消息内容，可能是其他机器人发送的消息"
   - 5秒后自动删除提示
2. **Telegram官方频道消息** (`id: 777000`)

   - 提示: "🤔 不该处理的消息，这是群内绑定频道发送的推送"
   - 5秒后自动删除提示

### 检测逻辑

```javascript
// 消息来源检测
function checkMessageSource(message) {
    if (message.from.is_bot) {
        return {
            canProcess: false,
            alertText: '🥺 无法取得消息内容，可能是其他机器人发送的消息'
        };
    }
  
    if (message.from.id === 777000) {
        return {
            canProcess: false,
            alertText: '🤔 不该处理的消息，这是群内绑定频道发送的推送'
        };
    }
  
    return { canProcess: true };
}
```

## 按钮配置系统

### 按钮配置结构

```javascript
const BUTTON_CONFIG = {
    submit: { text: '立即投稿', callback: 'submit' },
    removeCaption: { text: '🗑️去除说明', callback: 'removeCaption:false' },
    cancel: { text: '取消', callback: 'cancel' }
};

const GRADE_BUTTON_CONFIG = {
    normal: { text: '普通', callback: 'grade:normal' },
    nsfw: { text: 'NSFW', callback: 'grade:nsfw' },
    qwyl: { text: '奇闻异录', callback: 'grade:qwyl' },
    R18G: { text: '三重警告', callback: 'grade:R18G' },
    waitHeroes: { text: '等一位英雄', callback: 'grade:waitHeroes' },
    clear: { text: '🔄取消分级', callback: 'grade:clear' }
};
```

### 动态按钮生成

```javascript
// 统一的按钮创建函数
async function createUnifiedSubmissionButtons(options) {
    const { type } = options;
  
    if (type === 'command') {
        // /0命令投稿按钮
        return createCommandContributeButtons(options.message, options.groupConfig);
    } else if (type === 'contribute') {
        // 投稿群内按钮（支持去除说明）
        return await createSubmitToChannelButtons(
            options.chatId, 
            options.originalMessageId, 
            options.forwardedMessageId, 
            options.topicId, 
            options.defaultGroupsBind, 
            options.env
        );
    }
  
    return null;
}

// 投稿群内按钮生成逻辑（已统一）
async function createSubmitToChannelButtons(chatId, originalMessageId, forwardedMessageId, topicId, defaultGroupsBind, env, originalMessage = null) {
    const buttons = [
        [{ text: '🚀投稿到频道', callback_data: `sc:${chatId}:${originalMessageId}:${forwardedMessageId}` }]
    ];
  
    // 统一的去除说明按钮显示逻辑
    let showRemoveCaptionButton = false;
  
    // 优先使用传递的原始消息对象
    if (originalMessage) {
        showRemoveCaptionButton = hasOriginalCaption(originalMessage);
    } else if (env && (topicId === '4' || topicId === '6' || topicId === '2829')) {
        // 降级：从数据库获取原始消息信息
        const dbMessage = await getOriginalMessageFromDatabase(env, chatId, originalMessageId);
        if (dbMessage) {
            showRemoveCaptionButton = hasOriginalCaption(dbMessage);
        }
    }
  
    if (showRemoveCaptionButton) {
        buttons.push([{ text: '🗑️去除说明', callback_data: `co:removeCaption:${forwardedMessageId}` }]);
    }
  
    // 分级按钮
    buttons.push(
        [{ text: '🔄取消分级', callback_data: `co:clear:${forwardedMessageId}` }, { text: '🔞NSFW⚠️', callback_data: `co:nsfw:${forwardedMessageId}` }],
        [{ text: '#️⃣奇闻异录', callback_data: `co:qw:${forwardedMessageId}` }, { text: '⚠️三重警告', callback_data: `co:R18G:${forwardedMessageId}` }],
        [{ text: '🥺等一位英雄', callback_data: `co:wh:${forwardedMessageId}` }, { text: '🤔迷惑', callback_data: `co:cf:${forwardedMessageId}` }],
        [{ text: '❌取消', callback_data: 'cc' }]
    );
  
    return { inline_keyboard: buttons };
}

// 统一的原始说明检测逻辑
function hasOriginalCaption(message) {
    // 检查是否是媒体消息
    const isMediaMessage = !!(
        message.photo || message.video || message.animation ||
        message.document || message.audio || message.voice ||
        message.video_note || message.sticker
    );
  
    // 纯文字消息不显示去除说明按钮
    if (!isMediaMessage) return false;
  
    // 检查是否有caption
    const hasCaption = message.caption && message.caption.trim() !== '';
  
    // 如果有caption，检查是否不只是via/from信息
    if (hasCaption) {
        const caption = message.caption.trim();
        const isOnlyViaFrom = /^(via\s+.+|from\s+.+|via\s+.+\s+from\s+.+)$/i.test(caption);
        return !isOnlyViaFrom;
    }
  
    return false;
}
```

## 错误处理

### 降级策略

```javascript
// 投稿失败降级
if (result && result.ok) {
    // 成功处理
    await deleteSubmissionData(env, originalMessageId, sourceGroupId);
} else {
    // 失败降级：保留数据，仅显示错误
    Logger.error('投稿失败，保留投稿数据供重试');
}

// 数据库查询失败降级
try {
    const originalMessage = await getOriginalMessageFromDatabase(env, chatId, originalMessageId);
    showRemoveCaptionButton = hasOriginalCaption(originalMessage);
} catch (error) {
    // 降级：不显示去除说明按钮
    Logger.debug('获取原始消息失败，跳过去除说明按钮');
    showRemoveCaptionButton = false;
}
```

### 错误恢复

- **消息删除失败**: 继续执行后续操作，记录错误日志
- **按钮更新失败**: 不影响主要功能，保持原有按钮状态
- **数据库操作失败**: 降级到仅操作消息，避免数据不一致

#### 🔧 主要改进

1. **投稿群内按钮增强**

   - 新增去除说明按钮支持
   - 从数据库动态获取原始消息信息
   - 智能判断是否显示去除说明按钮
2. **统一的按钮状态管理**

   - 独立的去除说明按钮状态切换
   - 与分级按钮完全解耦的状态管理
   - 一致的用户交互体验
3. **代码复用和封装**

   - 统一的按钮创建函数 `createUnifiedSubmissionButtons`
   - 共享的原始说明检测逻辑 `hasOriginalCaption`
   - 标准化的数据库查询接口

#### 🔄 工作流程统一

**手动投稿流程**:

```
/0命令 → 检测原始说明 → 显示去除说明按钮 → 用户选择 → 应用处理 → 投稿
```

**自动转发流程**:

```
自动转发 → 数据库查询原始消息 → 显示去除说明按钮 → 用户选择 → 应用处理 → 投稿
```

#### 🛠️ 技术实现要点

1. **按钮逻辑统一**: 按钮显示逻辑基于传递的原始消息对象，而不是数据库查询
2. **数据源优化**: 优先使用传递的消息对象，降级时才查询数据库
3. **格式处理统一**: 统一使用单换行符分隔，消除多余空行问题
4. **状态管理统一**: 使用相同的状态检测和更新逻辑
5. **错误处理**: 完善的降级策略，确保功能稳定性

## 配置说明

### 群组绑定

```javascript
const DefaultGroupsBind = [
    {
        id: '-1001143091022',        // 沙雕英雄群
        name: '沙雕英雄',
        defaultChannel: '-1001566585081'  // 对应频道
    }
];
```

### 目标群组

```javascript
const TargetGroups = {
    forwardMedia: ['-1001143091022'],     // 转发媒体的群组
    logMedia: ['-1001143091022']          // 记录媒体的群组
};
```

### 话题配置

```javascript
const TOPIC_MAPPING = {
    normal: 6,        // 未分级话题
    submission: 4     // 已投稿话题  
};

const TARGET_GROUP_ID = '-1002599022189';  // 投稿处理群
```

## 性能优化

### 队列处理

- 媒体组 3 秒延迟确保完整性
- 异步队列处理避免阻塞
- 定期清理过期队列记录

### 数据库优化

- 索引设计：`(msg_id, group_id)`, `(media_group_id)`
- 定期清理：删除过期记录
- 批量操作：媒体组统一处理

### API 调用优化

- 频率限制：200ms 间隔
- 批量删除：单次删除多个消息
- 错误重试：关键操作失败重试

## 维护指南

### 常见问题

1. **媒体组不完整**: 检查队列延迟配置
2. **权限错误**: 验证 Bot 管理员权限
3. **数据不一致**: 运行数据库清理脚本
4. **去除说明功能异常**: 检查媒体消息caption检测逻辑

### 监控指标

- 投稿成功率
- 队列处理延迟
- 数据库记录增长
- API 调用频率
- 去除说明功能使用率

### 功能测试清单

- [ ] /0 命令权限验证
- [ ] 机器人消息检测和提示
- [ ] Telegram官方频道消息检测和提示
- [ ] 去除说明按钮显示逻辑
- [ ] 去除说明功能独立切换
- [ ] 分级和去除说明组合状态显示
- [ ] 媒体组去除说明处理
- [ ] 投稿内容正确性验证

---
