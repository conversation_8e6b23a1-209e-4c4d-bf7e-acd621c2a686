# Durable Objects 键值存储封装使用指南

本指南介绍如何在 Cloudflare Workers 项目中使用简化的 Durable Objects 键值存储接口。

## 🚀 快速开始

### 1. 基本使用

```javascript
import DO from '../utils/objectStore.js';

// 在 Worker 的 fetch 函数中初始化
DO.init(env);

// 设置值
await DO.set('user:123', { name: '张三', age: 25 });

// 获取值
const user = await DO.get('user:123');
console.log(user); // { name: '张三', age: 25 }

// 删除值
await DO.delete('user:123');
```

### 2. 带过期时间的存储

```javascript
// 设置值，60秒后自动过期
await DO.set('session:abc123', { userId: 456 }, 60);

// 设置临时缓存，5分钟后过期
await DO.set('cache:api:response', responseData, 300);
```

## 📚 完整API参考

### 基本操作

| 方法 | 说明 | 示例 |
|------|------|------|
| `DO.set(key, value, ttl?)` | 设置键值对 | `await DO.set('name', 'value')` |
| `DO.get(key, defaultValue?)` | 获取值 | `await DO.get('name', 'default')` |
| `DO.delete(key)` | 删除键 | `await DO.delete('name')` |
| `DO.has(key)` | 检查键是否存在 | `await DO.has('name')` |
| `DO.clear()` | 清空所有数据 | `await DO.clear()` |

### 批量操作

```javascript
// 批量设置
await DO.setMultiple({
    'config:theme': 'dark',
    'config:lang': 'zh-CN',
    'config:debug': true
});

// 批量获取
const configs = await DO.getMultiple(['config:theme', 'config:lang']);
```

### 查询操作

```javascript
// 获取所有键
const allKeys = await DO.keys();

// 获取指定前缀的键
const userKeys = await DO.keys('user:', 10); // 最多10个

// 获取存储大小
const count = await DO.size();
```

## 🎯 实际使用场景

### 1. 用户会话管理

```javascript
// 创建会话
const sessionId = generateSessionId();
await DO.set(`session:${sessionId}`, {
    userId: 123,
    loginTime: Date.now(),
    permissions: ['read', 'write']
}, 1800); // 30分钟过期

// 验证会话
const session = await DO.get(`session:${sessionId}`);
if (session) {
    // 会话有效
    console.log('用户ID:', session.userId);
}
```

### 2. 缓存数据库查询

```javascript
const cacheKey = 'db:users:page1';
let users = await DO.get(cacheKey);

if (!users) {
    // 从数据库查询
    users = await queryDatabase('SELECT * FROM users LIMIT 20');
    // 缓存5分钟
    await DO.set(cacheKey, users, 300);
}

return users;
```

### 3. 计数器和统计

```javascript
// 页面访问计数
const visitKey = 'counter:page:home';
let visits = await DO.get(visitKey, 0);
visits++;
await DO.set(visitKey, visits);

// 每日统计（带过期）
const today = new Date().toISOString().split('T')[0];
const dailyKey = `stats:daily:${today}`;
let dailyStats = await DO.get(dailyKey, { visits: 0, actions: 0 });
dailyStats.visits++;
// 设置过期时间为当天结束
await DO.set(dailyKey, dailyStats, 86400);
```

### 4. 配置管理

```javascript
// 应用配置
const defaultConfig = {
    maxUploadSize: 10485760,
    allowedTypes: ['image/jpeg', 'image/png'],
    rateLimit: 100
};

// 获取配置，如果不存在则使用默认值
let config = await DO.get('app:config', defaultConfig);

// 更新配置
config.rateLimit = 200;
await DO.set('app:config', config);
```

## 🔧 高级用法

### 1. 创建多个存储实例

```javascript
import { createObjectStore } from '../utils/objectStore.js';

// 创建专用的用户数据存储
const userStore = createObjectStore(env, 'KEY_VALUE_STORE', 'user-data');

// 创建专用的缓存存储
const cacheStore = createObjectStore(env, 'KEY_VALUE_STORE', 'cache-data');

await userStore.set('profile', userProfile);
await cacheStore.set('api:response', apiData, 300);
```

### 2. 错误处理

```javascript
try {
    const success = await DO.set('important:data', complexObject);
    if (!success) {
        console.error('数据保存失败');
    }
} catch (error) {
    console.error('存储错误:', error);
}

// 安全的获取操作（总是返回默认值，不会抛出异常）
const safeData = await DO.get('might:not:exist', null);
```

### 3. 键命名约定

建议使用有意义的键名前缀：

```javascript
// 用户相关数据
await DO.set('user:123:profile', userProfile);
await DO.set('user:123:settings', userSettings);

// 会话数据
await DO.set('session:abc123', sessionData);

// 缓存数据
await DO.set('cache:api:users:page1', apiResponse);

// 配置数据
await DO.set('config:app', appConfig);

// 计数器
await DO.set('counter:visits:home', visitCount);
```

## ⚙️ 配置说明

### 1. wrangler.toml 配置

确保您的 `wrangler.toml` 包含以下配置：

```toml
# Durable Objects 配置
[[durable_objects.bindings]]
name = "KEY_VALUE_STORE"
class_name = "KeyValueStore"

# 迁移配置
[[migrations]]
tag = "v2"
new_sqlite_classes = ["KeyValueStore"]
```

### 2. 项目结构

```
src/
├── durableObjects/
│   └── keyValueStore.js     # Durable Object 实现
├── utils/
│   └── objectStore.js       # 封装工具类
├── examples/
│   └── objectStoreExample.js # 使用示例
└── index.js                 # 主文件（导出 Durable Object）
```

## 🚨 注意事项

1. **初始化**：在使用全局 `DO` 实例前，必须先调用 `DO.init(env)`
2. **异步操作**：所有方法都是异步的，需要使用 `await`
3. **数据类型**：支持所有可 JSON 序列化的数据类型
4. **过期时间**：TTL 以秒为单位，最大值受 Cloudflare 限制
5. **存储限制**：每个 Durable Object 实例有存储大小限制

## 🔄 从现有代码迁移

如果您已有使用原生 Durable Objects API 的代码：

```javascript
// 原来的代码
const objectId = env.MY_DURABLE_OBJECT.idFromName('my-object');
const stub = env.MY_DURABLE_OBJECT.get(objectId);
const response = await stub.fetch(request);

// 新的简化代码
DO.init(env);
await DO.set('key', 'value');
const value = await DO.get('key');
```


现在可以像使用普通变量一样简单地操作 Durable Objects 存储了！

 `src/examples/objectStoreExample.js` 中有完整示例。 

这个 DO 封装完美适合解决 Workers 中的这些传统难题：
✅ 持久化数据 - 完全替代文件存储和数据库
✅ 全局变量 - 跨请求保持状态
✅ 多请求同步 - 实现共享状态管理
✅ 会话管理 - 替代传统 session 中间件
✅ 缓存系统 - 替代 Redis 等外部缓存
✅ 限流防护 - 实现跨请求的速率限制
使用这个封装，可以将传统 NodeJS 项目的复杂状态管理逻辑直接迁移到 Workers 环境中，而且性能更好、运维更简单！