# Durable Objects Alarm 定时任务系统

## 📋 概述

本文档说明了项目中使用 Cloudflare Workers Durable Objects 的 `alarm()` 功能来替代不稳定的 `setTimeout` 定时器的技术方案。这套系统解决了在生产环境中长时间定时任务执行不稳定的问题。

## 🚨 问题背景

### 原始问题

在 Cloudflare Workers 生产环境中，`setTimeout` 存在以下问题：

1. **超时限制**：Workers 有执行时间限制，长时间 `setTimeout` 可能被中断
2. **实例重启**：Worker 实例重启会导致所有 `setTimeout` 丢失
3. **不可靠性**：即使使用 `ctx.waitUntil()` 包装，长时间定时器仍不稳定

### 具体表现

```javascript
// ❌ 这种写法在生产环境不稳定
ctx.waitUntil(new Promise(resolve => {
    setTimeout(async () => {
        // 28秒后删除验证消息
        await deleteMessage(chatId, messageId);
        resolve();
    }, 28000);
}));
```

**问题表现**：
- 验证超时消息删除失效
- 欢迎消息30秒自动删除不执行
- 投稿按钮15秒自动删除失效

## 🛠️ 解决方案

使用 **Durable Objects 的 `alarm()` 功能** 替代 `setTimeout`：

### 核心优势

1. **持久化**：即使 Worker 实例重启，任务仍会执行
2. **精确性**：系统级定时，比 `setTimeout` 更可靠
3. **可扩展**：支持任意时长的定时任务
4. **免费**：无需付费队列服务

## 🏗️ 技术架构

### 系统组件

```
┌─────────────────────────────────────┐
│           应用层调用                │
├─────────────────────────────────────┤
│  scheduleVerificationTimeout()     │  验证超时调度
│  scheduleMessageDelete()           │  消息删除调度
├─────────────────────────────────────┤
│         工具函数层                  │
├─────────────────────────────────────┤
│  VerificationTimer DO              │  验证定时器
│  MessageScheduler DO               │  消息调度器
├─────────────────────────────────────┤
│        Durable Objects             │
│        alarm() 系统功能             │
└─────────────────────────────────────┘
```

### 实现的 Durable Objects

#### 1. VerificationTimer
- **文件**：`src/durableObjects/verificationTimer.js`
- **用途**：处理验证流程中的超时任务
- **支持任务**：
  - `group_timeout`：群组验证28秒超时
  - `math_timeout`：数学题验证28秒超时

#### 2. MessageScheduler
- **文件**：`src/durableObjects/messageScheduler.js`
- **用途**：处理通用的消息自动删除任务
- **支持任务**：
  - `delete_message`：延迟删除指定消息

## 📚 使用方法

### 验证超时调度

```javascript
import { scheduleVerificationTimeout } from '../utils/verificationTimer.js';

// 调度验证超时任务
await scheduleVerificationTimeout(
    env,                    // 环境变量
    sessionId,              // 会话ID
    userId,                 // 用户ID
    chatId,                 // 群组ID
    'group_timeout',        // 任务类型
    28                      // 延迟秒数
);
```

### 消息删除调度

```javascript
import { scheduleMessageDelete } from '../../utils/messageScheduler.js';

// 调度消息删除任务
await scheduleMessageDelete(
    env,                    // 环境变量
    chatId,                 // 聊天ID
    messageId,              // 消息ID
    30,                     // 延迟秒数
    '欢迎消息自动删除'      // 任务描述
);
```

## 🔧 配置说明

### wrangler.toml 配置

```toml
# Durable Objects 配置
[[durable_objects.bindings]]
name = "VERIFICATION_TIMER"
class_name = "VerificationTimer"

[[durable_objects.bindings]]
name = "MESSAGE_SCHEDULER"
class_name = "MessageScheduler"

# 迁移配置
[[migrations]]
tag = "v3"
new_sqlite_classes = ["VerificationTimer"]

[[migrations]]
tag = "v4"
new_sqlite_classes = ["MessageScheduler"]
```

### 导出配置

```javascript
// src/index.js
export { 
    TelegramRateLimiter, 
    KeyValueStore, 
    VerificationTimer,      // 验证定时器
    MessageScheduler        // 消息调度器
};
```

## 📊 迁移对照表

| 原 setTimeout 用法 | 新 DO Alarm 用法 | 时长 | 状态 |
|-------------------|------------------|------|------|
| 验证超时删除 | `scheduleVerificationTimeout` | 28秒 | ✅ 已迁移 |
| 欢迎消息删除 | `scheduleMessageDelete` | 30秒 | ✅ 已迁移 |
| 投稿按钮删除 | `scheduleMessageDelete` | 15秒 | ✅ 已迁移 |
| 提示消息删除 | `scheduleMessageDelete` | 5秒 | ✅ 已迁移 |
| 媒体组延迟 | `setTimeout` | 2秒 | ⏸️ 保持不变 |
| 解封延迟 | `setTimeout` | 1秒 | ⏸️ 保持不变 |

## 🎯 最佳实践

### 何时使用 DO Alarm

✅ **推荐使用**：
- 时长 ≥ 5秒的定时任务
- 消息删除相关任务
- 业务逻辑重要的定时任务
- 需要高可靠性的定时任务

⚠️ **可继续使用 setTimeout**：
- 时长 < 5秒的短暂延迟
- 非关键业务的微延迟
- API请求超时控制
- 批处理间隔延迟

### 代码示例对比

```javascript
// ❌ 旧方案：不稳定的 setTimeout
ctx.waitUntil(new Promise(resolve => {
    setTimeout(async () => {
        await deleteMessage(chatId, messageId);
        resolve();
    }, 30000);
}));

// ✅ 新方案：稳定的 DO Alarm
await scheduleMessageDelete(env, chatId, messageId, 30, '消息自动删除');
```

## 🚀 部署注意事项

### 首次部署

1. **Durable Object 注册**：新的 DO 类需要在生产环境注册
2. **迁移执行**：确保 migrations 正确执行
3. **测试验证**：部署后测试定时任务是否正常工作

### 监控要点

```javascript
// 日志监控关键词
Logger.debug('🔔 已设置 Durable Object alarm');
Logger.info('⏰ 执行到期的验证超时任务');
Logger.debug('🗑️ 自动删除消息成功');
```

## 📈 技术优势

### 可靠性对比

| 特性 | setTimeout | DO Alarm |
|------|------------|----------|
| 实例重启存活 | ❌ | ✅ |
| 长时间稳定性 | ⚠️ | ✅ |
| 精确度 | 中等 | 高 |
| 免费可用 | ✅ | ✅ |
| 配置复杂度 | 低 | 中等 |

### 性能影响

- **启动成本**：DO 首次创建有轻微延迟
- **运行成本**：每个 alarm 触发消耗少量 CPU 时间
- **存储成本**：任务数据存储在 DO 中，占用极少空间

## 🔍 故障排查

### 常见问题

1. **任务未执行**
   ```javascript
   // 检查 alarm 是否设置成功
   const currentAlarm = await this.state.storage.getAlarm();
   Logger.debug('当前 alarm 时间:', new Date(currentAlarm));
   ```

2. **任务重复执行**
   ```javascript
   // 确保任务执行后删除
   await this.state.storage.delete(`task:${taskId}`);
   ```

3. **时间不准确**
   ```javascript
   // 验证时间计算
   const triggerTime = Date.now() + (delaySeconds * 1000);
   Logger.debug('预期触发时间:', new Date(triggerTime));
   ```

## 📝 更新日志

### v1.0 - 验证定时器 (2024-07-31)
- 创建 `VerificationTimer` DO
- 解决28秒验证超时问题
- 替代验证流程中的 setTimeout

### v1.1 - 消息调度器 (2024-07-31)
- 创建 `MessageScheduler` DO
- 解决消息自动删除问题
- 统一管理所有消息删除定时任务

## 🔗 相关文档

- [Cloudflare Durable Objects 文档](https://developers.cloudflare.com/workers/learning/using-durable-objects/)
- [Durable Objects Alarms](https://developers.cloudflare.com/workers/learning/using-durable-objects/#alarms)
- [项目 Durable Objects 指南](./Guide-durable-objects.md)

---

> **💡 提示**：这套系统确保了定时任务在生产环境中的可靠执行，是项目稳定性的重要保障。