## 注释标记规范

```sql
-- #region 📊 数据库架构 - 一级
  -- #region 🔧 核心表结构 - 二级
    -- #region 👤 用户相关表 - 三级
    -- TODO: 完成用户认证功能
    -- FIXME: 修复登录超时问题  
    -- NOTE: 这里是重要的业务逻辑
    CREATE TABLE users (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE
    );
    
    CREATE TABLE user_profiles (
        user_id INTEGER REFERENCES users(id),
        avatar_url TEXT,
        bio TEXT
    );
    -- #endregion 👤 用户相关表

    -- #region 📝 投稿相关表 - 三级
    -- 投稿系统: 核心投稿处理逻辑
    -- 数据库: 表结构设计
    CREATE TABLE tg_log_sub_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        msg_id INTEGER NOT NULL,
        raw_json TEXT NOT NULL
    );
    
    CREATE TABLE submission_queue (
        id INTEGER PRIMARY KEY,
        message_id INTEGER,
        status TEXT DEFAULT 'pending'
    );
    -- #endregion 📝 投稿相关表
  -- #endregion 🔧 核心表结构

  -- #region 📊 索引优化 - 二级
    -- #region 🚀 性能索引 - 三级
    -- TODO: 添加数据验证
    -- NOTE: 性能优化相关
    CREATE INDEX idx_user_name ON users(name);
    CREATE INDEX idx_user_email ON users(email);
    -- #endregion 🚀 性能索引

    -- #region 🔍 查询索引 - 三级
    CREATE INDEX idx_sub_msg_group ON tg_log_sub_messages(msg_id, group_id);
    CREATE INDEX idx_sub_media_group ON tg_log_sub_messages(media_group_id);
    -- #endregion 🔍 查询索引
  -- #endregion 📊 索引优化
-- #endregion 📊 数据库架构

-- #region ⚙️ 系统配置 - 一级
  -- #region 🔐 权限管理 - 二级
    -- #region 👥 角色定义 - 三级
    -- TODO: 实现角色权限控制
    -- FIXME: 权限验证逻辑有bug
    CREATE TABLE roles (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        permissions TEXT
    );
    -- #endregion 👥 角色定义

    -- #region 🛡️ 权限检查 - 三级
    CREATE TABLE user_roles (
        user_id INTEGER REFERENCES users(id),
        role_id INTEGER REFERENCES roles(id)
    );
    -- #endregion 🛡️ 权限检查
  -- #endregion 🔐 权限管理

  -- #region 📈 监控统计 - 二级
  -- 📝 NOTE: 使用 emoji 也可以让注释更醒目
  -- 🚀 TODO: 性能优化任务
  -- 🐛 FIXME: 已知问题修复
  -- ⚠️  WARNING: 重要警告信息
  CREATE TABLE system_stats (
      id INTEGER PRIMARY KEY,
      metric_name TEXT,
      value INTEGER,
      timestamp INTEGER
  );
  -- #endregion 📈 监控统计
-- #endregion ⚙️ 系统配置

-- SECTION: 权限管理部分
-- TODO: 实现角色权限控制
-- FIXME: 权限验证逻辑有bug

-- 📝 NOTE: 使用 emoji 也可以让注释更醒目
-- 🚀 TODO: 性能优化任务
-- 🐛 FIXME: 已知问题修复
-- ⚠️  WARNING: 重要警告信息 

-- #region 投稿处理群消息记录表
-- 代码内容
-- #endregion 
```

## 注释标记示例
```javascript
// #region 🏗️ 模块架构模板 - 展示完整的层级结构
/**
 * 这是一个展示如何使用 region 层级结构的模板文件
 * 适用于大型处理器模块的注释组织
 */

// #region 📦 依赖导入
import Logger from '../utils/logger.js';
import { sendTelegramRequest } from '../utils/telegramApi.js';
// #endregion 📦 依赖导入

// #region 🔧 工具函数与常量
  // #region 📊 常量定义
  const MAX_RETRY_COUNT = 3;
  const DEFAULT_TIMEOUT = 5000;
  // #endregion 📊 常量定义

  // #region 🛠️ 辅助工具函数
  function formatMessage(text) {
    return text.trim();
  }
  
  function validateInput(input) {
    return input && typeof input === 'string';
  }
  // #endregion 🛠️ 辅助工具函数
// #endregion 🔧 工具函数与常量

// #region 🎯 核心处理逻辑
  // #region 🔍 输入验证与解析
  function parseCommand(message) {
    // #region 📝 基础验证
    if (!message || !message.text) {
      return null;
    }
    // #endregion 📝 基础验证

    // #region 🔄 命令解析
    const text = message.text.trim();
    const parts = text.split(' ');
    return {
      command: parts[0],
      args: parts.slice(1)
    };
    // #endregion 🔄 命令解析
  }
  // #endregion 🔍 输入验证与解析

  // #region 📋 业务逻辑处理
    // #region 🏠 基础命令处理
    function handleBasicCommands(command, args) {
      switch (command) {
        case '/start':
          return createResponse('欢迎使用！');
        case '/help':
          return createResponse('帮助信息...');
        default:
          return null;
      }
    }
    // #endregion 🏠 基础命令处理

    // #region 🎨 高级功能处理
    function handleAdvancedCommands(command, args) {
      // TODO: 实现高级功能
      // NOTE: 这里处理复杂的业务逻辑
      return null;
    }
    // #endregion 🎨 高级功能处理
  // #endregion 📋 业务逻辑处理

  // #region 💬 响应生成与格式化
    // #region 🔧 响应构建
    function createResponse(text, options = {}) {
      return {
        method: 'sendMessage',
        text: text,
        parse_mode: 'HTML',
        ...options
      };
    }
    // #endregion 🔧 响应构建

    // #region 🎨 消息格式化
    function formatResponse(data) {
      // #region 📝 文本处理
      if (typeof data === 'string') {
        return data;
      }
      // #endregion 📝 文本处理

      // #region 🏷️ 对象格式化
      return JSON.stringify(data, null, 2);
      // #endregion 🏷️ 对象格式化
    }
    // #endregion 🎨 消息格式化
  // #endregion 💬 响应生成与格式化
// #endregion 🎯 核心处理逻辑

// #region 📤 导出接口
/**
 * 主要处理函数
 * @param {Object} ctx 上下文对象
 * @returns {Object|null} 处理结果
 */
export async function handleRequest(ctx) {
  try {
    // #region 🔍 请求预处理
    const parsed = parseCommand(ctx.message);
    if (!parsed) {
      return null;
    }
    // #endregion 🔍 请求预处理

    // #region 🎯 命令路由
    let result = handleBasicCommands(parsed.command, parsed.args);
    if (!result) {
      result = handleAdvancedCommands(parsed.command, parsed.args);
    }
    // #endregion 🎯 命令路由

    // #region 📤 结果处理
    return result ? {
      ...result,
      chat_id: ctx.message.chat.id,
      reply_to_message_id: ctx.message.message_id
    } : null;
    // #endregion 📤 结果处理

  } catch (error) {
    // #region 🚨 错误处理
    Logger.error('处理请求时出错:', error);
    return createResponse('处理请求时出错，请稍后再试。');
    // #endregion 🚨 错误处理
  }
}
// #endregion 📤 导出接口
// #endregion 🏗️ 模块架构模板 
```