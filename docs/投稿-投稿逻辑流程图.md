# Telegram Bot 投稿逻辑流程图

## 主流程图

```mermaid
graph TD
    A[用户消息] --> B{消息类型判断}
    
    B --> C["/0 命令"]
    C --> D{权限检查}
    D -->|有权限| E{消息内容检查}
    D -->|无权限| F[返回权限错误]
    
    E --> G{消息来源判断}
    G -->|机器人消息| H[显示机器人消息提示]
    G -->|频道推送消息| I[显示频道推送提示]
    G -->|普通用户消息| J[生成投稿按钮]
    
    H --> K[5秒后自动删除提示]
    I --> K
    J --> L[用户点击按钮]
    
    L --> M{按钮类型}
    M --> N[分级选项]
    M --> O[去除说明选项]
    M --> P[投稿确认]
    M --> Q[取消操作]
    
    N --> R[更新按钮状态]
    O --> S[切换去除说明状态]
    R --> T[应用分级标记]
    S --> U[显示当前选择状态]
    
    P --> V{投稿处理}
    V --> W{是否媒体组}
    W -->|是| X[从数据库获取完整媒体组]
    W -->|否| Y[单个消息处理]
    
    X --> Z[应用分级和去除说明到所有媒体]
    Y --> AA[应用分级和去除说明到单个消息]
    
    Z --> BB[发送到目标频道]
    AA --> BB
    
    BB --> CC{投稿是否成功}
    CC -->|成功| DD[删除投稿群消息和数据库记录]
    CC -->|失败| EE[显示错误信息]
    
    Q --> FF[仅删除操作按钮消息]
    
    B --> GG["#投稿 文字回复"]
    GG --> HH{检查群组}
    HH -->|沙雕英雄群| II[查询转发记录]
    HH -->|其他群| JJ[忽略处理]
    
    II --> KK{记录状态}
    KK -->|无记录| LL[直接转发到话题4]
    KK -->|话题4中| MM[跳过处理]
    KK -->|话题6中| NN[删除话题6记录]
    
    NN --> LL
    LL --> OO[记录到数据库]
    
    B --> PP[媒体消息]
    PP --> QQ{群组检查}
    QQ -->|转发群组| RR{是否媒体组}
    QQ -->|其他群组| SS[忽略]
    
    RR -->|是| TT[加入媒体组队列]
    RR -->|否| UU[直接转发处理]
    
    TT --> VV[队列处理]
    VV --> WW[等待媒体组完整]
    WW --> XX[批量转发到投稿群]
    
    UU --> YY[单个媒体转发]
    YY --> ZZ[转发到投稿群]
    
    XX --> AAA[生成统一投稿按钮]
    ZZ --> AAA
    AAA --> BBB[记录到数据库]
    AAA --> CCC[传递原始消息对象]
    
    B --> CCC[投稿群内按钮点击]
    CCC --> DDD{按钮类型}
    DDD --> EEE[分级操作]
    DDD --> FFF[去除说明操作]
    DDD --> GGG[投稿到频道]
    DDD --> HHH[取消操作]
    
    EEE --> III[编辑消息分级]
    FFF --> JJJ[切换去除说明状态]
    III --> KKK[更新按钮状态]
    JJJ --> KKK
    
    GGG --> LLL{媒体组处理}
    LLL -->|是| MMM[获取完整媒体组]
    LLL -->|否| NNN[单个消息]
    
    MMM --> OOO[应用分级和去除说明到媒体组]
    NNN --> PPP[应用分级和去除说明到单个]
    
    OOO --> QQQ[发送到频道]
    PPP --> QQQ
    
    QQQ --> RRR{投稿成功?}
    RRR -->|成功| SSS[添加反应表情<br/>删除消息和数据库记录]
    RRR -->|失败| TTT[显示错误]
    
    HHH --> UUU[删除消息和数据库记录]
```

## 消息检测流程图

```mermaid
graph TD
    A["命令触发"] --> B{权限验证}
    B -->|无权限| C[返回权限错误]
    B -->|有权限| D{检查回复消息}
    
    D -->|无回复消息| E[提示需要回复消息]
    D -->|有回复消息| F{消息来源检测}
    
    F --> G{发送者类型}
    G -->|机器人消息| H[机器人消息检测]
    G -->|官方频道消息| I[Telegram官方频道检测]
    G -->|普通用户| J[正常处理流程]
    
    H --> K["显示机器人消息提示<br/>无法取得消息内容"]
    I --> L["显示频道推送提示<br/>不该处理的消息"]
    
    K --> M[5秒后自动删除提示]
    L --> M
    
    J --> N[生成投稿界面]
    N --> O{统一检查媒体类型和原始说明}
    O -->|有原始说明文字的媒体| P[显示去除说明按钮]
    O -->|纯文字或无原始说明| Q[不显示去除说明按钮]
    
    P --> R["完整按钮布局<br/>立即投稿-去除说明-分级按钮-取消"]
    Q --> S["标准按钮布局<br/>立即投稿-分级按钮-取消"]
```

## 统一的去除说明功能流程图

```mermaid
graph TD
    A[用户点击去除说明按钮] --> B{当前状态}
    B -->|未选中| C[设置为选中状态]
    B -->|已选中| D[设置为未选中状态]
    
    C --> E[更新按钮显示状态]
    D --> E
    
    E --> F{是否同时选择了分级}
    F -->|是| G["显示组合状态<br/>例:NSFW+去除说明"]
    F -->|否| H["显示单独状态<br/>去除说明"]
    
    G --> I[保持按钮可用]
    H --> I
    
    I --> J[用户可继续选择其他选项]
    J --> K{用户点击投稿}
    
    K --> L{处理投稿内容}
    L --> M{去除说明状态}
    M -->|已选中| N["移除原始说明文字<br/>保留分级标记和via信息<br/>使用单换行符格式"]
    M -->|未选中| O["保留完整内容<br/>添加分级标记<br/>使用单换行符格式"]
    
    N --> P[发送到目标频道]
    O --> P
```

## 消息状态转换图

```mermaid
stateDiagram-v2
    [*] --> 源群组消息
    源群组消息 --> 话题6投稿群: 自动转发
    源群组消息 --> 话题4投稿群: #投稿标签
    话题6投稿群 --> 话题4投稿群: #投稿文字转换
    话题6投稿群 --> 目标频道: 投稿群内操作
    话题4投稿群 --> 目标频道: 投稿群内操作
    话题6投稿群 --> [*]: 取消/删除
    话题4投稿群 --> [*]: 取消/删除
    目标频道 --> [*]: 投稿完成
    
    note right of 目标频道
        统一的投稿功能：
        - 保留原始说明
        - 去除说明文字
        - 应用分级标记
        - 统一格式处理
        - 单个媒体和媒体组一致体验
    end note
```

## 权限验证流程

```mermaid
graph TD
    A[用户操作] --> B{检查用户ID}
    B -->|匹配ADMIN_USER_ID| C[Bot管理员权限]
    B -->|不匹配| D{检查群组管理员}
    
    D -->|是管理员| E[群组管理员权限]
    D -->|非管理员| F{检查匿名权限}
    
    F -->|匿名+管理员| G[匿名管理员权限]
    F -->|其他| H[权限不足]
    
    C --> I[允许操作]
    E --> I
    G --> I
    H --> J[拒绝操作]
```

## 媒体组处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant B as Bot
    participant Q as 队列
    participant DB as 数据库
    participant TG as 投稿群
    
    U->>B: 发送媒体组第1条
    B->>Q: 加入队列
    B->>DB: 记录原始数据
    
    U->>B: 发送媒体组第2条
    B->>Q: 加入队列
    B->>DB: 记录原始数据
    
    U->>B: 发送媒体组第N条
    B->>Q: 加入队列
    B->>DB: 记录原始数据
    
    Note over Q: 等待3秒确保完整性
    
    Q->>DB: 查询完整媒体组
    Q->>TG: 批量转发媒体组
    TG->>DB: 记录投稿状态
    Q->>Q: 清理队列记录
```

## 统一按钮显示逻辑流程图

```mermaid
graph TD
    A[创建投稿按钮] --> B{是否传递了原始消息对象}
    B -->|是| C[使用传递的消息对象]
    B -->|否| D[尝试从数据库获取]
    
    C --> E[检查媒体类型]
    D --> F{数据库查询成功?}
    F -->|是| E
    F -->|否| G[不显示去除说明按钮]
    
    E --> H{是否为媒体消息?}
    H -->|否| G
    H -->|是| I{是否有原始说明文字?}
    
    I -->|否| G
    I -->|是| J[显示去除说明按钮]
    
    G --> K[生成标准按钮布局]
    J --> L[生成完整按钮布局]
    
    K --> M[返回按钮配置]
    L --> M
```

## 数据清理流程

```mermaid
graph TD
    A[触发删除操作] --> B{删除类型}
    
    B --> C["0命令投稿成功"]
    B --> D[投稿群投稿成功]
    B --> E[投稿群取消操作]
    B --> F["0命令取消"]
    
    C --> G[查询数据库记录]
    D --> G
    E --> G
    F --> H[仅删除按钮消息]
    
    G --> I{找到记录?}
    I -->|是| J[删除投稿群消息]
    I -->|否| K[跳过消息删除]
    
    J --> L{是否媒体组?}
    L -->|是| M[删除整个媒体组消息]
    L -->|否| N[删除单个消息]
    
    M --> O[删除数据库记录]
    N --> O
    K --> O
    
    O --> P[操作完成]
    H --> P
```

---