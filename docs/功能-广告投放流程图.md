# 广告投放流程图

## 📊 系统整体架构流程图

```mermaid
graph TB
    subgraph "用户交互层"
        UI[Mini App 管理界面]
        USER[管理员用户]
    end
    
    subgraph "API服务层" 
        API[Cloudflare Workers API]
        CRON[Cron 定时触发器]
    end
    
    subgraph "业务逻辑层"
        ADM[AdManager 广告管理器]
        TIME[时间检查模块]
        SEND[发送执行模块]
    end
    
    subgraph "数据存储层"
        DB[(Cloudflare D1 数据库)]
        CAMP[ad_campaigns 规则表]
        POSTS[ad_posts 记录表]
    end
    
    subgraph "外部服务"
        TG[Telegram Bot API]
        CHAN[目标频道]
    end
    
    USER --> UI
    UI --> API
    CRON --> API
    API --> ADM
    ADM --> TIME
    ADM --> SEND
    ADM --> DB
    DB --> CAMP
    DB --> POSTS
    SEND --> TG
    TG --> CHAN
```

## 🕐 定时广告投放详细流程

```mermaid
flowchart TD
    START([Cron 每分钟触发]) --> INIT[初始化定时任务]
    INIT --> QUERY[查询活跃广告规则]
    
    QUERY --> CHECK_RULES{有活跃规则?}
    CHECK_RULES -->|否| END_NO_RULES[无规则需要处理]
    CHECK_RULES -->|是| LOOP[遍历每个规则]
    
    LOOP --> TIME_CHECK[时间匹配检查]
    TIME_CHECK --> HOUR_MATCH{当前小时 = 设定小时?}
    HOUR_MATCH -->|否| NEXT_RULE[处理下一个规则]
    HOUR_MATCH -->|是| FREQ_CHECK[频率检查]
    
    FREQ_CHECK --> LAST_SENT[查询该时间段最后发送记录]
    LAST_SENT --> NEVER_SENT{从未发送?}
    NEVER_SENT -->|是| CAN_SEND[可以发送]
    NEVER_SENT -->|否| CALC_DAYS[计算天数差异]
    
    CALC_DAYS --> DAYS_CHECK{天数差异 > 频率设置?}
    DAYS_CHECK -->|否| NEXT_RULE
    DAYS_CHECK -->|是| CAN_SEND
    
    CAN_SEND --> EXECUTE[执行广告发送]
    EXECUTE --> FORWARD[转发消息到目标频道]
    FORWARD --> FORWARD_SUCCESS{转发成功?}
    FORWARD_SUCCESS -->|否| LOG_ERROR[记录错误日志]
    FORWARD_SUCCESS -->|是| DELETE_OLD[删除旧广告]
    
    DELETE_OLD --> RECORD[记录发送记录]
    RECORD --> PIN_CHECK{需要置顶?}
    PIN_CHECK -->|是| PIN_MSG[置顶消息]
    PIN_CHECK -->|否| SUCCESS[发送成功]
    PIN_MSG --> SUCCESS
    
    SUCCESS --> NEXT_RULE
    LOG_ERROR --> NEXT_RULE
    NEXT_RULE --> MORE_RULES{还有规则?}
    MORE_RULES -->|是| LOOP
    MORE_RULES -->|否| END_SUCCESS[所有规则处理完成]
    
    END_NO_RULES --> FINAL([任务结束])
    END_SUCCESS --> FINAL
```

## ⏰ 时间判断逻辑详细流程

```mermaid
flowchart TD
    START([开始时间检查]) --> GET_TIME[获取当前UTC时间]
    GET_TIME --> PARSE_TARGET[解析规则设定时间]
    
    PARSE_TARGET --> HOUR_COMPARE{currentHour == targetHour?}
    HOUR_COMPARE -->|否| REJECT[时间不匹配]
    HOUR_COMPARE -->|是| QUERY_HISTORY[查询该publish_time的发送历史]
    
    QUERY_HISTORY --> HAS_HISTORY{有发送记录?}
    HAS_HISTORY -->|否| ALLOW[从未发送，允许发送]
    HAS_HISTORY -->|是| CALC_DIFF[计算时间差异]
    
    CALC_DIFF --> GET_LAST[获取最后发送时间]
    GET_LAST --> DAYS_DIFF[daysDiff = floor now - lastSent / 24h]
    DAYS_DIFF --> FREQUENCY_CHECK{daysDiff > frequency_days?}
    
    FREQUENCY_CHECK -->|否| REJECT_FREQ[频率未达到]
    FREQUENCY_CHECK -->|是| ALLOW_FREQ[频率达到，允许发送]
    
    REJECT --> LOG_TIME[记录时间不匹配日志]
    REJECT_FREQ --> LOG_FREQ[记录频率未达到日志]
    ALLOW --> PROCEED[继续执行发送]
    ALLOW_FREQ --> PROCEED
    
    LOG_TIME --> END_REJECT([拒绝发送])
    LOG_FREQ --> END_REJECT
    PROCEED --> END_ALLOW([允许发送])
    
    style REJECT fill:#ffcccc
    style REJECT_FREQ fill:#ffcccc
    style ALLOW fill:#ccffcc
    style ALLOW_FREQ fill:#ccffcc
```

## 🔄 多时间段支持逻辑

```mermaid
graph TD
    subgraph "同一广告内容"
        CONTENT[广告内容: 消息ID 123]
    end
    
    subgraph "规则1: 早间时段"
        RULE1[名称: 早间推广<br/>时间: 09:00<br/>频率: 每天]
        HIST1[发送历史: publish_time='09:00']
        CHECK1[检查09:00时段最后发送]
    end
    
    subgraph "规则2: 晚间时段"  
        RULE2[名称: 晚间推广<br/>时间: 18:00<br/>频率: 每天]
        HIST2[发送历史: publish_time='18:00']
        CHECK2[检查18:00时段最后发送]
    end
    
    CONTENT --> RULE1
    CONTENT --> RULE2
    
    RULE1 --> CHECK1
    RULE2 --> CHECK2
    
    CHECK1 --> SEND1{09:00可发送?}
    CHECK2 --> SEND2{18:00可发送?}
    
    SEND1 -->|是| EXEC1[09:00发送] 
    SEND1 -->|否| SKIP1[跳过09:00]
    
    SEND2 -->|是| EXEC2[18:00发送]
    SEND2 -->|否| SKIP2[跳过18:00]
    
    EXEC1 --> RECORD1[记录: publish_time='09:00']
    EXEC2 --> RECORD2[记录: publish_time='18:00']
    
    RECORD1 --> HIST1
    RECORD2 --> HIST2
```

## 🗑️ 删除旧广告容错流程

```mermaid
flowchart TD
    START([开始删除旧广告]) --> QUERY[查询未删除的旧广告记录]
    QUERY --> HAS_OLD{有旧广告?}
    HAS_OLD -->|否| NO_OLD[无需删除]
    HAS_OLD -->|是| LOOP[遍历每条旧记录]
    
    LOOP --> TRY_DELETE[尝试删除Telegram消息]
    TRY_DELETE --> DELETE_API[调用deleteMessage API]
    DELETE_API --> API_RESULT{API调用结果}
    
    API_RESULT -->|成功| MARK_SUCCESS[标记数据库为已删除]
    API_RESULT -->|失败| LOG_FAIL[记录删除失败日志]
    
    LOG_FAIL --> MARK_ANYWAY[仍然标记数据库为已删除]
    MARK_ANYWAY --> REASON[原因: 避免重复尝试删除]
    
    MARK_SUCCESS --> NEXT_OLD{还有旧广告?}
    REASON --> NEXT_OLD
    
    NEXT_OLD -->|是| LOOP
    NEXT_OLD -->|否| COMPLETE[旧广告清理完成]
    
    NO_OLD --> END([删除流程结束])
    COMPLETE --> END
    
    style LOG_FAIL fill:#ffcccc
    style MARK_ANYWAY fill:#ccffcc
    style REASON fill:#fff2cc
```

## 🌍 时区转换处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端JS
    participant D as 数据库
    participant W as Workers后端
    participant C as Cron触发器
    
    Note over U,C: 创建广告规则流程
    U->>F: 输入本地时间 14:00
    F->>F: 转换为UTC: 14:00 → 06:00
    F->>D: 存储UTC时间: "06:00"
    
    Note over U,C: 定时执行流程
    C->>W: UTC 06:00 触发
    W->>D: 查询publish_time="06:00"
    W->>W: 当前UTC时间匹配检查
    W->>W: 发送广告
    
    Note over U,C: 显示流程
    F->>D: 读取UTC时间: "06:00"
    F->>F: 转换为本地时间: 06:00 → 14:00
    F->>U: 显示本地时间: 14:00
```

## 📱 前端管理界面交互流程

```mermaid
stateDiagram-v2
    [*] --> 加载页面
    加载页面 --> 广告管理界面
    
    广告管理界面 --> 创建规则: 点击新增
    广告管理界面 --> 编辑规则: 点击编辑
    广告管理界面 --> 手动触发: 点击发送
    广告管理界面 --> 切换状态: 点击启停
    广告管理界面 --> 查看数据: 点击查看表
    
    创建规则 --> 填写表单
    编辑规则 --> 填写表单
    填写表单 --> 时区转换: 提交时间
    时区转换 --> API调用
    API调用 --> 刷新列表: 成功
    API调用 --> 显示错误: 失败
    
    手动触发 --> 确认对话框
    确认对话框 --> API调用: 确认
    确认对话框 --> 广告管理界面: 取消
    
    切换状态 --> API调用
    
    查看数据 --> 数据表模态框
    数据表模态框 --> 复制JSON: 点击复制
    数据表模态框 --> 刷新数据: 点击刷新
    数据表模态框 --> 广告管理界面: 关闭
    
    刷新列表 --> 广告管理界面
    显示错误 --> 广告管理界面
```


