# 广告投放功能说明文档

## 📋 目录
- [概述](#概述)
- [系统架构](#系统架构)
- [数据库设计](#数据库设计)
- [核心功能模块](#核心功能模块)
- [业务流程](#业务流程)
- [API接口](#api接口)
- [时间处理机制](#时间处理机制)
- [错误处理](#错误处理)
- [部署配置](#部署配置)

## 🎯 概述

广告投放功能是一个基于 Cloudflare Workers 的自动化广告管理系统，支持：

- ✅ **定时广告投放**：按设定时间自动发送广告到指定频道
- ✅ **多时间段投放**：同一广告可设置多个时间段独立投放
- ✅ **手动触发投放**：支持即时手动发送广告
- ✅ **智能替换机制**：自动删除旧广告，发送新广告
- ✅ **时区自动转换**：前端本地时间与后端UTC时间自动转换
- ✅ **完整管理界面**：Mini App 界面管理广告规则

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mini App UI   │ ←→ │ Cloudflare      │ ←→ │   Telegram      │
│   前端管理界面   │    │ Workers API     │    │     Bot API     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              ↓
                       ┌─────────────────┐
                       │  Cloudflare D1  │
                       │   数据库存储    │
                       └─────────────────┘
                              ↓
                       ┌─────────────────┐
                       │   Cron Trigger  │
                       │   定时任务      │
                       └─────────────────┘
```

### 组件说明

1. **Mini App UI**: Telegram 内置小程序界面，提供广告规则管理
2. **Cloudflare Workers**: 核心业务逻辑处理和API服务
3. **Cloudflare D1**: SQLite数据库，存储广告规则和发送记录
4. **Telegram Bot API**: 消息转发、删除、置顶等操作
5. **Cron Trigger**: 定时触发广告检查和发送

## 🗄️ 数据库设计

### 广告规则表 (ad_campaigns)

```sql
CREATE TABLE ad_campaigns (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                    -- 广告规则名称
  target_channel_id TEXT NOT NULL,       -- 目标频道ID
  source_channel_id TEXT DEFAULT '-1001549390517', -- 广告源频道ID
  source_message_id INTEGER NOT NULL,    -- 源消息ID
  start_date DATE NOT NULL,              -- 开始日期
  end_date DATE NOT NULL,                -- 结束日期
  frequency_days INTEGER NOT NULL,       -- 发布频率间隔天数（0=每天，1=隔1天...）
  publish_time TEXT NOT NULL,            -- 发布时间 (HH:MM, UTC)
  is_pin BOOLEAN DEFAULT FALSE,          -- 是否置顶
  is_active BOOLEAN DEFAULT TRUE,        -- 是否启用
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 广告发送记录表 (ad_posts)

```sql
CREATE TABLE ad_posts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  campaign_id INTEGER NOT NULL,         -- 关联的广告规则ID
  target_channel_id TEXT NOT NULL,      -- 目标频道ID
  source_message_id INTEGER NOT NULL,   -- 源消息ID
  sent_message_id INTEGER NOT NULL,     -- 发送后的消息ID
  sent_at TIMESTAMP NOT NULL,           -- 发送时间
  publish_time TEXT,                    -- 对应的设定时间（支持多时间段）
  is_deleted BOOLEAN DEFAULT FALSE,     -- 是否已删除
  deleted_at TIMESTAMP,                 -- 删除时间
  FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id)
);
```

### 关键索引

```sql
-- 性能优化索引
CREATE INDEX idx_ad_campaigns_active ON ad_campaigns (is_active, start_date, end_date);
CREATE INDEX idx_ad_posts_campaign_time ON ad_posts (campaign_id, publish_time, sent_at DESC);
```

## ⚙️ 核心功能模块

### 1. AdManager 类

位置：`src/handlers/adManager.js`

#### 主要方法：

- **`createCampaign(db, campaignData)`**: 创建广告规则
- **`checkAndExecuteScheduledAds(db)`**: 定时任务入口，检查并执行广告
- **`shouldSendAd(db, campaign)`**: 判断是否应该发送广告
- **`executeAdPost(db, campaign)`**: 执行广告发送
- **`deleteOldAd(db, campaign)`**: 删除旧广告
- **`forwardMessage/deleteMessage/pinMessage`**: Telegram API 操作

### 2. 前端管理界面

位置：`assets/miniapp/admin.js` + `assets/miniapp/admin.html`

#### 主要功能：

- **广告规则管理**: 创建、编辑、删除、启停广告规则
- **手动触发**: 立即发送广告
- **数据查看**: 查看规则表和发送记录表
- **统计信息**: 显示发送统计和活跃规则数
- **时区转换**: 自动处理本地时间与UTC时间转换

## 🔄 业务流程

### 定时广告投放流程

```mermaid
graph TD
    A[Cron 触发 每分钟] --> B[checkAndExecuteScheduledAds]
    B --> C[查询活跃广告规则]
    C --> D{遍历每个规则}
    D --> E[shouldSendAd 检查]
    E --> F{时间匹配?}
    F -->|否| D
    F -->|是| G{频率检查}
    G -->|未到时间| D
    G -->|可以发送| H[executeAdPost]
    H --> I[转发消息到目标频道]
    I --> J[删除旧广告]
    J --> K[记录发送记录]
    K --> L{需要置顶?}
    L -->|是| M[置顶消息]
    L -->|否| N[发送完成]
    M --> N
    N --> D
    D --> O[所有规则检查完成]
```

### 时间判断逻辑

```mermaid
graph TD
    A[获取当前UTC时间] --> B[解析规则设定时间]
    B --> C{小时匹配?}
    C -->|否| D[不发送]
    C -->|是| E[查询该时间段最后发送记录]
    E --> F{从未发送?}
    F -->|是| G[可以发送]
    F -->|否| H[计算天数差异]
    H --> I{daysDiff > frequency_days?}
    I -->|是| G
    I -->|否| D
```

### 多时间段支持逻辑

```mermaid
graph LR
    A[规则1: 09:00] --> B[查询09:00发送记录]
    C[规则2: 18:00] --> D[查询18:00发送记录]
    B --> E[独立频率判断]
    D --> F[独立频率判断]
    E --> G[09:00时段发送]
    F --> H[18:00时段发送]
```

## 🔌 API接口

### 广告规则管理

```javascript
// 创建广告规则
POST /api/ads
{
  "name": "早间推广",
  "target_channel_id": "-1001234567890",
  "source_message_id": 123,
  "start_date": "2025-01-20",
  "end_date": "2025-12-31", 
  "frequency_days": 0,
  "publish_time": "06:00", // UTC时间
  "is_pin": false
}

// 获取广告规则列表
GET /api/ads/campaigns

// 更新广告规则
PUT /api/ads/campaigns/{id}

// 删除广告规则  
DELETE /api/ads/campaigns/{id}

// 切换规则状态
PUT /api/ads/campaigns/{id}/toggle

// 手动触发广告
POST /api/ads/trigger/{id}
```

### 数据查看

```javascript
// 获取规则表原始数据
GET /api/ads/tables/campaigns

// 获取发送记录表原始数据  
GET /api/ads/tables/posts

// 获取统计信息
GET /api/ads/stats
{
  "totalPosts": 156,
  "todayPosts": 8, 
  "activeCampaigns": 5
}
```

## ⏰ 时间处理机制

### 时区转换策略

1. **前端输入**: 用户输入本地时间（如中国 14:00）
2. **前端转换**: JavaScript 自动转换为 UTC 时间（如 06:00）
3. **数据库存储**: 统一存储 UTC 时间
4. **后端执行**: Cloudflare Workers 按 UTC 时间执行
5. **前端显示**: 自动转换回本地时间显示

### 时间匹配逻辑

- **精度**: 小时级匹配（当前小时 = 设定小时）
- **容差**: 无容差设置，整个小时内都可触发
- **频率控制**: 基于 `publish_time` 字段的独立频率检查

### Cron 配置

```toml
# wrangler.toml
[triggers]
crons = ["* * * * *"]  # 每分钟检查（测试环境）
# crons = ["0,30 * * * *"]  # 每30分钟检查（生产环境推荐）
```

## 🚨 错误处理

### 1. 删除旧广告容错

```javascript
try {
  await this.deleteMessage(chatId, messageId);
  // 删除成功，标记为已删除
} catch (deleteError) {
  // 删除失败（消息可能已不存在），仍标记为已删除
  // 避免重复尝试删除
}
```

### 2. 发送失败处理

- 单个规则发送失败不影响其他规则
- 详细错误日志记录
- 自动跳过无效规则

### 3. 时间解析容错

- 无效时间格式自动跳过
- 数据库查询异常保护
- UTC 时间转换异常处理

## 🚀 部署配置

### 1. 数据库迁移

```bash
# 本地开发
npx wrangler d1 migrations apply telegram-db --local

# 生产部署
npx wrangler d1 migrations apply telegram-db --remote
```

### 2. 环境变量

```bash
# .dev.vars (本地开发)
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_BOT_USERNAME=your_bot_username
```

### 3. Workers 部署

```bash
# 部署到 Cloudflare Workers
npx wrangler deploy
```

## 📊 关键特性

### 1. 多时间段支持

- ✅ 同一广告内容可设置多个时间段
- ✅ 每个时间段独立计算发送频率
- ✅ 避免不同时间段相互干扰

### 2. 智能替换机制

- ✅ 发送新广告前自动删除旧广告
- ✅ 支持相同时间段的精确替换
- ✅ 删除失败容错处理

### 3. 时区自动处理

- ✅ 前端本地时间输入
- ✅ 后端 UTC 时间存储和执行
- ✅ 显示时自动转换回本地时间

### 4. 完整管理界面

- ✅ 规则的增删改查
- ✅ 手动触发功能
- ✅ 实时统计信息
- ✅ 数据表查看和导出

## 🔧 技术栈

- **Backend**: Cloudflare Workers + D1 Database
- **Frontend**: Vanilla JavaScript + Telegram Mini App
- **Database**: SQLite (Cloudflare D1)
- **Scheduling**: Cloudflare Workers Cron Triggers
- **API**: Telegram Bot API

