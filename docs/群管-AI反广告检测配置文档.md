# AI反广告检测配置文档

## 📋 概述

AI反广告检测系统使用AI API来智能识别群组中的广告和侮辱性内容。系统支持Monica和Gemini两个AI提供商，采用Monica优先、Gemini备用的策略。

## 🔧 环境变量配置

### 本地开发环境

在 `.dev.vars` 文件中添加以下环境变量：

```bash
# 本地开发环境变量（此文件不会被部署）
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_USERNAME=your_bot_username

# AI API 配置
MONICA_API_KEY=your_monica_api_key
GEMINI_API_KEY=your_gemini_api_key
```

### 生产环境

使用 wrangler 命令设置密钥：

```bash
# 设置Monica API密钥
wrangler secret put MONICA_API_KEY

# 设置Gemini API密钥  
wrangler secret put GEMINI_API_KEY
```

## 🤖 AI提供商配置

### 1. Monica AI

**获取API密钥：**
1. 访问 [Monica AI](https://openapi.monica.im/)
2. 注册账号并获取API密钥
3. 使用模型：`deepseek-chat`

**配置示例：**
```bash
MONICA_API_KEY=monica_your_api_key_here
```

### 2. Gemini AI

**获取API密钥：**
1. 访问 [Google AI Studio](https://ai.google.dev/)
2. 创建项目并获取API密钥
3. 使用模型：`gemini-2.0-flash`

**配置示例：**
```bash
GEMINI_API_KEY=gemini_your_api_key_here
```

## 🎯 检测策略

### API调用优先级

1. **优先使用Monica** - 超时时间：15秒
2. **Monica失败后使用Gemini** - 超时时间：10秒
3. **所有API失败** - 跳过AI检测，不影响主流程

### 群组类型和检测内容

#### 新群组（侮辱性内容检测）
- 群组ID：`-1001360670669`, `-1001107742293`, `-1001730968687`
- 检测内容：针对性的辱骂内容
- 触发阈值：相关度 ≥ 10
- 处理方式：直接通知MitsukiJoe并封禁用户

#### 特殊群组（沙雕英雄群）
- 群组ID：`-1001143091022`
- 检测内容：广告内容
- 触发阈值：相关度 ≥ 8
- 处理方式：转发给MitsukiJoe审核

#### 普通群组（广告检测）
- 其他群组
- 检测内容：各类广告和诈骗内容
- 触发阈值：相关度 ≥ 5
- 处理方式：转发到管理群进行人工审核

## 🚀 工作流程

### 1. 消息检测流程
```
收到消息 → 基础检测（垃圾消息+语言检测） → 立即返回200
     ↓
异步执行AI检测 → 如检测到问题 → 发送到相应审核群/用户
```

### 2. 异步处理优势
- ✅ 不阻塞主流程，避免Telegram重试
- ✅ AI检测在后台进行，不影响响应速度
- ✅ 超时控制，避免Worker执行时间限制
- ✅ 错误容忍，AI失败不影响其他功能

### 3. 管理员交互
AI检测到可疑内容后会发送带按钮的消息到管理群/管理员：
- 🔪 **确认违规** - 删除消息并封禁用户
- ☕️ **非违规内容** - 取消处理

## 🔍 调试和监控

### 日志级别
- `DEBUG` - AI API调用详情和检测过程
- `INFO` - 检测结果和处理动作
- `WARN` - API超时和非致命错误
- `ERROR` - API调用失败和系统错误

### 常见问题

**Q: AI API调用失败怎么办？**
A: 系统会自动尝试备用API，如果都失败则跳过AI检测，不影响其他功能。

**Q: 如何调整检测敏感度？**
A: 修改 `aiAntiAd.js` 中的触发阈值（如 `relevanceLevel >= 5`）。

**Q: 如何添加新的群组检测？**
A: 在 `GROUP_CONFIGS` 中添加群组ID和相应的检测策略。

**Q: API调用太慢怎么办？**
A: 调整 `AI_CONFIG` 中的超时时间，或者只使用一个更快的API提供商。

## 📊 性能指标

- **Monica API超时**：15秒
- **Gemini API超时**：10秒
- **总体处理时间**：不超过30秒（Worker限制）
- **主流程响应**：< 1秒（立即返回200）

## 🛡️ 安全注意事项

1. **API密钥安全**：
   - 生产环境必须使用 `wrangler secret`
   - 不要将密钥提交到代码仓库
   - 定期轮换API密钥

2. **权限控制**：
   - 管理员消息自动跳过检测
   - 只有授权用户可以操作审核按钮

3. **错误处理**：
   - 所有AI调用都有超时保护
   - 任何错误都不会影响主要功能
   - 敏感信息不会记录到日志中 