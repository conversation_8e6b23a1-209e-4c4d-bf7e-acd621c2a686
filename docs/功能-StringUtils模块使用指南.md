# StringUtils 模块使用指南

## 概述

`src/utils/stringUtils.js` 是项目中处理所有文本格式化、清理和安全处理的核心模块。它解决了UTF-8编码、emoji处理、特殊字符清理等问题。

## 核心原则

1. **安全第一** - 所有用户输入都应该经过安全处理
2. **UTF-8兼容** - 确保所有文本都能正确编码和传输
3. **Emoji友好** - 正确处理各种emoji和Unicode字符
4. **一致性** - 在整个项目中使用统一的文本处理方式

## 基础功能

### 文本安全处理

```javascript
import { safeString } from '../utils/stringUtils.js';

// 基础安全处理
const userInput = "用户输入的包含emoji的文本 🎉";
const safeText = safeString(userInput);
```

### 安全截断

```javascript
import { safeTruncate } from '../utils/stringUtils.js';

// 安全截断，不会在emoji中间截断
const longText = "很长的文本包含emoji 🚀🎯🎮";
const truncated = safeTruncate(longText, 10); // "很长的文本包..."
```

### 用户名处理

```javascript
import { safeUserDisplayName } from '../utils/stringUtils.js';

// 处理Telegram用户对象
const user = {
  first_name: "张三 🎭",
  last_name: "李四",
  username: "zhangsan123"
};
const displayName = safeUserDisplayName(user, 8); // "张三 🎭 李..."
```

### Caption文本处理

```javascript
import { safeCaptionText } from '../utils/stringUtils.js';

// 处理消息caption
const caption = "这是一段包含emoji的说明文字 🌟⭐";
const safeCaption = safeCaptionText(caption);
```

## 扩展功能

### 特殊标签清理

```javascript
import { removeSpecialTags } from '../utils/stringUtils.js';

// 移除特定标签
const text = "这是一段文字 #投稿 #测试";
const cleaned = removeSpecialTags(text, ['#投稿', '#测试']); // "这是一段文字"
```

### 时间格式化

```javascript
import { formatTimestamp } from '../utils/stringUtils.js';

const timestamp = 1703123456;
const formatted = formatTimestamp(timestamp, 'relative'); // "2小时前"
```

### 文件大小格式化

```javascript
import { formatFileSize } from '../utils/stringUtils.js';

const size = formatFileSize(1048576); // "1.0 MB"
```

## 在不同场景中的使用

### 1. 数据库存储前

```javascript
// 在 logSubMessage 函数中
const params = [
  safeString(originalMessage.from?.first_name || ''),
  safeString(originalMessage.from?.last_name || ''),
  safeCaptionText(originalMessage.caption || ''),
  // ... 其他参数
];
```

### 2. API请求参数

```javascript
// 在发送Telegram API请求前
const params = {
  chat_id: chatId,
  caption: safeCaptionText(caption),
  // ... 其他参数
};
```

### 3. 显示名称生成

```javascript
// 在 createCaptionWithSource 函数中
const userDisplayName = safeUserDisplayName(user, 6);
const channelName = safeChannelDisplayName(chat, signature, 10);
```

## 添加新功能的最佳实践

### 1. 遵循命名规范

```javascript
// ✅ 好的命名
export function safeEmailAddress(email) { ... }
export function formatPhoneNumber(phone) { ... }

// ❌ 避免的命名
export function cleanEmail(email) { ... }
export function phoneFormat(phone) { ... }
```

### 2. 保持一致的参数模式

```javascript
// ✅ 好的参数设计
export function safeCustomText(text, options = {}) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  // ... 处理逻辑
}
```

### 3. 添加完整的JSDoc注释

```javascript
/**
 * 你的新功能描述
 * @param {string} input 输入参数描述
 * @param {Object} options 选项对象描述
 * @param {number} options.maxLength 最大长度
 * @returns {string} 返回值描述
 */
export function yourNewFunction(input, options = {}) {
  // 实现
}
```

### 4. 在扩展区域添加

```javascript
// =============================================================================
// 扩展区域 - 在这里添加新的格式化规则和功能
// =============================================================================

/**
 * 你的新功能
 */
export function yourNewFunction() {
  // 实现
}
```

### 5. 更新默认导出

```javascript
export default {
  // ... 现有函数
  yourNewFunction  // 添加新函数
};
```

## 常见使用场景

### 处理转发消息

```javascript
import { safeChannelDisplayName, safeUserDisplayName } from '../utils/stringUtils.js';

// 处理转发来源
const channelName = safeChannelDisplayName(
  message.forward_from_chat, 
  message.forward_signature, 
  10
);

const userName = safeUserDisplayName(message.from, 6);
```

### 处理媒体组

```javascript
import { safeCaptionText, safeEntities } from '../utils/stringUtils.js';

// 处理媒体组caption
const safeCaption = safeCaptionText(message.caption);
const safeEntityList = safeEntities(
  message.caption_entities,
  originalCaption,
  safeCaption
);
```

### 处理用户输入

```javascript
import { sanitizeUsername, stripHtmlTags } from '../utils/stringUtils.js';

// 清理用户名
const cleanUsername = sanitizeUsername(userInput); // 移除特殊字符

// 清理HTML（如果接受web输入）
const plainText = stripHtmlTags(htmlInput);
```

## 测试建议

在添加新功能时，建议测试以下场景：

1. **空值处理** - `null`, `undefined`, `''`
2. **Emoji测试** - 各种复杂emoji组合
3. **特殊字符** - Unicode、控制字符等
4. **边界情况** - 超长文本、特殊字符组合
5. **编码测试** - 确保UTF-8兼容性

## 性能注意事项

1. **缓存结果** - 对于重复处理的文本，考虑缓存
2. **避免过度处理** - 只在必要时使用复杂的转换
3. **批量处理** - 对于大量文本，考虑批量处理优化

这个模块现在是项目文本处理的核心，所有新的格式化需求都应该在这里统一实现，确保整个项目的文本处理保持一致性和安全性。