# 群规推送功能说明

## 📋 概述

群规推送功能是Telegram Bot的核心功能之一，用于自动向新加入群组的成员发送群规链接，确保新成员了解并遵守群组规则。该功能支持多条群规配置、链接有效性验证、智能标题处理等特性。

## 🏗️ 系统架构

### 📁 核心文件结构

```
src/
├── utils/groupRulesUtils.js          # 群规核心工具函数
├── handlers/
│   ├── command/groupAdmin.js         # /rules命令处理
│   ├── memberManagement/
│   │   ├── index.js                  # 新成员处理入口
│   │   └── actions/welcomeActions.js # 欢迎消息和群规发送
├── miniappapi/handlers/groupConfig.js # Web管理界面API
└── assets/miniapp/group-config.html   # Web管理界面
```

### 🗄️ 数据库表结构

```sql
-- 群规配置表
CREATE TABLE group_rules (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chat_id TEXT NOT NULL,                  -- 群组ID
  rule_urls TEXT NOT NULL DEFAULT '[]',   -- 群规URL数组(JSON格式)
  set_by_user_id INTEGER NOT NULL,        -- 设置人用户ID
  set_by_user_name TEXT,                  -- 设置人姓名
  is_active BOOLEAN DEFAULT TRUE,         -- 是否启用
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**群规URL数据格式**：
```json
[
  {
    "url": "https://t.me/c/1143091022/1",
    "title": "群规",
    "canAccess": true,
    "accessError": null,
    "chatTitle": "示例群组"
  }
]
```

## ⚙️ 功能特性

### 🔧 核心功能

1. **多群规支持** - 每个群组可配置多条群规链接
2. **链接验证** - 自动验证群规链接的有效性和可访问性
3. **智能标题** - 自动处理群规标题显示逻辑
4. **自动推送** - 新成员加入时自动发送群规
5. **命令查询** - 支持`/rules`命令查看当前群规
6. **Web管理** - 提供可视化配置界面

### 🛡️ 安全特性

1. **权限验证** - 只有管理员可配置群规
2. **链接检查** - 验证Telegram链接格式和可访问性
3. **数据验证** - 严格的输入数据格式验证
4. **错误处理** - 完善的异常处理和日志记录

## 🚀 使用流程

### 📝 群规配置流程

1. **Web界面配置**
   - 访问群组配置管理界面
   - 选择目标群组
   - 添加群规链接和标题
   - 系统自动验证链接有效性
   - 保存配置

2. **数据存储**
   - 旧配置标记为`is_active = FALSE`
   - 新配置插入数据库
   - 包含验证结果和元数据

### 🎉 新成员欢迎流程

1. **触发条件**
   - 新成员加入群组
   - 通过真人验证（如果启用）

2. **执行逻辑**
   ```javascript
   // 获取群规配置
   const rulesConfig = await getGroupRules(env, chatId);
   
   // 过滤可访问的群规
   const accessibleRules = rulesConfig.rule_urls.filter(rule => 
       rule.canAccess !== false
   );
   
   // 构建欢迎消息
   const welcomeText = `[${userFirstName}](tg://user?id=${userId}) 欢迎加入
   请遵守群规: ${ruleLinks.join(' ')}`;
   
   // 发送消息并设置自动删除
   await sendMessage(welcomeText);
   await scheduleMessageDelete(messageId, 30); // 30秒后删除
   ```

### 📋 命令查询流程

**`/rules`命令**：
- 检查群组类型（仅群组可用）
- 获取当前群规配置
- 格式化显示信息
- 包含访问状态和设置信息

## 🔍 技术实现

### 📤 群规发送核心函数

```javascript
export async function sendRulesToNewMember(env, chatId, userId, userFirstName) {
    // 1. 获取群规配置
    const rulesConfig = await getGroupRules(env, chatId);
    
    // 2. 验证和过滤
    const accessibleRules = rulesConfig.rule_urls.filter(rule => 
        rule.canAccess !== false
    );
    
    // 3. 构建链接文本
    const ruleLinks = accessibleRules.map((rule, index) => {
        let title = rule.title;
        // 智能标题处理：多条默认标题时添加序号
        if (accessibleRules.length > 1 && 
            accessibleRules.every(r => r.title === '群规')) {
            title = `群规${index + 1}`;
        }
        return `[${title}](${rule.url})`;
    });
    
    // 4. 发送欢迎消息
    const welcomeText = `[${userFirstName}](tg://user?id=${userId}) 欢迎加入
    请遵守群规: ${ruleLinks.join(' ')}`;
    
    return await sendTelegramMessage(chatId, welcomeText);
}
```

### 🔗 链接验证机制

```javascript
export async function verifyMessageAccess(env, chatId, messageId) {
    try {
        // 尝试获取消息内容
        const response = await sendTelegramRequest(env, 
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`, {
                chat_id: env.TELEGRAM_BOT_TOKEN.split(':')[0], // Bot自己的ID
                from_chat_id: chatId,
                message_id: messageId,
                disable_notification: true
            }
        );
        
        return {
            canAccess: response.ok,
            error: response.ok ? null : response.description,
            chatInfo: response.result?.forward_from_chat
        };
    } catch (error) {
        return {
            canAccess: false,
            error: error.message
        };
    }
}
```

### 📊 智能标题处理

```javascript
// 标题显示逻辑
const ruleLinks = accessibleRules.map((rule, index) => {
    let title = rule.title;
    
    // 如果存在多条群规且标题都是默认的"群规"，则加上序号
    if (accessibleRules.length > 1 && 
        accessibleRules.every(r => r.title === '群规')) {
        title = `群规${index + 1}`;
    }
    
    return `[${title}](${rule.url})`;
});
```

## 🎯 配置管理

### 🌐 Web管理界面

**功能特性**：
- 群组选择下拉菜单
- 群规链接批量添加
- 实时链接验证
- 访问状态显示
- 配置保存和更新

**验证API**：
```javascript
// POST /api/group-config/verify-rules
{
    "rules": [
        {"url": "https://t.me/c/1143091022/1", "title": "群规"}
    ]
}

// 响应
{
    "success": true,
    "results": [
        {
            "index": 1,
            "url": "https://t.me/c/1143091022/1",
            "title": "群规",
            "status": "accessible",
            "chatInfo": {"title": "示例群组"}
        }
    ],
    "summary": {"total": 1, "accessible": 1, "failed": 0}
}
```

### 💾 数据管理

**保存逻辑**：
1. 验证所有群规链接
2. 标记旧配置为非活跃状态
3. 插入新配置记录
4. 返回验证结果和统计信息

**查询逻辑**：
```sql
SELECT * FROM group_rules 
WHERE chat_id = ? AND is_active = TRUE 
ORDER BY created_at DESC 
LIMIT 1
```

## 📈 性能优化

### 🗃️ 数据库索引

```sql
-- 群规查询优化
CREATE INDEX idx_group_rules_chat ON group_rules (chat_id, is_active);
CREATE INDEX idx_group_rules_setter ON group_rules (set_by_user_id);
```

### ⚡ 缓存策略

- **链接验证结果缓存** - 避免重复验证相同链接
- **群规配置缓存** - 减少数据库查询频率
- **消息自动删除** - 30秒后删除欢迎消息，保持群组整洁

## 🔧 故障处理

### 🚨 常见问题

1. **链接无法访问**
   - 自动跳过无效链接
   - 记录错误信息
   - 仅发送可访问的群规

2. **发送失败**
   - 详细错误日志
   - 返回失败原因
   - 不影响其他功能

3. **配置丢失**
   - 软删除机制（is_active标记）
   - 完整的操作日志
   - 支持配置恢复

### 📊 监控指标

- 群规配置成功率
- 链接验证通过率
- 新成员欢迎发送成功率
- 消息自动删除执行率

## 🎯 最佳实践

### ✅ 推荐做法

1. **群规链接格式**：使用`https://t.me/c/群组ID/消息ID`格式
2. **标题命名**：使用有意义的标题，避免全部使用"群规"
3. **定期检查**：定期验证群规链接的有效性
4. **权限管理**：仅授权可信管理员配置群规

### ❌ 避免事项

1. 不要使用外部链接作为群规
2. 不要配置过多群规（建议不超过5条）
3. 不要频繁修改群规配置
4. 不要在群规中包含敏感信息
