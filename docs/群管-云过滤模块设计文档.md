# 云过滤模块设计文档

## 📋 概述

云过滤模块是一个统一的消息检测系统，用于自动检测和处理Telegram群组中的垃圾消息、违规内容等。该模块重构了原有分散在`src/index.js`中的检测逻辑，提供了模块化、可扩展的架构设计。

## 🏗️ 架构设计

### 📁 模块结构

```
src/cloudFilter/
├── index.js                    # 主入口，统一检测流程
├── modules/                    # 检测模块
│   ├── languageDetector.js     # 语言检测模块
│   ├── spamDetector.js         # 多维度检测模块
│   └── aiDetector.js           # AI检测封装模块
└── actions/                    # 动作执行模块
    ├── actionExecutor.js       # 执行删除和封禁操作
    └── resultFormatter.js      # 格式化检测结果
```

### 🔄 检测流程

云过滤采用**三层检测架构**，按优先级依次执行：

1. **🈲 语言检测**（优先级最高）
   - 检测黑名单词汇比例
   - 性能最优，覆盖面广
   - 命中后：删除消息，不封禁用户

2. **🔍 多维度检测**（优先级中等）
   - 违法关键词、违禁词、引导行为等
   - 综合评分机制
   - 命中后：删除消息并封禁用户

3. **🤖 AI检测**（优先级最低）
   - 智能语义分析
   - 仅在前两层检测通过后执行
   - 异步处理，不阻塞主流程

## 🎯 两种工作模式

### 1. 自动模式 (auto)

**触发条件**：非管理员群员发送消息时自动触发

**执行逻辑**：
```javascript
// 权限检查
const shouldSkip = await isBotAdmin(botCtx) || await isGroupAdmin(botCtx);

if (!shouldSkip) {
    // 执行云过滤检测
    const filterResult = await executeCloudFilter(message, env, {
        mode: 'auto',
        executionContext: ctx
    });
    
    if (filterResult.shouldDelete) {
        // 立即执行删除和封禁操作
        await executeActions(filterResult, message, env, ctx);
    }
}
```

**特点**：
- ✅ 短路优化：检测到违规内容立即停止并执行操作
- ✅ 异步AI检测：不阻塞主流程
- ✅ 自动执行：无需人工干预

### 2. 命令模式 (command)

**触发条件**：管理员使用`/adCheck`命令

**执行逻辑**：
```javascript
// 执行完整检测
const filterResult = await executeCloudFilter(tempMessage, env, {
    mode: 'command'
});

// 格式化详细结果
const formattedResult = formatDetectionResult(
    filterResult, 
    isReplyMode, 
    message, 
    replyMessage
);
```

**特点**：
- ✅ 完整检测：执行所有检测模块，显示详细结果
- ✅ 同步AI检测：等待AI检测结果
- ✅ 仅显示结果：不执行删除和封禁操作

## 🔧 核心API

### executeCloudFilter()

**主要检测函数**

```javascript
/**
 * 执行云过滤检测
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {Object} options 选项
 * @param {string} options.mode 模式：'auto' | 'command'
 * @param {Object} options.executionContext Cloudflare Worker 执行上下文
 * @returns {Promise<Object>} 检测结果
 */
export async function executeCloudFilter(message, env, options = {})
```

**返回结果结构**：
```javascript
{
    shouldDelete: false,        // 是否应该删除消息
    shouldBan: false,          // 是否应该封禁用户
    detectionResults: [        // 各模块检测结果
        {
            module: 'language_detection',
            shouldDelete: false,
            reason: '...',
            details: {...}
        },
        // ...
    ],
    finalAction: null,         // 最终触发的动作类型
    errors: [],               // 检测过程中的错误
    executionTime: 123        // 检测耗时（毫秒）
}
```

### executeActions()

**动作执行函数**

```javascript
/**
 * 执行云过滤检测后的动作（删除消息、封禁用户等）
 * @param {Object} filterResult 云过滤检测结果
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {Object} executionContext Cloudflare Worker 执行上下文
 * @returns {Promise<Object>} 执行结果
 */
export async function executeActions(filterResult, message, env, executionContext)
```

### formatDetectionResult()

**结果格式化函数**

```javascript
/**
 * 格式化云过滤检测结果用于adCheck命令显示
 * @param {Object} filterResult 云过滤检测结果
 * @param {boolean} isReplyMode 是否为回复模式
 * @param {Object} originalMessage 原始消息对象
 * @param {Object} replyMessage 被回复的消息对象
 * @returns {string} 格式化后的结果文本
 */
export function formatDetectionResult(filterResult, isReplyMode, originalMessage, replyMessage)
```

## 📊 检测模块详解

### 🈲 语言检测模块

**文件**：`src/cloudFilter/modules/languageDetector.js`

**功能**：检测消息中黑名单词汇的比例

**判定标准**：
- 黑名单词汇比例 > 设定阈值
- 触发动作：删除消息（不封禁用户）

**返回结果**：
```javascript
{
    shouldDelete: boolean,
    reason: string,
    details: {
        totalBlacklistRatio: number,  // 黑名单比例
        // ... 其他详细信息
    }
}
```

### 🔍 多维度检测模块

**文件**：`src/cloudFilter/modules/spamDetector.js`

**功能**：综合评分机制检测垃圾消息

**评分标准**：
- 违法关键词：+100分（直接判定）
- 违禁词：+20分
- 表情密度>30%：+25分
- 引导私聊：+20分
- 引导加群：+15分
- 可疑用户名：+15分
- 引导符号：+10分
- 多个推广词：+10分

**判定标准**：
- 综合评分 ≥ 70分：判定为垃圾消息
- 触发动作：删除消息并封禁用户

**返回结果**：
```javascript
{
    isSpam: boolean,
    score: number,
    reasons: [string],
    details: {
        textLength: number,
        emojiCount: number,
        emojiDensity: number,
        highRisk: [string],        // 违法关键词
        prohibitedWords: [string], // 违禁词
        // ... 其他检测项目
    }
}
```

### 🤖 AI检测模块

**文件**：`src/cloudFilter/modules/aiDetector.js`

**功能**：使用AI进行智能语义分析

**特点**：
- 继承现有AI检测的超时和错误处理逻辑
- Monica API优先，Gemini API备用
- 支持同步/异步两种调用模式

**返回结果**：
```javascript
{
    success: boolean,
    hasContent: boolean,
    reason: string,
    error?: string,
    executionTime: number
}
```

## 🛡️ 权限管理

### 权限检查逻辑

```javascript
// 检查是否需要跳过检测
const shouldSkipDetection = await isBotAdmin(botCtx) || await isGroupAdmin(botCtx);
```

**跳过检测的用户**：
- Bot管理员（配置在 `BOT_ADMINS`）
- 群组管理员（通过Telegram API动态获取）
- 匿名管理员

### 权限缓存机制

- 使用内存缓存避免重复API调用
- 缓存时间：5分钟
- 自动清理过期缓存

## 📈 性能优化

### 检测顺序优化

1. **语言检测优先**：性能最优，命中率高
2. **多维度检测次之**：逻辑判断，速度较快  
3. **AI检测最后**：需要网络请求，耗时较长

### 短路机制

- **自动模式**：检测到违规内容立即停止并执行操作
- **命令模式**：执行完整检测，显示所有结果

### 异步处理

```javascript
// 自动模式：异步执行AI检测，不等待结果
if (mode === 'auto') {
    executionContext?.waitUntil(executeAiDetection(message, env));
} else {
    // 命令模式：同步执行AI检测并返回结果
    const aiResult = await executeAiDetection(message, env);
}
```

## 🚨 错误处理

### 错误分类

1. **预期错误**：AI API超时、网络失败等
2. **非预期错误**：代码逻辑错误等

### 处理策略

```javascript
try {
    // 检测逻辑
} catch (error) {
    Logger.error('云过滤检测出现未预期错误:', error);
    result.errors.push({
        module: 'cloud_filter',
        error: error.message
    });
    // 不抛出异常，避免影响消息处理
}
```

### Webhook设计原则

**关键原则**：即使处理失败也要返回200状态码

```javascript
// ✅ 正确做法
catch (error) {
    Logger.error('处理失败:', error);
    return new Response('OK - Error handled', { status: 200 });
}
```

## 📝 使用示例

### adCheck命令使用

**回复模式**：
```
/adCheck (回复一条消息)
```

**文本模式**：
```
/adCheck 要检测的文本内容
```

**返回结果示例**：
```
🔍 云过滤检测结果

👤 检测者: 管理员名称 🛡️ (管理员)
🎯 总体判定: ❌ 违规
⚡ 处理方式: 删除消息并封禁用户
🔍 触发原因: 垃圾消息（多维度检测评分过高）
⏱️ 检测耗时: 245ms

📊 各模块检测详情:
1. 🈲 语言检测: ✅ 正常

2. 🔍 多维度检测: ❌ 违规
   📊 评分: 85/100
   ⚠️ 违规原因:
      • 检测到违禁词汇
      • 表情符号密度过高
   📈 统计: 长度156字符, 表情23个, 密度35.2%

3. 🤖 AI检测: ✅ 完成
   📝 状态: AI检测已执行（异步处理）
   ⏱️ 耗时: 1240ms

📋 判定标准:
┏━ 🈲 语言检测
┃  • 黑名单词汇比例 > 阈值: 删除消息
┃  • 不封禁用户
┣━ 🔍 多维度检测
┃  • 违法关键词: +100分 直接判定
┃  • 违禁词: +20分
┃  • 综合评分≥70分: 判定为垃圾消息
┗━ 🤖 AI检测: 异步执行，用于辅助判断
```

## 🔧 配置说明

### 环境变量

- `TELEGRAM_BOT_TOKEN`: Telegram Bot Token
- `MONICA_API_KEY`: Monica AI API密钥（可选）
- `GEMINI_API_KEY`: Gemini AI API密钥（可选）

### 常量配置

**Bot管理员**：`src/config/constants.js`
```javascript
export const BOT_ADMINS = [96728357, 其他ID];
```

**检测阈值**：在各模块中配置具体的检测标准和阈值

## 🚀 扩展指南

### 添加新的检测模块

1. 在 `src/cloudFilter/modules/` 创建新模块
2. 实现检测函数，返回标准格式结果
3. 在 `src/cloudFilter/index.js` 中集成
4. 在 `resultFormatter.js` 中添加格式化逻辑

### 添加新的动作类型

1. 在 `actionExecutor.js` 中添加新的动作逻辑
2. 更新结果格式化函数
3. 更新文档说明

## 📋 未来优化方向

1. **配置管理**：支持动态调整检测阈值和参数
2. **缓存策略**：实现智能缓存提升性能
3. **AI检测增强**：返回更详细的AI检测结果
4. **统计分析**：添加检测效果统计和分析
5. **规则引擎**：支持更灵活的检测规则配置

## 📚 相关文档

- [权限管理模块使用指南](./Guide-permissions.md)
- [AI API配置说明](./AI-API-Configuration.md)
- [日志系统使用指南](./Guide-logger-usage.md)

---

*本文档记录了云过滤模块的完整实现，如有疑问请查阅相关代码或联系开发者。* 