// #region 📦 依赖导入
import Logger from '../utils/logger.js';
import { detectLanguageSpam } from './modules/languageDetector.js';
import { detectSpamMessage } from './modules/spamDetector.js';
import { executeAiDetection } from './modules/aiDetector.js';
import { executeActions } from './actions/actionExecutor.js';
import { formatDetectionResult } from './actions/resultFormatter.js';
// #endregion 📦 依赖导入

// #region 🔍 主检测函数

/**
 * 执行云过滤检测
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {Object} options 选项
 * @param {string} options.mode 模式：'auto' | 'command'
 * @param {Object} options.executionContext Cloudflare Worker 执行上下文
 * @returns {Promise<Object>} 检测结果
 */
export async function executeCloudFilter(message, env, options = {}) {
	const {
		mode = 'auto',           // 'auto' | 'command'
		executionContext = null   // Cloudflare Worker 执行上下文
	} = options;
	
	const startTime = Date.now();
	
	// 初始化结果对象
	const result = {
		shouldDelete: false,
		shouldBan: false,
		detectionResults: [],
		finalAction: null,
		errors: [],
		executionTime: 0
	};
	
	Logger.debug(`🏷️ 开始云过滤检测`, {
		mode,
		chatId: message.chat.id,
		messageId: message.message_id,
		userId: message.from?.id
	});
	
	try {
		// 1. 语言检测（优先执行，性能更好）
		Logger.debug('🈲 执行语言检测');
		const languageResult = detectLanguageSpam(message, { 
			forceCheck: mode === 'command' 
		});
		result.detectionResults.push({
			module: 'language_detection',
			...languageResult
		});
		
		if (languageResult.shouldDelete) {
			result.shouldDelete = true;
			result.shouldBan = languageResult.shouldBan || false;
			
			// 根据触发原因设置不同的 finalAction
			if (languageResult.reason.includes('用户名')) {
				result.finalAction = 'username_ban';
			} else if (languageResult.shouldBan) {
				result.finalAction = 'content_arabic_ban';
			} else {
				result.finalAction = 'language_violation';
			}
			
			// 根据不同情况记录不同的日志
			if (languageResult.reason.includes('用户名')) {
				Logger.info('🚫 用户名检测命中，准备删除消息并封禁用户', {
					reason: languageResult.reason,
					details: languageResult.details,
					shouldBan: result.shouldBan
				});
			} else if (languageResult.shouldBan) {
				Logger.info('🚫 消息内容阿拉伯语检测命中，准备删除消息并封禁用户', {
					reason: languageResult.reason,
					details: languageResult.details,
					shouldBan: result.shouldBan
				});
			} else {
				Logger.info('🈲 消息内容语言检测命中，准备删除消息', {
					reason: languageResult.reason,
					details: languageResult.details,
					shouldBan: result.shouldBan
				});
			}
			
			if (mode === 'auto') {
				result.executionTime = Date.now() - startTime;
				Logger.debug(`🏷️ 云过滤检测完成（短路），耗时: ${result.executionTime}ms`);
				return result; // 短路返回
			}
		}
		
		// 2. 多维度检测（在语言检测之后）
		Logger.debug('🔍 执行多维度检测');
		const spamResult = detectSpamMessage(message);
		result.detectionResults.push({
			module: 'spam_detection',
			...spamResult
		});
		
		if (!result.shouldDelete && spamResult.isSpam) {
			result.shouldDelete = true;
			result.shouldBan = true;
			result.finalAction = 'spam_detected';
			Logger.warn('🔍 多维度检测命中，准备删除消息并封禁用户', {
				score: spamResult.score,
				reasons: spamResult.reasons
			});
			
			if (mode === 'auto') {
				result.executionTime = Date.now() - startTime;
				Logger.debug(`🏷️ 云过滤检测完成（短路），耗时: ${result.executionTime}ms`);
				return result; // 短路返回
			}
		}
		
		// 3. AI检测处理
		if (!result.shouldDelete) {
			if (mode === 'auto') {
				// 自动模式：异步执行AI检测，不等待结果
				Logger.debug('🤖 前置检测通过，启动AI检测（异步）');
				executionContext?.waitUntil(executeAiDetection(message, env));
			} else {
				// 命令模式：同步执行AI检测并返回结果
				Logger.debug('🤖 执行AI检测（同步）');
				const aiResult = await executeAiDetection(message, env, { 
					forceCheck: true 
				});
				result.detectionResults.push({
					module: 'ai_detection',
					...aiResult
				});
			}
		} else if (mode === 'command') {
			// 命令模式下即使前面检测已命中，也要显示AI检测的结果
			Logger.debug('🤖 执行AI检测（显示结果）');
			const aiResult = await executeAiDetection(message, env, { 
				forceCheck: true 
			});
			result.detectionResults.push({
				module: 'ai_detection',
				...aiResult
			});
		}
		
	} catch (error) {
		Logger.error('云过滤检测出现未预期错误:', error);
		// 对于不应该出现的错误，记录但不影响消息处理
		result.errors.push({
			module: 'cloud_filter',
			error: error.message
		});
	}
	
	result.executionTime = Date.now() - startTime;
	Logger.debug(`🏷️ 云过滤检测完成，耗时: ${result.executionTime}ms`, {
		shouldDelete: result.shouldDelete,
		shouldBan: result.shouldBan,
		finalAction: result.finalAction,
		detectionCount: result.detectionResults.length
	});
	
	return result;
}
// #endregion 🔍 主检测函数

// #region 📤 导出函数
export { executeActions, formatDetectionResult };
// #endregion 📤 导出函数 