// #region 📦 依赖导入
import Logger from '../../utils/logger.js';
// #endregion 📦 依赖导入

// #region 🔧 违禁关键词配置

/**
 * 违禁关键词列表
 */
const BANNED_KEYWORDS = {
	// 极高风险（单独命中即封禁）
	high_risk: [
		// 违法色情内容
		'小傌',
		'小&马',
		'小马视频',
		'精品純幼',
		'精品純呦',
		'精品呦',
		'点我看u',
		'点我看幼',
		'小学生视',
		'U女',
		'u小女',
		'U稀缺',
		'稀缺小',
		'呦呦uu',
		'UU破',
		'最新呦',
		'呦女看',
		'出原创幼',
		'同城🉑🈷️',
		'@xquu1',
		'@xsm77788',
		'@qkucnfhz',
		'@TONGY',
		'@uun120',
		'@wzyoyo',
		'@wzyoyo1',
		'@solspinusbot',
		'@jinpai',
		'@jisuqf',
		'@jqs18',
		'@fjqy3',
		'@PostBot',
		'@AUUMYTIANbot',
		'@Rizne52',
		// 其他恶性关键词
		'必安空投福利派送',
		'全网最高汇率',
		'trx闪兑',
		'币安担保',
		'空投奖池',
		'感恩回馈',
		'招加群手',
		'能量官方源头活动',
		'TRX能量',
		'能量闪租',
		'转账能量',
		'全网底价',
		'汇盈',
		'发财网',
		'亚太同城',
		'同城担保',
		'起点担保',
		'百万现金',
		'兼职线上',
		'线上小时工',
		// 违禁URL
		'marketwebb.red',
		'hy886.vip',
		'nodepay.com',
	],

	// 违禁词 - 中风险
	prohibited_words: [
		'人獸',
		'兽交',
		'童颜',
		'未成年',
		'小学生',
		'中学生',
		'高中生',
		'处女',
		'母子',
		'父女',
		'乱伦',
		'迷姦',
		'强姦',
		'USDT',
		'TRX',
		'泰铢',
		'币种',
		'钱庄',
		'赌场',
		'投注',
		'日入',
		'押金',
	],

	// 引导私聊词语 - 中风险
	private_contact: ['pm', '私聊', '私信', '私', '联系我', '加我', '找我', '咨询', '详情', '了解'],

	// 引导加群词语 - 中风险
	group_invitation: ['进群', '加群', '入群', '群组', '资源群', '福利群', '精品群', '群里', '群内'],

	// 广告推广词语 - 低风险
	promotion: ['资源', '精品', '福利', '免费', '优惠', '特价', '限时', '活动', '推广', '广告', 'VIP'],
};

/**
 * 可疑用户名模式
 */
const SUSPICIOUS_USERNAME_PATTERNS = [
	/^[a-z]{2,4}\d{4,8}$/i, // 如: abc1234, xyz88888
	/^[a-z]\d{6,10}$/i, // 如: a123456, x7777777
	/^\w{3,6}\d{2,5}$/i, // 如: user123, name88
];
// #endregion 🔧 违禁关键词配置

// #region 🔍 多维度检测函数

/**
 * 检测消息是否为恶性广告
 * @param {Object} message Telegram消息对象
 * @returns {Object} 检测结果 {isSpam: boolean, score: number, reasons: string[]}
 */
export function detectSpamMessage(message) {
	const result = {
		isSpam: false,
		score: 0,
		reasons: [],
		details: {},
	};

	// 收集所有文本内容（包括用户姓名和引用内容）
	const textContents = [
		message.text,
		message.caption,
		message.forward_from?.username ? `@${message.forward_from.username}` : '',
		message.forward_from_chat?.username ? `@${message.forward_from_chat.username}` : '',
		// 添加发送者的姓名检测
		message.from?.first_name || '',
		message.from?.last_name || '',
		// 添加引用内容检测
		message.quote?.text || '',
		// 如果是回复消息，也检测回复目标的引用内容
		message.reply_to_message?.quote?.text || '',
		// 检测外部回复内容
		message.external_reply?.quote?.text || '',
		message.reply_to_message?.external_reply?.quote?.text || '',
		// 检测外部回复的频道/群组信息
		message.external_reply?.chat?.title || '',
		message.external_reply?.chat?.username ? `@${message.external_reply.chat.username}` : '',
		message.external_reply?.origin?.chat?.title || '',
		message.external_reply?.origin?.chat?.username ? `@${message.external_reply.origin.chat.username}` : '',
		// 检测回复消息中的外部回复频道/群组信息
		message.reply_to_message?.external_reply?.chat?.title || '',
		message.reply_to_message?.external_reply?.chat?.username ? `@${message.reply_to_message.external_reply.chat.username}` : '',
		message.reply_to_message?.external_reply?.origin?.chat?.title || '',
		message.reply_to_message?.external_reply?.origin?.chat?.username
			? `@${message.reply_to_message.external_reply.origin.chat.username}`
			: '',
	]
		.filter(Boolean)
		.join(' ');

	// 即使没有文本内容，也要检测发送者用户名
	const hasTextContent = textContents.trim().length > 0;

	if (!hasTextContent && !message.from?.username) {
		return result; // 既没有文本内容，也没有发送者用户名，不检测
	}

	const lowerText = textContents.toLowerCase();

	// 预先计算文本统计信息（所有检测都需要）
	const emojiCount = hasTextContent
		? (
				textContents.match(
					/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu
				) || []
		  ).length
		: 0;
	const textLength = hasTextContent ? textContents.replace(/\s/g, '').length : 0;

	// 设置基础统计信息
	result.details.textLength = textLength;
	result.details.emojiCount = emojiCount;

	// 只有在有文本内容时才进行文本相关检测
	if (hasTextContent) {
		// 1. 极高风险关键词检测（单独命中即封禁）
		const highRiskMatches = BANNED_KEYWORDS.high_risk.filter((keyword) => lowerText.includes(keyword));

		if (highRiskMatches.length > 0) {
			result.isSpam = true;
			result.score += 100; // 直接满分
			result.reasons.push(`包含违法关键词: ${highRiskMatches.join(', ')}`);
			result.details.highRisk = highRiskMatches;
			result.details.totalScore = result.score;
			return result; // 直接返回，不需要继续检测
		}

		// 2. 违禁词检测（中风险）
		const prohibitedMatches = BANNED_KEYWORDS.prohibited_words.filter((keyword) => lowerText.includes(keyword));

		if (prohibitedMatches.length > 0) {
			result.score += 20; // 中风险等级，与引导私聊相同
			result.reasons.push(`包含违禁词: ${prohibitedMatches.join(', ')}`);
			result.details.prohibitedWords = prohibitedMatches;
		}

		// 3. 表情符号密度检测
		const emojiDensity = textLength > 0 ? emojiCount / textLength : 0;

		if (emojiDensity > 0.3) {
			// 表情符号占比超过30%
			result.score += 25;
			result.reasons.push(`表情符号密度过高: ${(emojiDensity * 100).toFixed(1)}%`);
			result.details.emojiDensity = emojiDensity;
		}

		// 4. 私聊引导检测
		const privateContactMatches = BANNED_KEYWORDS.private_contact.filter((keyword) => lowerText.includes(keyword));

		if (privateContactMatches.length > 0) {
			result.score += 20;
			result.reasons.push(`包含引导私聊词语: ${privateContactMatches.join(', ')}`);
			result.details.privateContact = privateContactMatches;
		}

		// 5. 加群引导检测
		const groupInviteMatches = BANNED_KEYWORDS.group_invitation.filter((keyword) => lowerText.includes(keyword));

		if (groupInviteMatches.length > 0) {
			result.score += 15;
			result.reasons.push(`包含加群引导词语: ${groupInviteMatches.join(', ')}`);
			result.details.groupInvite = groupInviteMatches;
		}
	}

	// 6. 可疑用户名检测
	const textSuspiciousUsernames = [];
	if (hasTextContent) {
		const usernames = textContents.match(/@\w+/g) || [];
		textSuspiciousUsernames.push(
			...usernames.filter((username) => {
				const cleanUsername = username.replace('@', '');
				return SUSPICIOUS_USERNAME_PATTERNS.some((pattern) => pattern.test(cleanUsername));
			})
		);
	}

	// 同时检测消息发送者的用户名
	const senderSuspiciousUsernames = [];
	if (message.from?.username) {
		const senderUsername = message.from.username;
		if (SUSPICIOUS_USERNAME_PATTERNS.some((pattern) => pattern.test(senderUsername))) {
			senderSuspiciousUsernames.push(`@${senderUsername}`);
		}
	}

	// 合并所有可疑用户名
	const allSuspiciousUsernames = [...textSuspiciousUsernames, ...senderSuspiciousUsernames];

	if (allSuspiciousUsernames.length > 0) {
		result.score += 15;
		result.reasons.push(`包含可疑用户名: ${allSuspiciousUsernames.join(', ')}`);
		result.details.suspiciousUsernames = allSuspiciousUsernames;
	}

	// 只有在有文本内容时才进行以下检测
	if (hasTextContent) {
		// 7. 特殊符号检测
		const specialSymbols = textContents.match(/[↓↑→←⬇⬆➡⬅]{2,}/g) || [];
		if (specialSymbols.length > 0) {
			result.score += 10;
			result.reasons.push(`包含引导符号: ${specialSymbols.join(', ')}`);
			result.details.specialSymbols = specialSymbols;
		}

		// 8. 广告推广词语检测
		const promotionMatches = BANNED_KEYWORDS.promotion.filter((keyword) => lowerText.includes(keyword));

		if (promotionMatches.length >= 2) {
			// 需要至少2个推广词语
			result.score += 10;
			result.reasons.push(`包含多个推广词语: ${promotionMatches.join(', ')}`);
			result.details.promotion = promotionMatches;
		}
	}

	// 9. 综合评分判断
	if (result.score >= 70) {
		result.isSpam = true;
	}

	result.details.totalScore = result.score;

	return result;
}

/**
 * 记录检测结果（用于调试和优化）
 * @param {Object} message 消息对象
 * @param {Object} detectionResult 检测结果
 */
export function logDetectionResult(message, detectionResult) {
	if (detectionResult.isSpam) {
		Logger.warn('🚨 检测到垃圾消息', {
			chatId: message.chat.id,
			messageId: message.message_id,
			userId: message.from?.id,
			username: message.from?.username,
			score: detectionResult.score,
			reasons: detectionResult.reasons,
			details: detectionResult.details,
			content: (message.text || message.caption || '').substring(0, 200),
		});
	} else if (detectionResult.score > 0) {
		Logger.debug('可疑消息但未达到封禁阈值', {
			chatId: message.chat.id,
			messageId: message.message_id,
			score: detectionResult.score,
			reasons: detectionResult.reasons,
		});
	}
}
// #endregion 🔍 多维度检测函数
