// #region 📦 依赖导入
import Logger from '../../utils/logger.js';
// #endregion 📦 依赖导入

// #region 🔧 语言检测配置

/**
 * 语言检测规则配置
 */
const LANGUAGE_PATTERNS = {
	zh: {
		regex: /[\u4E00-\u9FA5\u2E80-\u2EFF\u31C0-\u31EF\u2F00-\u2FDF]/g,
		name: '中文',
		isBlacklisted: false
	},
	jp: {
		regex: /[\u3040-\u309F\u30A0-\u30FF\u31F0-\u31FF]/g,
		name: '日文',
		isBlacklisted: false
	},
	ko: {
		regex: /[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F]/g,
		name: '韩文',
		isBlacklisted: true
	},
	en: {
		regex: /[A-Za-z]/g,
		name: '英文',
		isBlacklisted: false
	},
	ru: {
		regex: /[а-яА-ЯЁё]/g,
		name: '俄文',
		isBlacklisted: true
	},
	el: {
		regex: /[\u0370-\u03FF]/g,
		name: '希腊文',
		isBlacklisted: false
	},
	ar: {
		regex: /[\u0600-\u06ff]/g,
		name: '阿拉伯文',
		isBlacklisted: true
	},
	sa: {
		regex: /[\u0F00-\u0FFF]/g,
		name: '梵文(藏文)',
		isBlacklisted: true
	},
	th: {
		regex: /[\u0E00-\u0E7F]/g,
		name: '泰文',
		isBlacklisted: true
	},
	num: {
		regex: /[0-9]/g,
		name: '数字',
		isBlacklisted: false
	},
	symbols: {
		regex: /[\$\uFFE5\^\+=`~<>{}\[\]|\u3000-\u303F!-#%-\x2A,-/:;\x3F@\x5B-\x5D_\x7B}\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E3B\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/g,
		name: '符号',
		isBlacklisted: false
	}
};

/**
 * 语言检测配置
 */
const DETECTION_CONFIG = {
	// 黑名单语言占比阈值
	blacklistThreshold: 0.3, // 30%
	// 单个黑名单语言占比阈值
	singleLanguageThreshold: 0.3, // 30%
	// 最小文本长度（字符数）
	minTextLength: 1,
	// 跳过检测的用户ID列表（机器人管理员等）
	skipUserIds: [777000], // Telegram 频道推送用户ID
};
// #endregion 🔧 语言检测配置

// #region 🈲 语言检测函数

/**
 * 检测消息语言并判断是否需要删除
 * @param {Object} message Telegram消息对象
 * @param {Object} options 选项
 * @param {boolean} options.forceCheck 强制检测，忽略回复消息跳过规则（用于/adCheck命令）
 * @returns {Object} 检测结果 {shouldDelete: boolean, reason: string, details: Object}
 */
export function detectLanguageSpam(message, options = {}) {
	const { forceCheck = false } = options;
	
	const result = {
		shouldDelete: false,
		shouldBan: false,
		reason: '',
		details: {
			languages: {},
			totalBlacklistRatio: 0,
			textLength: 0,
			highestBlacklistLanguage: null
		}
	};

	// 跳过检测的情况（除非是强制检测模式）
	if (!forceCheck && shouldSkipLanguageDetection(message)) {
		result.reason = 'skipped';
		return result;
	}

	// 优先检测用户名中的阿拉伯文和泰文（直接封禁）
	const usernameCheckResult = checkUsernameForBanLanguages(message);
	if (usernameCheckResult.shouldBan) {
		result.shouldDelete = true;
		result.shouldBan = true;
		result.reason = usernameCheckResult.reason;
		result.details = usernameCheckResult.details;
		Logger.warn('🚫 检测到用户名包含禁止语言，直接封禁', {
			chatId: message.chat.id,
			messageId: message.message_id,
			userId: message.from?.id,
			username: message.from?.username,
			firstName: message.from?.first_name,
			lastName: message.from?.last_name,
			reason: result.reason
		});
		return result;
	}

	// 提取文本内容
	const textContent = extractTextContent(message);
	if (!textContent || textContent.length < DETECTION_CONFIG.minTextLength) {
		result.reason = 'text_too_short';
		return result;
	}

	// 去除空格的纯文本（用于计算占比）
	const cleanText = textContent.replace(/\s/g, '');
	result.details.textLength = cleanText.length;

	// 检测各种语言
	let totalBlacklistMatches = 0;
	let highestBlacklistRatio = 0;
	let highestBlacklistLanguage = null;

	for (const [langCode, langConfig] of Object.entries(LANGUAGE_PATTERNS)) {
		const matches = cleanText.match(langConfig.regex) || [];
		const matchCount = matches.length;
		const ratio = cleanText.length > 0 ? matchCount / cleanText.length : 0;

		if (matchCount > 0) {
			result.details.languages[langCode] = {
				name: langConfig.name,
				matches: matchCount,
				ratio: ratio,
				isBlacklisted: langConfig.isBlacklisted
			};

			// 累计黑名单语言占比
			if (langConfig.isBlacklisted) {
				totalBlacklistMatches += matchCount;
				
				// 记录占比最高的黑名单语言
				if (ratio > highestBlacklistRatio) {
					highestBlacklistRatio = ratio;
					highestBlacklistLanguage = {
						code: langCode,
						name: langConfig.name,
						ratio: ratio
					};
				}
			}
		}
	}

	// 计算总黑名单语言占比
	const totalBlacklistRatio = cleanText.length > 0 ? totalBlacklistMatches / cleanText.length : 0;
	result.details.totalBlacklistRatio = totalBlacklistRatio;
	result.details.highestBlacklistLanguage = highestBlacklistLanguage;

	// 判断是否需要删除
	// 规则1: 总黑名单语言占比超过阈值
	if (totalBlacklistRatio > DETECTION_CONFIG.blacklistThreshold) {
		result.shouldDelete = true;
		result.reason = `黑名单语言总占比过高: ${(totalBlacklistRatio * 100).toFixed(1)}%`;
		// 检查是否包含阿拉伯语，如果是则封禁
		if (result.details.languages.ar && result.details.languages.ar.ratio > DETECTION_CONFIG.singleLanguageThreshold) {
			result.shouldBan = true;
			result.reason = `阿拉伯文占比过高: ${(result.details.languages.ar.ratio * 100).toFixed(1)}%`;
		}
		return result;
	}

	// 规则2: 单个黑名单语言占比超过阈值
	if (highestBlacklistRatio > DETECTION_CONFIG.singleLanguageThreshold) {
		result.shouldDelete = true;
		result.reason = `${highestBlacklistLanguage.name}占比过高: ${(highestBlacklistRatio * 100).toFixed(1)}%`;
		// 如果是阿拉伯语超过阈值，则封禁
		if (highestBlacklistLanguage.code === 'ar') {
			result.shouldBan = true;
		}
		return result;
	}

	return result;
}

/**
 * 检测用户名中的阿拉伯文和泰文（直接封禁语言）
 * @param {Object} message 消息对象
 * @returns {Object} 检测结果 {shouldBan: boolean, reason: string, details: Object}
 */
function checkUsernameForBanLanguages(message) {
	const result = {
		shouldBan: false,
		reason: '',
		details: {
			languages: {},
			totalBanLanguageRatio: 0,
			textLength: 0,
			checkedText: ''
		}
	};

	// 提取用户名文本（姓名）
	const nameParts = [];
	if (message.from?.first_name) {
		nameParts.push(message.from.first_name);
	}
	if (message.from?.last_name) {
		nameParts.push(message.from.last_name);
	}

	const nameText = nameParts.join(' ').trim();
	if (!nameText) {
		result.reason = 'no_name';
		return result;
	}

	// 去除空格的纯文本（用于计算占比）
	const cleanText = nameText.replace(/\s/g, '');
	result.details.textLength = cleanText.length;
	result.details.checkedText = nameText;

	// 只检测阿拉伯文和泰文
	const banLanguages = ['ar', 'th'];
	let totalBanMatches = 0;
	let highestBanRatio = 0;
	let highestBanLanguage = null;

	for (const langCode of banLanguages) {
		const langConfig = LANGUAGE_PATTERNS[langCode];
		const matches = cleanText.match(langConfig.regex) || [];
		const matchCount = matches.length;
		const ratio = cleanText.length > 0 ? matchCount / cleanText.length : 0;

		if (matchCount > 0) {
			result.details.languages[langCode] = {
				name: langConfig.name,
				matches: matchCount,
				ratio: ratio,
				isBlacklisted: langConfig.isBlacklisted
			};

			totalBanMatches += matchCount;
			
			// 记录占比最高的封禁语言
			if (ratio > highestBanRatio) {
				highestBanRatio = ratio;
				highestBanLanguage = {
					code: langCode,
					name: langConfig.name,
					ratio: ratio
				};
			}
		}
	}

	// 计算总封禁语言占比
	const totalBanRatio = cleanText.length > 0 ? totalBanMatches / cleanText.length : 0;
	result.details.totalBanLanguageRatio = totalBanRatio;

	// 判断是否需要封禁（阿拉伯文或泰文超过30%）
	if (highestBanRatio > DETECTION_CONFIG.singleLanguageThreshold) {
		result.shouldBan = true;
		result.reason = `用户名${highestBanLanguage.name}占比过高: ${(highestBanRatio * 100).toFixed(1)}%`;
	}

	return result;
}

/**
 * 判断是否应该跳过语言检测
 * @param {Object} message 消息对象
 * @returns {boolean} 是否跳过
 */
function shouldSkipLanguageDetection(message) {
	// 跳过机器人消息
	if (message.from && message.from.is_bot) {
		return true;
	}

	// 跳过特定用户ID（如频道推送）
	if (message.from && DETECTION_CONFIG.skipUserIds.includes(message.from.id)) {
		return true;
	}

	return false;
}

/**
 * 提取消息中的文本内容
 * @param {Object} message 消息对象
 * @returns {string} 文本内容
 */
function extractTextContent(message) {
	const textParts = [];

	// 提取消息文本
	if (message.text) {
		textParts.push(message.text);
	}

	// 提取媒体说明文字
	if (message.caption) {
		textParts.push(message.caption);
	}

	// 特殊处理：频道消息的标题检测
	if (message.from && message.from.username === 'Channel_Bot' && 
		message.sender_chat && message.sender_chat.type === 'channel' &&
		message.sender_chat.title) {
		textParts.push(message.sender_chat.title);
	}

	return textParts.join(' ').trim();
}

/**
 * 记录语言检测结果
 * @param {Object} message 消息对象
 * @param {Object} detectionResult 检测结果
 */
export function logLanguageDetectionResult(message, detectionResult) {
	if (detectionResult.shouldDelete) {
		Logger.warn('🈲 检测到违规语言消息', {
			chatId: message.chat.id,
			messageId: message.message_id,
			userId: message.from?.id,
			username: message.from?.username,
			reason: detectionResult.reason,
			textLength: detectionResult.details.textLength,
			totalBlacklistRatio: (detectionResult.details.totalBlacklistRatio * 100).toFixed(1) + '%',
			languages: detectionResult.details.languages,
			content: extractTextContent(message).substring(0, 200)
		});
	} else if (detectionResult.details.totalBlacklistRatio > 0) {
		Logger.debug('语言检测：包含黑名单语言但未达到删除阈值', {
			chatId: message.chat.id,
			messageId: message.message_id,
			reason: detectionResult.reason,
			totalBlacklistRatio: (detectionResult.details.totalBlacklistRatio * 100).toFixed(1) + '%',
			languages: Object.fromEntries(
				Object.entries(detectionResult.details.languages).filter(([_, lang]) => lang.isBlacklisted)
			)
		});
	}
}

/**
 * 更新语言检测配置
 * @param {Object} newConfig 新的配置选项
 */
export function updateLanguageDetectionConfig(newConfig) {
	Object.assign(DETECTION_CONFIG, newConfig);
	Logger.info('语言检测配置已更新', DETECTION_CONFIG);
}
// #endregion 🈲 语言检测函数 