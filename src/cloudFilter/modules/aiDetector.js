// #region 📦 依赖导入
import Logger from '../../utils/logger.js';
import { aiAntiAdCheck } from '../../handlers/aiAntiAd.js';
// #endregion 📦 依赖导入

// #region 🔧 AI 配置
// 群组类型配置
const GROUP_CONFIGS = {
	// 新群组：检测侮辱性内容
	newGroups: [-1001360670669, -1001107742293, -1001730968687],
	// 特殊群组：沙雕英雄群
	specialGroup: -1001143091022,
	// 管理群ID
	adminChatId: -1002008890111,
	// MitsukiJoe用户ID
	mitsukiJoeId: 96728357
};

// AI API 配置
const AI_CONFIG = {
	monicaTimeout: 15000,    // Monica API 超时时间（15秒）
	geminiTimeout: 10000,    // Gemini API 超时时间（10秒）
	maxRetries: 1            // 最大重试次数
};
// #endregion 🔧 AI 配置

// #region 🤖 AI API 调用函数

/**
 * 调用 Monica API 进行内容检测
 */
async function callMonica(prompt, env, timeout = AI_CONFIG.monicaTimeout) {
	if (!env.MONICA_API_KEY) {
		Logger.warn('Monica API Key 未配置，跳过Monica检测');
		return null;
	}

	const controller = new AbortController();
	const timeoutId = setTimeout(() => controller.abort(), timeout);

	try {
		Logger.debug('开始调用 Monica API');
		
		const response = await fetch('https://openapi.monica.im/v1/chat/completions', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${env.MONICA_API_KEY}`
			},
			body: JSON.stringify({
				model: 'deepseek-chat',
				messages: [{
					role: 'user',
					content: [{
						type: 'text',
						text: prompt
					}]
				}]
			}),
			signal: controller.signal
		});

		clearTimeout(timeoutId);

		if (!response.ok) {
			throw new Error(`Monica API 响应错误: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		
		if (data.choices && data.choices[0] && data.choices[0].message) {
			const result = data.choices[0].message.content;
			Logger.success('Monica API 调用成功', { responseLength: result.length });
			return result;
		} else {
			throw new Error('Monica API 响应格式无效');
		}

	} catch (error) {
		clearTimeout(timeoutId);
		
		if (error.name === 'AbortError') {
			Logger.warn(`Monica API 调用超时 (${timeout}ms)`);
		} else {
			Logger.error('Monica API 调用失败', error);
		}
		
		return null;
	}
}

/**
 * 调用 Gemini API 进行内容检测
 */
async function callGemini(prompt, env, timeout = AI_CONFIG.geminiTimeout) {
	if (!env.GEMINI_API_KEY) {
		Logger.warn('Gemini API Key 未配置，跳过Gemini检测');
		return null;
	}

	const controller = new AbortController();
	const timeoutId = setTimeout(() => controller.abort(), timeout);

	try {
		Logger.debug('开始调用 Gemini API');
		
		const response = await fetch(
			`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${env.GEMINI_API_KEY}`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					safetySettings: [
						{ category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
						{ category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
						{ category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
						{ category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' }
					],
					contents: [{
						role: 'user',
						parts: [{ text: prompt }]
					}]
				}),
				signal: controller.signal
			}
		);

		clearTimeout(timeoutId);

		if (!response.ok) {
			throw new Error(`Gemini API 响应错误: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		
		if (data.candidates && data.candidates[0] && data.candidates[0].content) {
			const result = data.candidates[0].content.parts[0].text;
			Logger.success('Gemini API 调用成功', { responseLength: result.length });
			return result;
		} else {
			throw new Error('Gemini API 响应格式无效');
		}

	} catch (error) {
		clearTimeout(timeoutId);
		
		if (error.name === 'AbortError') {
			Logger.warn(`Gemini API 调用超时 (${timeout}ms)`);
		} else {
			Logger.error('Gemini API 调用失败', error);
		}
		
		return null;
	}
}

/**
 * 智能AI调用：Monica优先，失败后尝试Gemini
 */
async function callAIWithFallback(prompt, env) {
	Logger.debug('开始AI检测，Monica优先策略');
	
	// 优先尝试Monica
	let result = await callMonica(prompt, env);
	
	if (result) {
		Logger.debug('Monica API 调用成功，返回结果');
		return result;
	}
	
	// Monica失败，尝试Gemini
	Logger.debug('Monica API 失败，尝试Gemini API');
	result = await callGemini(prompt, env);
	
	if (result) {
		Logger.debug('Gemini API 调用成功，返回结果');
		return result;
	}
	
	Logger.warn('所有AI API都调用失败');
	return null;
}
// #endregion 🤖 AI API 调用函数

// #region 📝 提示词与响应处理

/**
 * 生成AI检测提示词
 */
function generatePrompt(message, chatId) {
	const { from, forward_origin = {}, text, caption } = message;
	const { type, sender_user = {}, chat = {} } = forward_origin;
	
	// 获取文本内容
	const textContent = text || caption || '';
	
	// 判断群组类型
	const isNewGroup = GROUP_CONFIGS.newGroups.includes(chatId);
	
	// 构建转发信息
	const forwardedFromUser = type === 'user' ? ` 转发自用户 ${sender_user.first_name}` : '';
	const forwardedFromChat = type === 'channel' ? ` 转发自聊天 ${chat.title}` : '';
	
	// 构建基础信息
	let prompt = `用户 ${from?.first_name || '未知用户'} `;
	prompt += `${forwardedFromUser || ''}`;
	prompt += `${forwardedFromChat || ''}`;
	prompt += `\n的信息: ${textContent}`;
	
	if (isNewGroup) {
		// 新群组：检测侮辱性内容
		prompt += '\n判断以上的用户和TA发送的内容是否包含针对性的辱骂内容，只要直接关联的相关性，不要"可能"相关的那种';
		prompt += '\n相关度用0~10数字表示,只回答相关性数字!!!只有相关度大于0的情况下再给出理由';
	} else {
		// 原有群组：检测广告内容
		prompt += '\n判断以上的用户和TA发送的内容在聊天群内出现是否属于[';
		prompt += '虚拟币或者假币广告/法轮功/博彩广告/诱导他人看自己头像或者名字加联系方式进行的特殊诈骗/';
		prompt += '全新引流方式【TG频道评论】全行业都可以引/这是我的XXweb3钱包助记词退圈了送给有缘人';
		prompt += ']相关内容';
		prompt += '\n不进行什么"在绝大多数平台"之类的检测，不检测性质上属于别的什么';
		prompt += '\n不管危害性等不相关的事情！';
		prompt += '\n相关度用0~10数字表示,只回答相关性数字!!!只有相关度大于0的情况下再给出理由';
	}
	
	return prompt;
}

/**
 * 解析AI响应结果
 */
function parseAIResponse(aiResponse) {
	if (!aiResponse || typeof aiResponse !== 'string') {
		return null;
	}

	// 匹配数字开头的响应格式
	const match = aiResponse.match(/^(\d+)\s*(.*)/s);
	if (match) {
		return {
			level: parseInt(match[1], 10),
			reason: match[2].trim()
		};
	}

	return null;
}
// #endregion 📝 提示词与响应处理

// #region 🕵️‍♂️ AI 检测核心

/**
 * 为命令模式执行AI检测（不执行任何操作，只返回结果）
 */
async function executeAiDetectionForCommand(message, env, startTime) {
	try {
		const chatId = message.chat.id;
		const textContent = message.text || message.caption;
		if (!textContent || !textContent.trim()) {
			return {
				success: true,
				hasContent: false,
				reason: '消息无文本内容',
				executionTime: Date.now() - startTime
			};
		}

		// 生成提示词
		const prompt = generatePrompt(message, chatId);
		Logger.debug('生成AI检测提示词', { promptLength: prompt.length });

		// 调用AI API
		const aiResponse = await callAIWithFallback(prompt, env);
		if (!aiResponse) {
			throw new Error('所有AI API都调用失败');
		}

		// 解析AI响应
		const parsed = parseAIResponse(aiResponse);
		if (!parsed) {
			Logger.warn('AI响应解析失败', { aiResponse: aiResponse.substring(0, 100) });
			throw new Error('AI响应解析失败');
		}

		const { level: relevanceLevel, reason } = parsed;
		const contentType = GROUP_CONFIGS.newGroups.includes(chatId) ? '侮辱性内容' : '广告';

		Logger.info('🤖 AI分析完成（命令模式）', {
			chatId,
			relevanceLevel,
			contentType,
			reason: reason.substring(0, 50)
		});

		return {
			success: true,
			hasContent: true,
			reason: `AI判定为"${contentType}"的相关度为 ${relevanceLevel}/10`,
			isViolation: relevanceLevel >= 5, // 综合阈值定为5
			confidence: relevanceLevel,
			details: reason || '无',
			contentType,
			executionTime: Date.now() - startTime
		};

	} catch (error) {
		Logger.error('命令模式AI检测失败:', error);
		return {
			success: false,
			hasContent: !!(message.text || message.caption),
			error: error.message,
			reason: 'AI检测服务暂时不可用',
			executionTime: Date.now() - startTime
		};
	}
}

/**
 * 执行AI检测（云过滤版本）
 */
export async function executeAiDetection(message, env, options = {}) {
	const { forceCheck = false } = options;
	const startTime = Date.now();
	
	try {
		Logger.debug('🤖 开始AI检测（云过滤版本）', {
			chatId: message.chat.id,
			messageId: message.message_id,
			userId: message.from?.id
		});
		
		const textContent = message.text || message.caption;
		if (!textContent || !textContent.trim()) {
			return {
				success: true,
				hasContent: false,
				reason: '消息无文本内容',
				executionTime: Date.now() - startTime
			};
		}
		
		// 命令模式：执行完整的AI检测逻辑并返回结果
		if (forceCheck) {
			return await executeAiDetectionForCommand(message, env, startTime);
		}
		
		// 自动模式：调用现有的AI检测逻辑（包含权限检查和后续操作）
		// 这个函数是异步执行的，不会阻塞主流程
		await aiAntiAdCheck(message, env);
		
		Logger.info('🤖 AI检测（自动模式）已启动', {
			chatId: message.chat.id,
			messageId: message.message_id,
			executionTime: Date.now() - startTime
		});
		
		return {
			success: true,
			hasContent: true,
			reason: 'AI检测已执行（异步处理）',
			executionTime: Date.now() - startTime
		};
		
	} catch (error) {
		Logger.error('AI检测分发失败:', error);
		return {
			success: false,
			hasContent: !!(message.text || message.caption),
			error: error.message,
			reason: 'AI检测服务启动失败',
			executionTime: Date.now() - startTime
		};
	}
}
// #endregion 🕵️‍♂️ AI 检测核心 