// #region 📦 依赖导入
import Logger from '../../utils/logger.js';
// #endregion 📦 依赖导入

// #region 📋 结果格式化函数

/**
 * 格式化云过滤检测结果用于adCheck命令显示
 * @param {Object} filterResult 云过滤检测结果
 * @param {boolean} isReplyMode 是否为回复模式
 * @param {Object} originalMessage 原始消息对象（用于获取发送者信息）
 * @param {Object} replyMessage 被回复的消息对象（如果是回复模式）
 * @returns {string} 格式化后的结果文本
 */
export function formatDetectionResult(filterResult, isReplyMode = false, originalMessage = null, replyMessage = null) {
	let resultText = '🔍 <b>云过滤检测结果</b>\n\n';
	
	// 显示检测者信息
	if (originalMessage && originalMessage.from) {
		resultText += `👤 <b>检测者</b>: <code>${originalMessage.from.first_name}${originalMessage.from.last_name ? ' ' + originalMessage.from.last_name : ''}</code> 🛡️ (管理员)\n`;
	}
	
	// 如果是回复模式，显示被检测消息的信息
	if (isReplyMode && replyMessage) {
		const messageSourceText = buildMessageSourceText(replyMessage);
		resultText += messageSourceText;
	}
	
	// 显示总体判定结果
	resultText += `🎯 <b>总体判定</b>: ${filterResult.shouldDelete ? '❌ 违规' : '✅ 正常'}\n`;
	if (filterResult.shouldDelete) {
		resultText += `⚡ <b>处理方式</b>: ${filterResult.shouldBan ? '删除消息并封禁用户' : '仅删除消息'}\n`;
		resultText += `🔍 <b>触发原因</b>: ${getActionDescription(filterResult.finalAction)}\n`;
	}
	resultText += `⏱️ <b>检测耗时</b>: ${filterResult.executionTime}ms\n\n`;
	
	// 显示各模块检测结果
	resultText += '📊 <b>各模块检测详情</b>:\n';
	
	filterResult.detectionResults.forEach((detection, index) => {
		resultText += formatModuleResult(detection, index + 1);
	});
	
	// 显示判定标准说明
	resultText += buildJudgmentCriteria();
	
	// 如果有错误信息，显示错误
	if (filterResult.errors.length > 0) {
		resultText += '\n❌ <b>检测过程中的错误</b>:\n';
		filterResult.errors.forEach((error, index) => {
			resultText += `${index + 1}. ${error.module}: ${error.error}\n`;
		});
	}
	
	return resultText;
}

/**
 * 构建消息来源信息文本
 * @param {Object} message 消息对象
 * @returns {string} 来源信息文本
 */
function buildMessageSourceText(message) {
	const from = message.from;
	let sourceText = '';
	
	if (message.forward_from) {
		// 转发自用户
		const forwardFrom = message.forward_from;
		sourceText = `🔄 <b>转发消息来源</b>: <code>${forwardFrom.first_name}${forwardFrom.last_name ? ' ' + forwardFrom.last_name : ''}</code> (ID: ${forwardFrom.id})\n`;
		sourceText += `📤 <b>转发者</b>: <code>${from ? (from.first_name + (from.last_name ? ' ' + from.last_name : '')) : '未知用户'}</code>\n`;
	} else if (message.forward_from_chat) {
		// 转发自频道/群组
		const forwardFromChat = message.forward_from_chat;
		sourceText = `🔄 <b>转发消息来源</b>: <code>${forwardFromChat.title}</code> (${forwardFromChat.type})\n`;
		sourceText += `📤 <b>转发者</b>: <code>${from ? (from.first_name + (from.last_name ? ' ' + from.last_name : '')) : '未知用户'}</code>\n`;
	} else {
		// 普通消息
		sourceText = `🎯 <b>被检测消息来自</b>: <code>${from ? (from.first_name + (from.last_name ? ' ' + from.last_name : '')) : '未知用户'}</code>\n`;
	}
	
	return sourceText;
}

/**
 * 格式化单个模块的检测结果
 * @param {Object} detection 检测结果
 * @param {number} index 模块序号
 * @returns {string} 格式化后的模块结果
 */
function formatModuleResult(detection, index) {
	let moduleText = '';
	const moduleName = getModuleName(detection.module);
	
	switch (detection.module) {
		case 'language_detection':
			moduleText += `${index}. 🈲 <b>${moduleName}</b>: ${detection.shouldDelete ? '❌ 违规' : '✅ 正常'}\n`;
			if (detection.shouldDelete) {
				const details = ` 📊 黑名单比例: ${(detection.details.totalBlacklistRatio * 100).toFixed(1)}%\n` +
				                ` 🔍 详情: ${detection.reason}\n`;
				moduleText += `<blockquote expandable>${details}</blockquote>`;
			}
			break;
			
		case 'spam_detection':
			moduleText += `${index}. 🔍 <b>${moduleName}</b>: ${detection.isSpam ? '❌ 违规' : '✅ 正常'}\n`;
			if (detection.score > 0) {
				let details = ` 📊 评分: ${detection.score}/100\n`;
				if (detection.isSpam && detection.reasons.length > 0) {
					details += ` ⚠️ 违规原因:\n`;
					detection.reasons.forEach((reason, i) => {
						let sanitizedReason = reason;
						if (detection.details.highRisk && detection.details.highRisk.length > 0) {
							detection.details.highRisk.forEach(keyword => {
								const sanitized = keyword.replace(/./g, (char, index) => index === 0 ? char : '*');
								sanitizedReason = sanitizedReason.replace(new RegExp(keyword, 'gi'), sanitized);
							});
						}
						details += `    • ${sanitizedReason}\n`;
					});
					
					if (detection.details) {
						details += ` 📈 统计: 长度${detection.details.textLength}字符, 表情${detection.details.emojiCount}个`;
						if (detection.details.emojiDensity) {
							details += `, 密度${(detection.details.emojiDensity * 100).toFixed(1)}%`;
						}
						details += '\n';
					}
				}
				moduleText += `<blockquote expandable>${details}</blockquote>`;
			}
			break;
			
		case 'ai_detection':
			moduleText += `${index}. 🤖 <b>${moduleName}</b>: ${detection.success ? '✅ 完成' : '❌ 失败'}\n`;
			if (detection.success) {
				let details = '';
				if (detection.confidence !== undefined) {
					details += ` ⚖️ 判定: ${detection.isViolation ? '❌ 违规' : '✅ 正常'}\n`;
					details += ` 🎯 类型: ${detection.contentType}\n`;
					details += ` 💯 可疑度: ${detection.confidence}/10\n`;
					if (detection.details) {
						details += ` 📝 理由: ${detection.details}\n`;
					}
				} else {
					details += ` 📝 状态: ${detection.reason}\n`;
				}
				if (!detection.hasContent) {
					details += ` ℹ️ 说明: 无文本内容，跳过AI检测\n`;
				}
				details += ` ⏱️ 耗时: ${detection.executionTime}ms\n`;
				moduleText += `<blockquote expandable>${details}</blockquote>`;
			} else {
				const errorDetails = ` ❌ 错误: ${detection.reason}\n` +
				                     ` ⏱️ 耗时: ${detection.executionTime}ms\n`;
				moduleText += `<blockquote expandable>${errorDetails}</blockquote>`;
			}
			break;
			
		default:
			moduleText += `${index}. ❓ <b>${moduleName}</b>: 未知模块\n`;
	}
	
	moduleText += '\n';
	return moduleText;
}

/**
 * 获取模块显示名称
 * @param {string} moduleKey 模块键名
 * @returns {string} 显示名称
 */
function getModuleName(moduleKey) {
	const moduleNames = {
		'language_detection': '语言检测',
		'spam_detection': '多维度检测',
		'ai_detection': 'AI检测'
	};
	return moduleNames[moduleKey] || moduleKey;
}

/**
 * 获取动作描述
 * @param {string} action 动作类型
 * @returns {string} 动作描述
 */
function getActionDescription(action) {
	const actionDescriptions = {
		'language_violation': '语言违规（黑名单词汇比例过高）',
		'spam_detected': '垃圾消息（多维度检测评分过高）'
	};
	return actionDescriptions[action] || action;
}

/**
 * 构建判定标准说明
 * @returns {string} 判定标准文本
 */
function buildJudgmentCriteria() {
	const criteria = `┏━ 🈲 <b>语言检测</b>
┃  • 黑名单词汇比例 > 阈值: 删除消息
┃  • 不封禁用户
┣━ 🔍 <b>多维度检测</b>
┃  • 违法关键词: +100分 直接判定
┃  • 违禁词: +20分
┃  • 综合评分≥70分: 判定为垃圾消息
┃  • 表情密度>30%: +25分
┃  • 引导私聊: +20分
┃  • 引导加群: +15分
┃  • 可疑用户名: +15分
┃  • 引导符号: +10分
┃  • 多个推广词: +10分
┗━ 🤖 <b>AI检测</b>: 异步执行，用于辅助判断`;

	return `📋 <b>判定标准</b><blockquote expandable>${criteria}</blockquote>`;
}
// #endregion 📋 结果格式化函数 