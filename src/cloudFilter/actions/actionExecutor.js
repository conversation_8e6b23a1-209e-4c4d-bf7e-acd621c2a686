// #region 📦 依赖导入
import Logger from '../../utils/logger.js';
import { sendTelegramRequest } from '../../utils/telegramApi.js';
// #endregion 📦 依赖导入

// #region 🎬 执行动作函数

/**
 * 执行云过滤检测后的动作（删除消息、封禁用户等）
 * @param {Object} filterResult 云过滤检测结果
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {Object} executionContext Cloudflare Worker 执行上下文
 * @returns {Promise<Object>} 执行结果
 */
export async function executeActions(filterResult, message, env, executionContext) {
	if (!filterResult.shouldDelete) {
		Logger.debug('无需执行删除操作');
		return { success: true, actions: [] };
	}
	
	const chatId = message.chat.id;
	const userId = message.from?.id;
	const messageId = message.message_id;
	const actions = [];
	
	Logger.warn('🚨 执行云过滤动作', {
		chatId,
		messageId,
		userId,
		shouldBan: filterResult.shouldBan,
		finalAction: filterResult.finalAction
	});
	
	try {
		const promises = [];
		
		// 删除消息（所有检测都会删除消息）
		Logger.debug('删除违规消息');
		const deletePromise = sendTelegramRequest(
			env,
			`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
			{
				chat_id: chatId,
				message_id: messageId
			}
		).then(() => {
			actions.push('delete_message');
			Logger.success('消息删除成功');
		}).catch(error => {
			Logger.error('删除消息失败:', error);
			actions.push('delete_message_failed');
		});
		
		promises.push(deletePromise);
		
		// 根据检测结果决定是否封禁用户
		if (filterResult.shouldBan && chatId < 0 && userId) {
			Logger.debug('封禁违规用户');
			const banPromise = sendTelegramRequest(
				env,
				`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/banChatMember`,
				{
					chat_id: chatId,
					user_id: userId
					// 不设置 until_date 参数，表示永久封禁
				}
			).then(() => {
				actions.push('ban_user');
				Logger.success('用户封禁成功');
			}).catch(error => {
				Logger.error('封禁用户失败:', error);
				actions.push('ban_user_failed');
			});
			
			promises.push(banPromise);
		}
		
		// 等待所有操作完成
		if (executionContext) {
			executionContext.waitUntil(Promise.all(promises));
		} else {
			await Promise.all(promises);
		}
		
		// 构建结果消息
		const actionDescription = filterResult.shouldBan ? '删除消息并封禁用户' : '删除消息';
		const resultMessage = `✅ 已处理违规消息：${actionDescription}`;
		
		Logger.info(resultMessage, {
			chatId,
			messageId,
			userId,
			finalAction: filterResult.finalAction,
			actions
		});
		
		return {
			success: true,
			actions,
			message: resultMessage,
			responseText: filterResult.shouldBan ? 'OK - Spam message handled' : 'OK - Language violation handled'
		};
		
	} catch (error) {
		Logger.error('执行云过滤动作时出错:', error);
		return {
			success: false,
			actions,
			error: error.message,
			responseText: 'OK - Error handled'
		};
	}
}
// #endregion 🎬 执行动作函数 