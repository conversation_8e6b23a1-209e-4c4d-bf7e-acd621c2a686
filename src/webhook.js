// #region 📦 依赖导入
import { mediaGroupLog } from './handlers/mediaGroupLog.js';
import { forwardMedia } from './handlers/forwardMedia.js';
import { queueMediaGroupForward, processAllPendingQueues, cleanupOldQueueRecords } from './handlers/mediaGroupQueue.js';
import { handleCallbackQuery } from './handlers/callbackQuery.js';
import { handleAIAntiAdCallback } from './handlers/aiAntiAd.js';
import { 
	getForwardedSubmissionRecord, 
	deleteForwardedSubmissionRecord, 
	deleteMessage,
	hasMedia
} from './handlers/forwardHandler.js';
import { getMediaGroupForwardedRecords } from './utils/mediaGroupUtils.js';
import { DefaultGroupsBind, TELEGRAM_OFFICIAL_ID, TargetGroups } from './config/constants.js';
import Logger from './utils/logger.js';
import { getMessageType, isUpdateExpired } from './utils/messageUtils.js';
import { sendTelegramRequest } from './utils/telegramApi.js';
import { unpinOfficialMessage, deleteForwardedAdMessage } from './handlers/groupManagement.js';
import { handleNewMember } from './handlers/memberManagement/index.js';
import { isDuplicateJoin } from './utils/memberJoinDeduplicator.js';
import { getGroupConfig } from './utils/groupConfigUtils.js';
// #endregion 📦 依赖导入

/**
 * 处理Telegram的Webhook请求
 * @param {Object} update 已经解析的Telegram更新对象
 * @param {Object} env 环境变量
 * @param {ExecutionContext} ctx 执行上下文
 */
export async function handleWebhook(update, env, ctx) {
	try {
		// #region 🏁 请求预处理与日志
		// 记录完整的 update 信息用于调试
		Logger.debug('🚀 收到Telegram Webhook请求:', update);
		
		// 提取关键信息便于快速识别
		if (update.message) {
			const msg = update.message;
			Logger.debug(`消息摘要: 群组ID=${msg.chat.id}, 用户ID=${msg.from?.id}, 消息ID=${msg.message_id}, 类型=${getMessageType(msg)}, 文本="${msg.text?.substring(0, 50) || ''}"`);
		} else if (update.edited_message) {
			const msg = update.edited_message;
			Logger.debug(`编辑消息摘要: 群组ID=${msg.chat.id}, 用户ID=${msg.from?.id}, 消息ID=${msg.message_id}, 类型=${getMessageType(msg)}, 文本="${msg.text?.substring(0, 50) || ''}"`);
		} else if (update.callback_query) {
			Logger.debug(`回调查询: 用户ID=${update.callback_query.from.id}, 数据="${update.callback_query.data}"`);
		} else {
			Logger.debug('其他类型的更新:', Object.keys(update));
		}

		// 检查更新是否过期
		if (isUpdateExpired(update)) {
			Logger.log('丢弃过期更新:', JSON.stringify(update).substring(0, 200) + '...');
			return new Response('OK - Expired update ignored', { status: 200 });
		}
		// #endregion 🏁 请求预处理与日志

		// #region 🚫 成员变动消息删除
		// 删除所有成员变动的系统提示消息
		if (update.message) {
			const message = update.message;
			let shouldDelete = false;
			
			// 检测成员变动类型
			if (message.new_chat_members && message.new_chat_members.length > 0) {
				// 新成员加入
				shouldDelete = true;
				Logger.debug('🗑️ 检测到新成员加入消息，准备删除系统提示');
			} else if (message.left_chat_member) {
				// 成员离开/被踢出
				shouldDelete = true;
				Logger.debug('🗑️ 检测到成员离开消息，准备删除系统提示');
			}
			
			// 删除成员变动系统消息
			if (shouldDelete) {
				try {
					await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
						chat_id: message.chat.id,
						message_id: message.message_id
					});
					Logger.debug('✅ 成员变动系统消息已删除:', {
						chatId: message.chat.id,
						messageId: message.message_id
					});
				} catch (error) {
					// 对于删除消息失败，使用更低的日志级别，因为这不是关键错误
					if (error.message?.includes('Network connection lost') ||
						error.message?.includes('timeout') ||
						error.retryable) {
						// 网络错误，静默处理
						Logger.debug('🌐 删除成员变动消息时网络暂时不可用，已忽略');
					} else {
						Logger.debug('⚠️ 删除成员变动消息失败:', error.message || error.description);
					}
					// 删除失败不影响后续处理，继续执行
				}
			}
		}
		
		// 检测 chat_member 变动（管理员操作等）
		if (update.chat_member) {
			Logger.debug('🗑️ 检测到成员状态变动，无需删除消息（chat_member类型）');
		}
		// #endregion 🚫 成员变动消息删除

		// #region 🔳 回调查询处理
		// 检查是否包含回调查询（处理按钮点击）
		if (update.callback_query) {
			// 检查是否是AI反广告相关的回调
			if (update.callback_query.data && update.callback_query.data.startsWith('AIantiAD:')) {
				await handleAIAntiAdCallback(update.callback_query, env);
				return new Response('OK - AI anti-ad callback handled', { status: 200 });
			}
			
			return await handleCallbackQuery(update.callback_query, env, ctx);
		}
		// #endregion 🔳 回调查询处理

		// #region 👥 成员状态变动处理 (chat_member 事件)
		// chat_member 事件是所有新成员验证的主入口（在大群组中更可靠）
		// message.new_chat_members 在人数过多的群组中可能不会发送
		if (update.chat_member) {
			const chatMember = update.chat_member;
			const oldStatus = chatMember.old_chat_member.status;
			const newStatus = chatMember.new_chat_member.status;
			const user = chatMember.new_chat_member.user;
			const chatId = chatMember.chat.id.toString();

			Logger.debug('🔄 检测到成员状态变动:', {
				userId: user.id,
				userName: user.first_name,
				chatId,
				oldStatus,
				newStatus
			});

			// 检测新成员加入：从 'left'/'kicked' 变为 'member' 或 is_member 从 false 变为 true
			const oldIsMember = chatMember.old_chat_member.is_member || false;
			const newIsMember = chatMember.new_chat_member.is_member || false;
			
			if (!user.is_bot && (
				((oldStatus === 'left' || oldStatus === 'kicked') && newStatus === 'member') || 
				(!oldIsMember && newIsMember))) {
				
				// 检查是否为重复请求（5秒内）
				if (isDuplicateJoin(user.id.toString(), chatId)) {
					Logger.debug('🔄 检测到重复的新成员加入请求，跳过处理:', {
						userId: user.id,
						userName: user.first_name,
						chatId
					});
					return new Response('OK - Duplicate join request ignored', { status: 200 });
				}
				
				Logger.info('🆕 通过chat_member事件检测到新成员加入:', {
					userId: user.id,
					userName: user.first_name,
					chatId,
					oldStatus,
					newStatus,
					oldIsMember,
					newIsMember,
					triggerSource: 'chat_member_event'  // 添加触发源标识
				});
				ctx.waitUntil(handleNewMember(user, chatId, env, ctx));
				return new Response('OK - Chat member join handled', { status: 200 });
			}

			// 其他状态变动暂不处理
			return new Response('OK - Chat member status change ignored', { status: 200 });
		}
		// #endregion 👥 成员状态变动处理

		// 检查是否包含消息或编辑消息
		let message = null;
		let isEditedMessage = false;

		if (update.message) {
			message = update.message;
			isEditedMessage = false;
		} else if (update.edited_message) {
			message = update.edited_message;
			isEditedMessage = true;
			Logger.debug('🔄 处理编辑消息事件');
		} else {
			return new Response('OK', { status: 200 });
		}

		const text = message.text || '';

		// #region 👥 新成员消息删除 (message 事件)
		// message.new_chat_members 事件只用于删除系统提示消息
		// 验证逻辑统一由 chat_member 事件处理（在大群组中更可靠）
		if (message.new_chat_members && message.new_chat_members.length > 0) {
			Logger.debug('🗑️ 检测到新成员入群消息，已在前面删除系统提示');
			// 不处理验证逻辑，只让消息删除生效
			return new Response('OK - New chat member message deleted', { status: 200 });
		}
		// #endregion 👥 新成员消息删除

		// #region ☁️ 云过滤检测
		// 🏷️ 云过滤检测逻辑
		const { executeCloudFilter, executeActions } = await import('./cloudFilter/index.js');
		const { isBotAdmin, isGroupAdmin } = await import('./handlers/permissions.js');
		
		// 创建上下文对象用于权限检查
		const botCtx = {
			message,
			env,
			executionCtx: ctx
		};
		
		// 检查是否需要跳过检测
		const shouldSkipDetection = await isBotAdmin(botCtx) || await isGroupAdmin(botCtx);
		
		if (shouldSkipDetection) {
			Logger.debug('跳过管理员消息的自动检测', {
				userId: message.from?.id,
				userName: message.from?.first_name,
				messageType: text.startsWith('/') ? '命令' : '普通消息',
				isEditedMessage
			});
		} else {
			// 检查群组云过滤开关
			const groupConfig = await getGroupConfig(env, message.chat.id.toString());
			const cloudFilterEnabled = groupConfig?.cloud_filter_enabled ?? true; // 默认启用
			
			if (!cloudFilterEnabled) {
				Logger.debug('群组已禁用云过滤功能，跳过检测', {
					chatId: message.chat.id,
					messageId: message.message_id,
					isEditedMessage
				});
			} else {
				// 执行云过滤检测
				Logger.debug(`🔍 开始云过滤检测 ${isEditedMessage ? '(编辑消息)' : '(新消息)'}`, {
					chatId: message.chat.id,
					messageId: message.message_id,
					userId: message.from?.id,
					isEditedMessage
				});

				const filterResult = await executeCloudFilter(message, env, {
					mode: 'auto',
					executionContext: ctx
				});

				if (filterResult.shouldDelete) {
					// 执行删除和封禁操作
					const actionResult = await executeActions(filterResult, message, env, ctx);
					Logger.info(`${isEditedMessage ? '编辑消息' : '新消息'}违规处理: ${actionResult.responseText}`);
					return new Response(actionResult.responseText, { status: 200 });
				}
			}
		}
		// #endregion ☁️ 云过滤检测

		// #region 📢 官方消息处理
		// 检查是否是来自目标群组的消息
		const chatId = message.chat.id.toString();
		const fromId = message.from?.id;

		// 检查是否是Telegram官方账号在DefaultGroupsBind群组中发送的消息
		const isInDefaultGroup = DefaultGroupsBind.some((group) => group.id === chatId);
		if (isInDefaultGroup && fromId === TELEGRAM_OFFICIAL_ID) {
			// 检查是否是从广告频道转发的消息
			if (message.forward_from_chat && message.forward_from_chat.id === -1001549390517) {
				// 如果是从广告频道转发的消息，直接删除
				ctx.waitUntil(deleteForwardedAdMessage(env, chatId, message.message_id));
			} else {
				// 其他消息继续执行取消置顶操作
				ctx.waitUntil(unpinOfficialMessage(env, chatId, message.message_id));
			}
		}
		// #endregion 📢 官方消息处理

		// #region 🤖 命令处理
		// 只对新消息处理命令，编辑消息不触发命令
		if (!isEditedMessage) {
			const { handleCommand } = await import('./handlers/commands.js');
			const commandHandled = await handleCommand(message, botCtx, env.TELEGRAM_BOT_USERNAME, ctx);
			if (commandHandled) {
				return new Response('Command handled', { status: 200 });
			}
		}
		// #endregion 🤖 命令处理

		// #region ✍️ #投稿 功能处理
		// 处理"#投稿"文字回复（只对新消息生效，编辑消息不触发投稿）
		if (!isEditedMessage && text.trim() === '#投稿' && message.reply_to_message) {
			const replyMessage = message.reply_to_message;
			
			// 只在沙雕英雄群中处理
			if (chatId !== '-1001143091022') {
				return new Response('OK', { status: 200 });
			}
			
			// 检查回复的消息是否包含媒体
			if (!hasMedia(replyMessage)) {
				Logger.debug('回复的消息不包含媒体，跳过处理');
				return new Response('OK', { status: 200 });
			}
			
			try {
				// 步骤1: 优先查询新数据库表中是否已有记录
				let existingRecord = await getForwardedSubmissionRecord(env, replyMessage.message_id, chatId);
				let isMediaGroup = false;
				let mediaGroupRecords = [];
				
				if (!existingRecord && replyMessage.media_group_id) {
					// 如果没有找到单条记录但有媒体组ID，查询媒体组记录
					mediaGroupRecords = await getMediaGroupForwardedRecords(env, replyMessage.media_group_id);
					if (mediaGroupRecords.length > 0) {
						existingRecord = mediaGroupRecords[0]; // 取第一条作为参考
						isMediaGroup = true;
					}
				}
				
				// 步骤2: 根据是否找到记录以及记录状态决定处理方式
				let shouldForwardToTopic4 = false;
				
				if (!existingRecord) {
					Logger.debug('未找到转发记录，直接转发到话题4');
					shouldForwardToTopic4 = true;
				} else if (existingRecord.sub_topic_id === 4) {
					Logger.debug('消息已在话题4中，直接跳过');
					return new Response('OK - Already in topic 4', { status: 200 });
				} else if (existingRecord.sub_topic_id === 6) {
					Logger.debug('找到话题6中的记录，开始处理转移');
					
					// 步骤3: 删除话题6中的消息和记录
					const TARGET_GROUP_ID = '-1002599022189';
					
					if (isMediaGroup) {
						// 处理媒体组：删除所有相关消息
						for (const record of mediaGroupRecords) {
							await deleteMessage(env, TARGET_GROUP_ID, record.sub_message_id, 6);
							await deleteForwardedSubmissionRecord(env, record.id);
						}
					} else {
						// 处理单条消息
						await deleteMessage(env, TARGET_GROUP_ID, existingRecord.sub_message_id, 6);
						await deleteForwardedSubmissionRecord(env, existingRecord.id);
					}
					
					Logger.debug('已删除话题6中的消息和记录');
					shouldForwardToTopic4 = true;
				}
				
				// 步骤4: 如果需要转发到话题4，执行转发
				if (!shouldForwardToTopic4) {
					return new Response('OK - No forwarding needed', { status: 200 });
				}
				
				// 执行转发到话题4
				// 创建临时消息对象，强制转发到话题4
				const tempMessage = {
					...replyMessage,
					caption: (replyMessage.caption || '') + ' #投稿'
				};
				
				// 创建上下文对象
				const forwardCtx = {
					message: tempMessage,
					env,
					executionCtx: ctx
				};
				
				// 使用 forwardMedia 函数转发到话题4
				const forwardResult = await forwardMedia(forwardCtx);
				
				// forwardMedia 可能返回null（媒体组会添加到队列），这是正常的
				if (forwardResult !== null && (!forwardResult || !forwardResult.ok)) {
					throw new Error('转发到话题4失败');
				}
				
				Logger.debug('已成功转发到话题4');
				
				return new Response('OK - Submission to topic 4 completed', { status: 200 });
				
			} catch (error) {
				Logger.error('处理#投稿时出错:', error);
				
				// 即使处理失败也要返回200状态码，避免Telegram重复发送
				return new Response('OK - Error handled', { status: 200 });
			}
		}
		// #endregion ✍️ #投稿 功能处理

		// #region 🖼️ 媒体转发与日志
		// 处理媒体转发和日志记录，排除Telegram官方账号发送的消息（只对新消息生效，编辑消息不触发转发）
		if (!isEditedMessage && (TargetGroups.forwardMedia.includes(chatId) || TargetGroups.logMedia.includes(chatId)) && fromId !== TELEGRAM_OFFICIAL_ID) {
			// 判断是否是媒体组消息
			if (message.media_group_id) {
				// 对于媒体组消息，先记录到数据库
				if (TargetGroups.logMedia.includes(chatId)) {
					await mediaGroupLog(botCtx);
				}

				// 然后处理媒体组转发
				if (TargetGroups.forwardMedia.includes(chatId)) {
					// 将媒体组添加到转发队列，而不是直接转发
					await queueMediaGroupForward(botCtx);
				}
			} else {
				// 对于单个媒体消息，直接处理
				if (TargetGroups.forwardMedia.includes(chatId)) {
					await forwardMedia(botCtx);
				}
			}
		}
		// #endregion 🖼️ 媒体转发与日志

		// #region ⏳ 异步任务处理
		// 处理待处理的媒体组队列
		ctx.waitUntil(processAllPendingQueues(env));

		// 每天清理一次旧的队列记录
		// 使用随机数确保不是每个请求都执行清理
		if (Math.random() < 0.01) {
			// 1%的概率执行清理
			ctx.waitUntil(cleanupOldQueueRecords(env));
		}
		// #endregion ⏳ 异步任务处理

		return new Response('OK', { status: 200 });
	} catch (error) {
		// #region 🚨 全局错误处理
		Logger.error('处理Webhook请求时出错:', error);
		// Webhook 处理出错时也要返回200，避免Telegram重试
		return new Response('OK - Error handled', { status: 200 });
		// #endregion 🚨 全局错误处理
	}
}


