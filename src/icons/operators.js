/**
 * 操作符图标路径 (来自 Tabler Icons)
 * 自动生成，请勿手动修改
 */

export const OPERATOR_PATHS = {
    "+": {
        "paths": [
            "M12 5l0 14",
            "M5 12l14 0"
        ],
        "type": "stroke"
    },
    "-": {
        "paths": [
            "M5 12l14 0"
        ],
        "type": "stroke"
    },
    "=": {
        "paths": [
            "M5 10h14",
            "M5 14h14"
        ],
        "type": "stroke"
    },
    "?": {
        "paths": [
            "M8 8a3.5 3 0 0 1 3.5 -3h1a3.5 3 0 0 1 3.5 3a3 3 0 0 1 -2 3a3 4 0 0 0 -2 4",
            "M12 19l0 .01"
        ],
        "type": "stroke"
    },
    "×": {
        "paths": [
            "M18 6l-12 12",
            "M6 6l12 12"
        ],
        "type": "stroke"
    },
    "÷": {
        "paths": [
            "M5 12l14 0"
        ],
        "type": "stroke"
    }
};

export function generateOperatorPath(operator, x, y, scale = 1, color = '#0066cc') {
    const opData = OPERATOR_PATHS[operator];
    if (!opData) return '';
    
    let paths = '';
    opData.paths.forEach(path => {
        if (opData.type === 'stroke') {
            paths += `<path d="${path}" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>`;
        } else {
            paths += `<path d="${path}" fill="${color}"/>`;
        }
    });
    
    return `<g transform="translate(${x}, ${y}) scale(${scale})">
        ${paths}
    </g>`;
}
