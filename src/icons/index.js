/**
 * Tabler Icons 统一入口模块
 * 自动生成，请勿手动修改
 */

import { DIGIT_PATHS, generateDigitPath } from './digits.js';
import { LETTER_PATHS, generateLetterPath } from './letters.js';
import { OPERATOR_PATHS, generateOperatorPath } from './operators.js';

// 重新导出所有内容
export { DIGIT_PATHS, generateDigitPath, LETTER_PATHS, generateLetterPath, OPERATOR_PATHS, generateOperatorPath };

/**
 * 生成任意字符的路径
 */
export function generateCharacterPath(char, x, y, scale = 1, color = '#0066cc') {
    // 数字处理
    if (/[0-9]/.test(char)) {
        return generateDigitPath(char, x, y, scale, color);
    }
    
    // 字母处理
    if (/[A-D]/.test(char)) {
        return generateLetterPath(char, x, y, scale, color);
    }
    
    // 操作符处理
    if (/[+\-=?×÷]/.test(char)) {
        return generateOperatorPath(char, x, y, scale, color);
    }
    
    // 未找到字符，返回占位符
    return `<g transform="translate(${x}, ${y}) scale(${scale})">
        <circle cx="12" cy="12" r="8" fill="none" stroke="${color}" stroke-width="2"/>
        <text x="12" y="16" text-anchor="middle" fill="${color}" font-size="10">${char}</text>
    </g>`;
}
