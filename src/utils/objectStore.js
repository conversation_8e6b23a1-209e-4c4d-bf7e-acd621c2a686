import Logger from './logger.js';

/**
 * Durable Objects 键值存储封装类
 * 提供简单的全局接口：OB.set('key', 'value'), OB.get('key')
 */
class DurableObjectStore {
	constructor() {
		this.env = null;
		this.namespace = null;
		this.objectId = null;
		this.objectStub = null;
	}

	/**
	 * 初始化存储对象
	 * @param {Object} env - Cloudflare Workers 环境对象
	 * @param {string} bindingName - Durable Object 绑定名称 (默认: 'KEY_VALUE_STORE')
	 * @param {string} objectName - 对象实例名称 (默认: 'global-store')
	 */
	init(env, bindingName = 'KEY_VALUE_STORE', objectName = 'global-store') {
		this.env = env;
		this.namespace = env[bindingName];
		
		if (!this.namespace) {
			throw new Error(`Durable Object binding '${bindingName}' not found. Please check your wrangler.toml configuration.`);
		}
		
		this.objectId = this.namespace.idFromName(objectName);
		this.objectStub = this.namespace.get(this.objectId);
		
		Logger.debug('DurableObjectStore 初始化完成:', { bindingName, objectName });
	}

	/**
	 * 私有方法：发送请求到 Durable Object
	 * @param {string} action - 操作类型
	 * @param {Object} data - 请求数据
	 * @returns {Promise<any>} 响应数据
	 */
	async _request(action, data = {}) {
		if (!this.objectStub) {
			throw new Error('DurableObjectStore not initialized. Call init() first.');
		}

		try {
			const request = new Request(`https://dummy.url/${action}`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(data)
			});

			const response = await this.objectStub.fetch(request);
			
			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Request failed: ${response.status} ${errorText}`);
			}

			return await response.json();
		} catch (error) {
			Logger.error('DurableObjectStore 请求失败:', error);
			throw error;
		}
	}

	/**
	 * 设置键值对
	 * @param {string} key - 键
	 * @param {any} value - 值
	 * @param {number} ttl - 过期时间（秒），可选
	 * @returns {Promise<boolean>} 是否成功
	 */
	async set(key, value, ttl = null) {
		try {
			const data = { key, value };
			if (ttl) data.ttl = ttl;
			
			const result = await this._request('set', data);
			Logger.debug('设置键值:', { key, success: result.success });
			return result.success;
		} catch (error) {
			Logger.error(`设置键值失败 [${key}]:`, error);
			return false;
		}
	}

	/**
	 * 获取值
	 * @param {string} key - 键
	 * @param {any} defaultValue - 默认值
	 * @returns {Promise<any>} 值
	 */
	async get(key, defaultValue = null) {
		try {
			const result = await this._request('get', { key });
			const value = result.value !== undefined ? result.value : defaultValue;
			Logger.debug('获取键值:', { key, found: result.value !== undefined });
			return value;
		} catch (error) {
			Logger.error(`获取键值失败 [${key}]:`, error);
			return defaultValue;
		}
	}

	/**
	 * 删除键
	 * @param {string} key - 键
	 * @returns {Promise<boolean>} 是否删除成功
	 */
	async delete(key) {
		try {
			const result = await this._request('delete', { key });
			Logger.debug('删除键值:', { key, deleted: result.deleted });
			return result.deleted;
		} catch (error) {
			Logger.error(`删除键值失败 [${key}]:`, error);
			return false;
		}
	}

	/**
	 * 清空所有数据
	 * @returns {Promise<boolean>} 是否成功
	 */
	async clear() {
		try {
			const result = await this._request('clear');
			Logger.debug('清空所有数据:', result.success);
			return result.success;
		} catch (error) {
			Logger.error('清空数据失败:', error);
			return false;
		}
	}

	/**
	 * 获取所有键
	 * @param {string} prefix - 键前缀，可选
	 * @param {number} limit - 限制数量，可选
	 * @returns {Promise<string[]>} 键数组
	 */
	async keys(prefix = null, limit = null) {
		try {
			const data = {};
			if (prefix) data.prefix = prefix;
			if (limit) data.limit = limit;
			
			const result = await this._request('keys', data);
			Logger.debug('获取键列表:', { count: result.keys.length, prefix });
			return result.keys;
		} catch (error) {
			Logger.error('获取键列表失败:', error);
			return [];
		}
	}

	/**
	 * 获取存储大小
	 * @returns {Promise<number>} 键数量
	 */
	async size() {
		try {
			const result = await this._request('size');
			Logger.debug('获取存储大小:', result.size);
			return result.size;
		} catch (error) {
			Logger.error('获取存储大小失败:', error);
			return 0;
		}
	}

	/**
	 * 检查键是否存在
	 * @param {string} key - 键
	 * @returns {Promise<boolean>} 是否存在
	 */
	async has(key) {
		try {
			const result = await this._request('get', { key });
			return result.value !== undefined;
		} catch (error) {
			Logger.error(`检查键存在失败 [${key}]:`, error);
			return false;
		}
	}

	/**
	 * 批量设置
	 * @param {Object} pairs - 键值对对象
	 * @param {number} ttl - 过期时间（秒），可选
	 * @returns {Promise<boolean>} 是否全部成功
	 */
	async setMultiple(pairs, ttl = null) {
		try {
			const promises = Object.entries(pairs).map(([key, value]) => 
				this.set(key, value, ttl)
			);
			const results = await Promise.all(promises);
			return results.every(result => result === true);
		} catch (error) {
			Logger.error('批量设置失败:', error);
			return false;
		}
	}

	/**
	 * 批量获取
	 * @param {string[]} keys - 键数组
	 * @param {any} defaultValue - 默认值
	 * @returns {Promise<Object>} 键值对对象
	 */
	async getMultiple(keys, defaultValue = null) {
		try {
			const promises = keys.map(key => 
				this.get(key, defaultValue).then(value => [key, value])
			);
			const results = await Promise.all(promises);
			return Object.fromEntries(results);
		} catch (error) {
			Logger.error('批量获取失败:', error);
			return {};
		}
	}
}

// 创建全局实例
const DO = new DurableObjectStore();

// 创建简化的全局函数（需要先初始化）
export const createObjectStore = (env, bindingName, objectName) => {
	const store = new DurableObjectStore();
	store.init(env, bindingName, objectName);
	return store;
};

// 默认导出全局实例
export default DO;

// 导出类以供自定义使用
export { DurableObjectStore }; 