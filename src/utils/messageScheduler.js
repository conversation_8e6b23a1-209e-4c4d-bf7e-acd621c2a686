import Logger from './logger.js';

/**
 * 消息调度工具函数
 * 封装对 MessageScheduler Durable Object 的调用
 */

/**
 * 调度消息自动删除
 * @param {Object} env 环境变量
 * @param {string} chatId 聊天ID
 * @param {number} messageId 消息ID
 * @param {number} delaySeconds 延迟秒数
 * @param {string} description 任务描述
 * @returns {Promise<string>} 返回任务ID
 */
export async function scheduleMessageDelete(env, chatId, messageId, delaySeconds, description = '消息删除') {
    try {
        // 生成唯一的任务ID
        const taskId = `delete_${chatId}_${messageId}_${Date.now()}`;
        
        // 获取 Durable Object 实例
        const schedulerId = env.MESSAGE_SCHEDULER.idFromName('message-scheduler');
        const schedulerObj = env.MESSAGE_SCHEDULER.get(schedulerId);

        // #region 📝 调试：记录计划触发时间
        const plannedTriggerTime = new Date(Date.now() + delaySeconds * 1000).toISOString();
        Logger.debug('📝 [ScheduleDelete] 调度入口', {
            taskId,
            chatId,
            messageId,
            delaySeconds,
            plannedTriggerTime,
            doName: 'message-scheduler'
        });
        // #endregion 📝 调试：记录计划触发时间

        // 调用调度方法
        const response = await schedulerObj.fetch(new Request('https://scheduler/schedule-delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                taskId,
                chatId,
                messageId,
                delaySeconds,
                description
            })
        }));

        if (!response.ok) {
            Logger.warn('⚠️ [ScheduleDelete] 调度请求返回非200', { taskId, status: response.status });
            throw new Error(`调度失败: ${response.status}`);
        }

        Logger.debug('✅ 消息删除任务已调度:', { taskId, chatId, messageId, delaySeconds, description });
        return taskId;

    } catch (error) {
        Logger.error('❌ 调度消息删除任务失败:', error);
        return null;
    }
}

/**
 * 取消消息删除任务
 * @param {Object} env 环境变量
 * @param {string} taskId 任务ID
 */
export async function cancelMessageDelete(env, taskId) {
    try {
        // 获取 Durable Object 实例
        const schedulerId = env.MESSAGE_SCHEDULER.idFromName('message-scheduler');
        const schedulerObj = env.MESSAGE_SCHEDULER.get(schedulerId);

        // 调用取消方法
        const response = await schedulerObj.fetch(new Request('https://scheduler/cancel-delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ taskId })
        }));

        if (!response.ok) {
            throw new Error(`取消失败: ${response.status}`);
        }

        Logger.debug('✅ 消息删除任务已取消:', { taskId });
        return true;

    } catch (error) {
        Logger.error('❌ 取消消息删除任务失败:', error);
        return false;
    }
}