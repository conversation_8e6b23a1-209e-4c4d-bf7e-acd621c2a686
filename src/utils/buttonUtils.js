/**
 * 按钮配置工具
 * 统一管理投稿相关的按钮配置和创建
 */

import { getGradeConfig } from './gradeUtils.js';

/**
 * 统一的分级按钮配置
 */
export const GRADE_BUTTON_CONFIG = {
	clear: {
		text: '🔄取消分级',
		emoji: '🔄',
	},
	removeCaption: {
		text: '🗑️去除说明',
		emoji: '🗑️',
	},
	nsfw: {
		text: '🔞NSFW⚠️',
		emoji: '🔞',
	},
	qwyl: {
		text: '#️⃣奇闻异录',
		emoji: '#️⃣',
	},
	qw: {
		text: '#️⃣奇闻异录',
		emoji: '#️⃣',
	},
	R18G: {
		text: '⚠️三重警告',
		emoji: '⚠️',
	},
	waitHeroes: {
		text: '🥺等一位英雄',
		emoji: '🥺',
	},
	wh: {
		text: '🥺等一位英雄',
		emoji: '🥺',
	},
	cf: {
		text: '🤔迷惑',
		emoji: '🤔',
	},
	confuse: {
		text: '🤔迷惑',
		emoji: '🤔',
	},
};

/**
 * 按钮类型枚举
 */
export const BUTTON_TYPES = {
	COMMAND: 'cmd', // /0命令投稿
	CONTRIBUTE: 'co', // 投稿群内CB处理
	SUBMIT: 'sc', // 投稿到频道
};

/**
 * 获取分级按钮配置
 * @param {string} grade 分级类型
 * @returns {Object} 按钮配置
 */
export function getGradeButtonConfig(grade) {
	return (
		GRADE_BUTTON_CONFIG[grade] || {
			text: getGradeConfig(grade).displayName,
			emoji: '',
		}
	);
}

/**
 * 创建分级选项按钮组
 * @param {Object} options 选项
 * @param {string} options.buttonType 按钮类型 ('cmd', 'co', 'sc')
 * @param {string} options.messageId 消息ID
 * @param {boolean} options.includeConfuse 是否包含迷惑按钮
 * @param {boolean} options.isDocument 是否为文档类型（影响按钮布局）
 * @returns {Array} 按钮行数组
 */
export function createGradeButtons(options) {
	const { buttonType, messageId, includeConfuse = true, isDocument = false } = options;

	const buttons = [];

	// 第一行：清空说明 + NSFW
	buttons.push([
		{
			text: GRADE_BUTTON_CONFIG.clear.text,
			callback_data: `${buttonType}:clear:${messageId}`,
		},
		{
			text: GRADE_BUTTON_CONFIG.nsfw.text,
			callback_data: `${buttonType}:nsfw:${messageId}`,
		},
	]);

	// 第二行：奇闻异录 + 三重警告（文档类型时只有奇闻异录）
	if (isDocument) {
		// 对于cmd类型使用qwyl，对于co类型使用qw（保持兼容性）
		const qwKey = buttonType === 'cmd' ? 'qwyl' : 'qw';
		buttons.push([
			{
				text: GRADE_BUTTON_CONFIG.qwyl.text,
				callback_data: `${buttonType}:${qwKey}:${messageId}`,
			},
		]);
	} else {
		// 对于cmd类型使用qwyl，对于co类型使用qw（保持兼容性）
		const qwKey = buttonType === 'cmd' ? 'qwyl' : 'qw';
		buttons.push([
			{
				text: GRADE_BUTTON_CONFIG.qwyl.text,
				callback_data: `${buttonType}:${qwKey}:${messageId}`,
			},
			{
				text: GRADE_BUTTON_CONFIG.R18G.text,
				callback_data: `${buttonType}:R18G:${messageId}`,
			},
		]);
	}

	// 第三行：等一位英雄 + 迷惑（如果包含）
	if (includeConfuse) {
		// 对于cmd类型使用waitHeroes，对于co类型使用wh（保持兼容性）
		const heroKey = buttonType === 'cmd' ? 'waitHeroes' : 'wh';
		const confuseKey = buttonType === 'cmd' ? 'cf' : 'cf';
		buttons.push([
			{
				text: GRADE_BUTTON_CONFIG.waitHeroes.text,
				callback_data: `${buttonType}:${heroKey}:${messageId}`,
			},
			{
				text: GRADE_BUTTON_CONFIG.cf.text,
				callback_data: `${buttonType}:${confuseKey}:${messageId}`,
			},
		]);
	} else {
		// 对于cmd类型使用waitHeroes，对于co类型使用wh（保持兼容性）
		const heroKey = buttonType === 'cmd' ? 'waitHeroes' : 'wh';
		buttons.push([
			{
				text: GRADE_BUTTON_CONFIG.waitHeroes.text,
				callback_data: `${buttonType}:${heroKey}:${messageId}`,
			},
		]);
	}

	return buttons;
}

/**
 * 创建/0命令投稿按钮
 * @param {Object} replyMessage 被回复的消息
 * @param {Object} groupConfig 群组配置
 * @returns {Object} 按钮配置
 */
export function createCommandContributeButtons(replyMessage, groupConfig) {
	const messageId = replyMessage.message_id;
	const chatId = replyMessage.chat.id.toString();
	const channelId = groupConfig.defaultChannel;

	const buttons = [];

	// 第一行：立即投稿按钮
	buttons.push([
		{
			text: '🚀立即投稿',
			callback_data: `cmd_submit:${chatId.replace('-100', '')}:${messageId}:${channelId.replace('-100', '')}`,
		},
	]);

	// 检查是否有原始说明文字（仅媒体消息）
	const hasOriginal = hasOriginalCaption(replyMessage);

	// 如果有原始说明文字，在第二行添加【去除说明】按钮
	if (hasOriginal) {
		buttons.push([
			{
				text: GRADE_BUTTON_CONFIG.removeCaption.text,
				callback_data: `cmd:removeCaption:${messageId}`,
			},
		]);
	}

	// 添加分级按钮
	const isDocument = replyMessage.document && !replyMessage.photo && !replyMessage.video && !replyMessage.animation;
	const gradeButtons = createGradeButtons({
		buttonType: 'cmd',
		messageId: messageId,
		includeConfuse: true,
		isDocument: isDocument,
	});

	buttons.push(...gradeButtons);

	// 最后一行：取消按钮
	buttons.push([
		{
			text: '❌取消',
			callback_data: `cmd_cancel:${messageId}`,
		},
	]);

	return {
		inline_keyboard: buttons,
	};
}

/**
 * 创建投稿群内的分级按钮
 * @param {string} messageId 消息ID
 * @param {boolean} includeConfuse 是否包含迷惑按钮
 * @returns {Array} 按钮行数组
 */
export function createContributeGradeButtons(messageId, includeConfuse = true) {
	return createGradeButtons({
		buttonType: 'co',
		messageId: messageId,
		includeConfuse: includeConfuse,
		isDocument: false,
	});
}

/**
 * 创建投稿到频道按钮
 * @param {string} chatId 来源群组ID
 * @param {string} originalMessageId 原始消息ID
 * @param {string} forwardedMessageId 转发到投稿群的消息ID
 * @param {string} topicId 话题ID
 * @param {Array} defaultGroupsBind 群组绑定配置（从外部传入）
 * @param {Object} env 环境变量（用于数据库查询）
 * @param {Object} originalMessage 原始消息对象（可选，优先使用）
 * @returns {Promise<Object|null>} 按钮配置或null
 */
export async function createSubmitToChannelButtons(
	chatId,
	originalMessageId,
	forwardedMessageId,
	topicId,
	defaultGroupsBind,
	env = null,
	originalMessage = null
) {
	// 获取默认频道
	const groupConfig = defaultGroupsBind.find((group) => group.id === chatId);
	if (!groupConfig || !groupConfig.defaultChannel) return null;

	const groupName = groupConfig.name || '频道';
	const simplifiedGroupId = chatId.replace('-100', '');

	// 基础按钮 - 投稿到频道
	const buttons = [
		[
			{
				text: `🚀投稿到${groupName}频道`,
				callback_data: `sc:${simplifiedGroupId}:${originalMessageId}:${forwardedMessageId}`,
			},
		],
	];

	// 为话题4和话题6和话题2829添加分级按钮和去除说明按钮
	if (topicId === '4' || topicId === '6' || topicId === '2829') {
		// 检查是否需要显示去除说明按钮
		let showRemoveCaptionButton = false;

		// 优先使用传入的原始消息对象
		if (originalMessage) {
			showRemoveCaptionButton = hasOriginalCaption(originalMessage);
		} else if (env) {
			// 降级：尝试从数据库获取原始消息信息
			try {
				const dbMessage = await getOriginalMessageFromDatabase(env, chatId, originalMessageId);
				if (dbMessage) {
					showRemoveCaptionButton = hasOriginalCaption(dbMessage);
				}
			} catch (error) {
				// 如果数据库查询失败，不显示去除说明按钮
				console.debug('获取原始消息信息失败，跳过去除说明按钮:', error);
			}
		}

		// 如果有原始说明文字，添加去除说明按钮
		if (showRemoveCaptionButton) {
			buttons.push([
				{
					text: GRADE_BUTTON_CONFIG.removeCaption.text,
					callback_data: `co:removeCaption:${forwardedMessageId}`,
				},
			]);
		}

		// 添加分级按钮
		const gradeButtons = createGradeButtons({
			buttonType: 'co',
			messageId: forwardedMessageId,
			includeConfuse: true,
			isDocument: false,
		});

		buttons.push(...gradeButtons);
	}

	// 添加取消按钮
	buttons.push([
		{
			text: '❌取消',
			callback_data: 'cc',
		},
	]);

	return {
		inline_keyboard: buttons,
	};
}

/**
 * 从数据库获取原始消息信息
 * @param {Object} env 环境变量
 * @param {string} chatId 来源群组ID
 * @param {string} originalMessageId 原始消息ID
 * @returns {Promise<Object|null>} 原始消息对象或null
 */
async function getOriginalMessageFromDatabase(env, chatId, originalMessageId) {
	try {
		// 首先尝试从媒体组记录中获取
		const mediaGroupStmt = env.DB.prepare(
			`
			SELECT raw_json 
			FROM tg_log_media_group 
			WHERE msg_id = ? AND group_id = ?
		`
		).bind(originalMessageId, chatId);

		const mediaGroupRecord = await mediaGroupStmt.first();

		if (mediaGroupRecord && mediaGroupRecord.raw_json) {
			const originalMessage =
				typeof mediaGroupRecord.raw_json === 'string' ? JSON.parse(mediaGroupRecord.raw_json) : mediaGroupRecord.raw_json;
			return originalMessage;
		}

		// 如果媒体组记录中没有，尝试从转发记录中获取（虽然这里通常不会有原始消息的完整信息）
		// 这里可以根据需要扩展其他数据源

		return null;
	} catch (error) {
		console.error('从数据库获取原始消息失败:', error);
		return null;
	}
}

/**
 * 检测消息是否有原始说明文字
 * @param {Object} message 消息对象
 * @returns {boolean} 是否有原始说明文字
 */
export function hasOriginalCaption(message) {
	// 检查是否是媒体消息
	const isMediaMessage = !!(
		message.photo ||
		message.video ||
		message.animation ||
		message.document ||
		message.audio ||
		message.voice ||
		message.video_note ||
		message.sticker
	);

	// 纯文字消息不显示去除说明按钮
	if (!isMediaMessage) {
		return false;
	}

	// 检查是否有caption
	const hasCaption = message.caption && message.caption.trim() !== '';

	// 如果有caption，检查是否不只是via/from信息
	if (hasCaption) {
		const caption = message.caption.trim();
		// 如果caption只包含via或from信息，认为没有原始说明文字
		const isOnlyViaFrom = /^(via\s+.+|from\s+.+|via\s+.+\s+from\s+.+)$/i.test(caption);
		return !isOnlyViaFrom;
	}

	return false;
}

/**
 * 统一的投稿按钮创建函数
 * 根据上下文自动选择合适的按钮创建方式
 * @param {Object} options 选项
 * @param {string} options.type 按钮类型 ('command' | 'contribute')
 * @param {Object} options.message 消息对象
 * @param {Object} options.groupConfig 群组配置（command类型需要）
 * @param {string} options.chatId 来源群组ID（contribute类型需要）
 * @param {string} options.originalMessageId 原始消息ID（contribute类型需要）
 * @param {string} options.forwardedMessageId 转发消息ID（contribute类型需要）
 * @param {string} options.topicId 话题ID（contribute类型需要）
 * @param {Array} options.defaultGroupsBind 群组绑定配置（contribute类型需要）
 * @param {Object} options.env 环境变量（contribute类型需要）
 * @returns {Promise<Object|null>} 按钮配置或null
 */
export async function createUnifiedSubmissionButtons(options) {
	const { type } = options;

	if (type === 'command') {
		// /0命令投稿按钮
		const { message, groupConfig } = options;
		return createCommandContributeButtons(message, groupConfig);
	} else if (type === 'contribute') {
		// 投稿群内按钮
		const { chatId, originalMessageId, forwardedMessageId, topicId, defaultGroupsBind, env } = options;
		return await createSubmitToChannelButtons(chatId, originalMessageId, forwardedMessageId, topicId, defaultGroupsBind, env);
	}

	return null;
}
