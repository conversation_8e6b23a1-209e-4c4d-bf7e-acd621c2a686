import Logger from './logger.js';

//#region 消息时间检查工具

/**
 * 检查消息是否超过48小时无法删除
 * @param {number} messageTimestamp 消息时间戳（秒）
 * @returns {boolean} 是否超过48小时
 */
export function isMessageExpired(messageTimestamp) {
	try {
		const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
		const ageHours = (now - messageTimestamp) / 3600; // 转换为小时
		
		Logger.debug('消息时间检查:', {
			messageTime: new Date(messageTimestamp * 1000).toISOString(),
			currentTime: new Date(now * 1000).toISOString(),
			ageHours: ageHours.toFixed(2)
		});
		
		return ageHours > 48;
	} catch (error) {
		Logger.error('检查消息时间失败:', error);
		return false; // 检查失败时默认认为未过期，让API调用来判断
	}
}

/**
 * 从数据库获取消息的时间戳
 * @param {Object} env 环境变量
 * @param {number} submissionMessageId 投稿群内的消息ID
 * @returns {Promise<number|null>} 消息时间戳或null
 */
export async function getMessageTimestamp(env, submissionMessageId) {
	try {
		const stmt = env.DB.prepare(`
			SELECT date FROM tg_log_forwarded_submissions 
			WHERE sub_message_id = ? 
			LIMIT 1
		`).bind(submissionMessageId);
		
		const result = await stmt.first();
		
		if (result && result.date) {
			Logger.debug('找到消息时间戳:', {
				submissionMessageId,
				timestamp: result.date,
				date: new Date(result.date * 1000).toISOString()
			});
			return result.date;
		}
		
		Logger.warn('未找到消息时间戳:', submissionMessageId);
		return null;
	} catch (error) {
		Logger.error('获取消息时间戳失败:', error);
		return null;
	}
}

/**
 * 检查投稿群按钮消息对应的原始消息是否超过48小时
 * @param {Object} env 环境变量
 * @param {Object} buttonMessage 按钮消息对象
 * @returns {Promise<boolean>} 是否超过48小时
 */
export async function checkSubmissionMessageExpired(env, buttonMessage) {
	try {
		// 从按钮消息的reply_to_message获取投稿消息ID
		if (!buttonMessage.reply_to_message) {
			Logger.warn('按钮消息没有reply_to_message，无法检查时间');
			return false;
		}
		
		const submissionMessageId = buttonMessage.reply_to_message.message_id;
		const timestamp = await getMessageTimestamp(env, submissionMessageId);
		
		if (timestamp === null) {
			Logger.warn('无法获取消息时间戳，默认认为未过期');
			return false;
		}
		
		return isMessageExpired(timestamp);
	} catch (error) {
		Logger.error('检查投稿消息时间失败:', error);
		return false;
	}
}

//#endregion 