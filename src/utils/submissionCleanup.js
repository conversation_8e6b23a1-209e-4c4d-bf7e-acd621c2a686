import Logger from './logger.js';
import { sendTelegramRequest } from './telegramApi.js';

//#region 投稿记录定时清理工具

/**
 * 定时清理过期的投稿记录
 * 检查话题6中超过47小时的记录，批量删除对应消息和数据库记录
 * @param {Object} db 数据库连接
 * @param {string} botToken Bot Token
 * @returns {Promise<void>}
 */
export async function cleanupExpiredSubmissions(db, botToken) {
	Logger.tagLog('CLEANUP', '开始清理话题6中的过期投稿记录');
	
	try {
		// 查找话题6中超过47小时的记录
		const expiredRecords = await db.prepare(`
			SELECT * FROM tg_log_forwarded_submissions 
			WHERE sub_topic_id = 6 
			AND date < (strftime('%s', 'now') - 47 * 3600)
			ORDER BY date ASC
		`).all();
		
		const records = expiredRecords.results || [];
		
		if (records.length === 0) {
			Logger.debug('话题6中没有找到超过47小时的记录');
			return;
		}
		
		Logger.info(`话题6中找到 ${records.length} 条超过47小时的记录，开始批量清理`);
		
		const targetGroupId = '-1002599022189'; // 投稿专用处理群ID
		
		let successCount = 0;
		let deleteFailCount = 0;
		
		// 收集需要删除的消息ID，并去重媒体组
		const messagesToDelete = [];
		const recordMap = new Map(); // messageId -> record的映射
		const processedMediaGroups = new Set(); // 已处理的媒体组ID集合
		const uniqueRecords = []; // 去重后的记录列表
		
		for (const record of records) {
			const messageId = record.sub_message_id;
			
			// 如果是媒体组记录，检查是否已经处理过
			if (record.media_group_id) {
				if (processedMediaGroups.has(record.media_group_id)) {
					Logger.debug(`跳过重复的媒体组记录: ${record.media_group_id}, 消息ID: ${messageId}`);
					continue; // 跳过重复的媒体组
				}
				processedMediaGroups.add(record.media_group_id);
			}
			
			messagesToDelete.push(messageId);
			recordMap.set(messageId, record);
			uniqueRecords.push(record);
		}
		
		Logger.info(`原始记录数: ${records.length}, 去重后记录数: ${uniqueRecords.length}, 准备批量删除 ${messagesToDelete.length} 条消息`);
		
		// 分批处理，每次最多100个消息
		const batchSize = 100;
		const batches = [];
		for (let i = 0; i < messagesToDelete.length; i += batchSize) {
			batches.push(messagesToDelete.slice(i, i + batchSize));
		}
		
		Logger.info(`将分 ${batches.length} 批处理，每批最多 ${batchSize} 条消息`);
		
		// 处理每个批次
		for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
			const batch = batches[batchIndex];
			Logger.debug(`处理第 ${batchIndex + 1}/${batches.length} 批，包含 ${batch.length} 条消息`);
			
			try {
				// 批量删除消息
				const deleteResult = await sendTelegramRequest(
					{ TELEGRAM_BOT_TOKEN: botToken },
					`https://api.telegram.org/bot${botToken}/deleteMessages`,
					{
						chat_id: targetGroupId,
						message_ids: batch
					}
				);
				
				if (deleteResult.ok) {
					// 批量删除成功，批量清理对应的数据库记录
					Logger.debug(`第 ${batchIndex + 1} 批消息删除成功，批量清理对应数据库记录`);
					
					// 收集需要删除的记录
					const recordsToDelete = batch.map(messageId => recordMap.get(messageId)).filter(Boolean);
					
					try {
						await batchCleanupDatabaseRecords(db, recordsToDelete);
						successCount += batch.length;
						Logger.success(`第 ${batchIndex + 1} 批处理完成: 成功处理 ${batch.length} 条消息和数据库记录`);
					} catch (dbError) {
						Logger.error(`第 ${batchIndex + 1} 批批量清理数据库记录失败:`, dbError);
						// 回退到单个清理模式
						for (const messageId of batch) {
							const record = recordMap.get(messageId);
							if (record) {
								try {
									await cleanupDatabaseRecord(db, record);
									successCount++;
								} catch (singleDbError) {
									Logger.error(`清理消息 ${messageId} 的数据库记录失败:`, singleDbError);
									deleteFailCount++;
								}
							}
						}
					}
					
				} else {
					// 批量删除失败，回退到单个删除模式
					const errorDesc = deleteResult.description || '';
					Logger.warn(`第 ${batchIndex + 1} 批批量删除失败: ${errorDesc}，回退到单个删除模式`);
					
					// 检查是否是因为消息太旧导致的批量删除失败
					const isOldMessageError = errorDesc.includes("message can't be deleted") || 
											errorDesc.includes('Bad Request: message can\'t be deleted');
					
					if (isOldMessageError) {
						Logger.info(`第 ${batchIndex + 1} 批消息因过期无法删除，直接清理数据库记录`);
						try {
							const recordsToDelete = batch.map(messageId => recordMap.get(messageId)).filter(Boolean);
							await batchCleanupDatabaseRecords(db, recordsToDelete);
							successCount += batch.length;
							Logger.success(`第 ${batchIndex + 1} 批过期记录清理完成: ${batch.length} 条`);
						} catch (dbError) {
							Logger.error(`第 ${batchIndex + 1} 批清理过期数据库记录失败:`, dbError);
							// 回退到单个删除模式
							const batchResult = await processSingleMessages(db, botToken, targetGroupId, batch, recordMap);
							successCount += batchResult.successCount;
							deleteFailCount += batchResult.failCount;
						}
					} else {
						// 其他错误，正常回退到单个删除模式
						const batchResult = await processSingleMessages(db, botToken, targetGroupId, batch, recordMap);
						successCount += batchResult.successCount;
						deleteFailCount += batchResult.failCount;
					}
				}
				
			} catch (apiError) {
				Logger.error(`第 ${batchIndex + 1} 批调用删除API时出错:`, apiError);
				Logger.info('回退到单个删除模式');
				
				const batchResult = await processSingleMessages(db, botToken, targetGroupId, batch, recordMap);
				successCount += batchResult.successCount;
				deleteFailCount += batchResult.failCount;
			}
			
			// 批次间添加小延迟避免API限制
			if (batchIndex < batches.length - 1) {
				await new Promise(resolve => setTimeout(resolve, 200));
			}
		}
		
		Logger.success(`话题6批量清理完成: 原始记录 ${records.length} 条，去重后 ${uniqueRecords.length} 条，成功 ${successCount} 条，失败 ${deleteFailCount} 条`);
		
	} catch (error) {
		Logger.error('清理话题6过期投稿记录失败:', error);
	}
}

/**
 * 单个删除消息的回退处理
 * @param {Object} db 数据库连接
 * @param {string} botToken Bot Token
 * @param {string} targetGroupId 目标群组ID
 * @param {Array} messageIds 消息ID列表
 * @param {Map} recordMap 消息ID到记录的映射
 * @returns {Promise<Object>} 处理结果统计
 */
async function processSingleMessages(db, botToken, targetGroupId, messageIds, recordMap) {
	let successCount = 0;
	let failCount = 0;
	
	Logger.debug(`开始单个删除 ${messageIds.length} 条消息`);
	
	for (const messageId of messageIds) {
		const record = recordMap.get(messageId);
		if (!record) {
			failCount++;
			continue;
		}
		
		const recordAge = ((Date.now() / 1000) - record.date) / 3600;
		
		Logger.debug('尝试删除过期消息:', {
			messageId: messageId,
			recordId: record.id,
			messageAge: recordAge.toFixed(2) + '小时',
			originalDate: new Date(record.date * 1000).toISOString()
		});
		
		try {
			// 尝试删除投稿群内的消息
			const deleteResult = await sendTelegramRequest(
				{ TELEGRAM_BOT_TOKEN: botToken },
				`https://api.telegram.org/bot${botToken}/deleteMessage`,
				{
					chat_id: targetGroupId,
					message_id: messageId
				}
			);
			
			if (deleteResult.ok) {
				// 删除成功，清理数据库记录
				await cleanupDatabaseRecord(db, record);
				successCount++;
				Logger.debug('成功删除消息和数据库记录:', messageId);
			} else {
				// 删除失败，记录错误但继续处理
				const errorDesc = deleteResult.description || '';
				Logger.debug('删除消息失败:', messageId, errorDesc);
				
				// 如果是消息不存在或消息太旧无法删除，都清理数据库记录
				if (errorDesc.includes('message to delete not found') || 
					errorDesc.includes("message can't be deleted") ||
					errorDesc.includes('Bad Request: message can\'t be deleted') ||
					recordAge >= 48) { // 超过48小时的消息也强制清理数据库
					await cleanupDatabaseRecord(db, record);
					successCount++;
					Logger.debug('消息无法删除或已过期，清理数据库记录:', messageId, `年龄: ${recordAge.toFixed(2)}小时`);
				} else {
					failCount++;
				}
			}
			
		} catch (apiError) {
			Logger.error('调用删除API时出错:', apiError);
			failCount++;
		}
		
		// 添加小延迟避免API限制
		await new Promise(resolve => setTimeout(resolve, 100));
	}
	
	return { successCount, failCount };
}

/**
 * 批量清理数据库记录
 * @param {Object} db 数据库连接
 * @param {Array} records 要清理的记录列表
 * @returns {Promise<void>}
 */
async function batchCleanupDatabaseRecords(db, records) {
	if (records.length === 0) {
		return;
	}
	
	// 分离单个记录和媒体组记录
	const singleRecords = records.filter(record => !record.media_group_id);
	const mediaGroupRecords = records.filter(record => record.media_group_id);
	
	let totalDeletedRows = 0;
	
	// 批量删除单个记录
	if (singleRecords.length > 0) {
		const recordIds = singleRecords.map(record => record.id);
		const placeholders = recordIds.map(() => '?').join(',');
		
		const deleteResult = await db.prepare(`
			DELETE FROM tg_log_forwarded_submissions 
			WHERE id IN (${placeholders})
		`).bind(...recordIds).run();
		
		const deletedRows = deleteResult.changes || 0;
		totalDeletedRows += deletedRows;
		Logger.debug(`批量删除单个记录: ${singleRecords.length} 条记录，实际删除 ${deletedRows} 行`);
	}
	
	// 批量删除媒体组记录
	if (mediaGroupRecords.length > 0) {
		const mediaGroupIds = [...new Set(mediaGroupRecords.map(record => record.media_group_id))];
		const placeholders = mediaGroupIds.map(() => '?').join(',');
		
		const deleteResult = await db.prepare(`
			DELETE FROM tg_log_forwarded_submissions 
			WHERE media_group_id IN (${placeholders})
		`).bind(...mediaGroupIds).run();
		
		const deletedRows = deleteResult.changes || 0;
		totalDeletedRows += deletedRows;
		Logger.debug(`批量删除媒体组记录: ${mediaGroupIds.length} 个媒体组，实际删除 ${deletedRows} 行`);
	}
	
	Logger.debug(`批量清理数据库记录完成: 总删除 ${totalDeletedRows} 行`);
}

/**
 * 清理单个数据库记录（回退模式使用）
 * @param {Object} db 数据库连接
 * @param {Object} record 要清理的记录
 * @returns {Promise<void>}
 */
async function cleanupDatabaseRecord(db, record) {
	try {
		if (record.media_group_id) {
			// 如果是媒体组，删除整个媒体组的记录
			const deleteResult = await db.prepare(`
				DELETE FROM tg_log_forwarded_submissions 
				WHERE media_group_id = ?
			`).bind(record.media_group_id).run();
			
			Logger.debug(`清理媒体组记录: ${record.media_group_id}, 影响行数: ${deleteResult.changes || 0}`);
		} else {
			// 删除单个记录
			const deleteResult = await db.prepare(`
				DELETE FROM tg_log_forwarded_submissions 
				WHERE id = ?
			`).bind(record.id).run();
			
			Logger.debug(`清理单个记录: ${record.id}, 影响行数: ${deleteResult.changes || 0}`);
		}
		
	} catch (error) {
		Logger.error('清理数据库记录失败:', error);
		throw error;
	}
}

//#endregion 