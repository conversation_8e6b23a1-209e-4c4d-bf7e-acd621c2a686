/**
 * 群组配置管理工具函数
 * 支持群组配置的CRUD操作和举报功能管理
 */

import Logger from './logger.js';

// #region 🏗️ 群组配置数据库操作

/**
 * 获取群组配置
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @returns {Promise<Object|null>} 群组配置对象
 */
export async function getGroupConfig(env, chatId) {
	try {
		const result = await env.DB.prepare(`
			SELECT * FROM group_configs WHERE chat_id = ?
		`).bind(chatId).first();
		
		if (result && result.report_target_groups) {
			// 解析JSON字段
			try {
				result.report_target_groups = JSON.parse(result.report_target_groups);
			} catch (error) {
				Logger.warn('解析举报目标群组JSON失败:', error);
				result.report_target_groups = [];
			}
		}
		
		return result;
	} catch (error) {
		Logger.error('获取群组配置失败:', error);
		return null;
	}
}

/**
 * 创建或更新群组配置
 * @param {Object} env 环境变量
 * @param {Object} config 配置对象
 * @returns {Promise<boolean>} 是否成功
 */
export async function createOrUpdateGroupConfig(env, config) {
	try {
		const { 
			chat_id, 
			chat_title, 
			report_mode = 'disabled', 
			report_target_groups = [],
			new_member_verification = true,  // ✅ 改为true，匹配数据库默认值
			cloud_filter_enabled = true,     // ✅ 改为true，匹配数据库默认值
			auto_forward_enabled = true      // ✅ 改为true，匹配数据库默认值
		} = config;
		
		// 确保report_target_groups是JSON字符串
		const targetGroupsJson = Array.isArray(report_target_groups) 
			? JSON.stringify(report_target_groups)
			: report_target_groups;
		
		const result = await env.DB.prepare(`
			INSERT INTO group_configs (
				chat_id, chat_title, report_mode, report_target_groups,
				new_member_verification, cloud_filter_enabled, auto_forward_enabled,
				updated_at
			)
			VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
			ON CONFLICT(chat_id) DO UPDATE SET
				chat_title = excluded.chat_title,
				report_mode = excluded.report_mode,
				report_target_groups = excluded.report_target_groups,
				new_member_verification = excluded.new_member_verification,
				cloud_filter_enabled = excluded.cloud_filter_enabled,
				auto_forward_enabled = excluded.auto_forward_enabled,
				updated_at = CURRENT_TIMESTAMP
		`).bind(
			chat_id, chat_title, report_mode, targetGroupsJson,
			new_member_verification, cloud_filter_enabled, auto_forward_enabled
		).run();
		
		Logger.debug('群组配置更新成功:', { chat_id, result: result.success });
		return result.success;
	} catch (error) {
		Logger.error('创建或更新群组配置失败:', error);
		return false;
	}
}

/**
 * 获取所有已配置的群组列表
 * @param {Object} env 环境变量
 * @returns {Promise<Array>} 群组配置列表
 */
export async function getAllGroupConfigs(env) {
	try {
		const result = await env.DB.prepare(`
			SELECT chat_id, chat_title, report_mode, 
				   new_member_verification, cloud_filter_enabled, auto_forward_enabled,
				   created_at, updated_at
			FROM group_configs 
			ORDER BY updated_at DESC
		`).all();
		
		return result.results || [];
	} catch (error) {
		Logger.error('获取群组配置列表失败:', error);
		return [];
	}
}

// #endregion 🏗️ 群组配置数据库操作

// #region 📢 举报目标群组管理

/**
 * 获取举报目标群组列表
 * @param {Object} env 环境变量
 * @param {string} chatId 源群组ID
 * @returns {Promise<Array>} 目标群组列表
 */
export async function getReportTargets(env, chatId) {
	try {
		const config = await getGroupConfig(env, chatId);
		if (!config || !config.report_target_groups) {
			return [];
		}
		
		// 过滤出活跃的目标群组
		return config.report_target_groups.filter(target => target.is_active !== false);
	} catch (error) {
		Logger.error('获取举报目标群组失败:', error);
		return [];
	}
}

/**
 * 添加举报目标群组
 * @param {Object} env 环境变量
 * @param {string} sourceChatId 源群组ID
 * @param {Object} targetGroup 目标群组信息 {chat_id, title}
 * @returns {Promise<boolean>} 是否成功
 */
export async function addReportTarget(env, sourceChatId, targetGroup) {
	try {
		const config = await getGroupConfig(env, sourceChatId);
		let targets = config?.report_target_groups || [];
		
		// 检查是否已存在
		const existingIndex = targets.findIndex(t => t.chat_id === targetGroup.chat_id);
		if (existingIndex !== -1) {
			// 如果存在但被禁用，重新启用
			targets[existingIndex].is_active = true;
			targets[existingIndex].title = targetGroup.title; // 更新标题
		} else {
			// 添加新的目标群组
			targets.push({
				chat_id: targetGroup.chat_id,
				title: targetGroup.title,
				is_active: true,
				added_at: new Date().toISOString()
			});
		}
		
		// 更新配置
		return await createOrUpdateGroupConfig(env, {
			...config,
			chat_id: sourceChatId,
			report_target_groups: targets
		});
	} catch (error) {
		Logger.error('添加举报目标群组失败:', error);
		return false;
	}
}

/**
 * 移除举报目标群组
 * @param {Object} env 环境变量
 * @param {string} sourceChatId 源群组ID
 * @param {string} targetChatId 目标群组ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function removeReportTarget(env, sourceChatId, targetChatId) {
	try {
		const config = await getGroupConfig(env, sourceChatId);
		if (!config || !config.report_target_groups) {
			return true; // 没有配置则认为成功
		}
		
		// 将目标群组标记为非活跃状态（软删除）
		const targets = config.report_target_groups.map(target => {
			if (target.chat_id === targetChatId) {
				return { ...target, is_active: false };
			}
			return target;
		});
		
		// 更新配置
		return await createOrUpdateGroupConfig(env, {
			...config,
			chat_id: sourceChatId,
			report_target_groups: targets
		});
	} catch (error) {
		Logger.error('移除举报目标群组失败:', error);
		return false;
	}
}

// #endregion 📢 举报目标群组管理

// #region 📋 举报记录管理

/**
 * 创建举报记录
 * @param {Object} env 环境变量
 * @param {Object} reportData 举报数据
 * @returns {Promise<number|null>} 举报记录ID
 */
export async function createReportLog(env, reportData) {
	try {
		const {
			chat_id,
			reporter_id,
			reporter_name,
			target_message_id,
			report_reason,
			report_content,
			report_mode,
			notification_targets
		} = reportData;
		
		const result = await env.DB.prepare(`
			INSERT INTO report_logs (
				chat_id, reporter_id, reporter_name, target_message_id,
				report_reason, report_content, report_mode, notification_targets
			)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		`).bind(
			chat_id, reporter_id, reporter_name, target_message_id,
			report_reason, report_content, report_mode,
			JSON.stringify(notification_targets)
		).run();
		
		if (result.success) {
			Logger.debug('举报记录创建成功:', { reportId: result.meta.last_row_id });
			return result.meta.last_row_id;
		}
		
		return null;
	} catch (error) {
		Logger.error('创建举报记录失败:', error);
		return null;
	}
}

/**
 * 更新举报通知状态
 * @param {Object} env 环境变量
 * @param {number} reportId 举报记录ID
 * @param {string} status 通知状态
 * @returns {Promise<boolean>} 是否成功
 */
export async function updateReportNotificationStatus(env, reportId, status) {
	try {
		const result = await env.DB.prepare(`
			UPDATE report_logs 
			SET notification_status = ?
			WHERE id = ?
		`).bind(status, reportId).run();
		
		return result.success;
	} catch (error) {
		Logger.error('更新举报通知状态失败:', error);
		return false;
	}
}

/**
 * 更新举报处理状态
 * @param {Object} env 环境变量
 * @param {number} reportId 举报记录ID
 * @param {Object} handleData 处理数据
 * @returns {Promise<boolean>} 是否成功
 */
export async function updateReportHandleStatus(env, reportId, handleData) {
	try {
		const {
			handle_status,
			handled_by,
			handled_by_name,
			handle_action,
			handle_comment
		} = handleData;
		
		const result = await env.DB.prepare(`
			UPDATE report_logs 
			SET handle_status = ?, handled_by = ?, handled_by_name = ?,
				handle_action = ?, handle_comment = ?, handled_at = CURRENT_TIMESTAMP
			WHERE id = ?
		`).bind(
			handle_status, handled_by, handled_by_name,
			handle_action, handle_comment, reportId
		).run();
		
		return result.success;
	} catch (error) {
		Logger.error('更新举报处理状态失败:', error);
		return false;
	}
}

/**
 * 获取举报统计信息
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID（可选）
 * @param {number} days 统计天数
 * @returns {Promise<Object>} 统计信息
 */
export async function getReportStats(env, chatId = null, days = 30) {
	try {
		const dateFilter = `datetime('now', '-${days} days')`;
		let query = `
			SELECT 
				COUNT(*) as total_reports,
				COUNT(CASE WHEN handle_status = 'pending' THEN 1 END) as pending_reports,
				COUNT(CASE WHEN handle_status = 'handled' THEN 1 END) as handled_reports,
				COUNT(CASE WHEN handle_status = 'ignored' THEN 1 END) as ignored_reports
			FROM report_logs 
			WHERE created_at >= ${dateFilter}
		`;
		
		if (chatId) {
			query += ` AND chat_id = ?`;
		}
		
		const stmt = chatId 
			? env.DB.prepare(query).bind(chatId)
			: env.DB.prepare(query);
			
		const result = await stmt.first();
		
		return {
			total_reports: result?.total_reports || 0,
			pending_reports: result?.pending_reports || 0,
			handled_reports: result?.handled_reports || 0,
			ignored_reports: result?.ignored_reports || 0,
			days
		};
	} catch (error) {
		Logger.error('获取举报统计失败:', error);
		return {
			total_reports: 0,
			pending_reports: 0,
			handled_reports: 0,
			ignored_reports: 0,
			days
		};
	}
}

// #endregion 📋 举报记录管理

// #region 🔧 辅助函数

/**
 * 验证群组配置数据
 * @param {Object} config 配置对象
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
 */
export function validateGroupConfig(config) {
	const errors = [];
	
	if (!config.chat_id) {
		errors.push('群组ID不能为空');
	}
	
	if (!['disabled', 'private', 'group'].includes(config.report_mode)) {
		errors.push('举报模式无效');
	}
	
	if (config.report_mode === 'group' && (!config.report_target_groups || config.report_target_groups.length === 0)) {
		errors.push('群组举报模式需要至少配置一个目标群组');
	}
	
	// 验证群规配置（如果提供）
	if (config.rules && Array.isArray(config.rules)) {
		if (config.rules.length > 10) {
			errors.push('群规链接数量不能超过10个');
		}
		
		for (let i = 0; i < config.rules.length; i++) {
			const rule = config.rules[i];
			
			if (!rule.url || !rule.url.trim()) {
				errors.push(`第${i + 1}个群规的链接为空`);
				continue;
			}
			
			// 验证Telegram URL格式
			try {
				const urlObj = new URL(rule.url.trim());
				if (urlObj.hostname !== 't.me' || urlObj.pathname.length <= 1) {
					errors.push(`第${i + 1}个群规的链接格式无效`);
				}
			} catch (error) {
				errors.push(`第${i + 1}个群规的链接格式无效`);
			}
			
			// 验证标题长度
			if (rule.title && rule.title.length > 50) {
				errors.push(`第${i + 1}个群规的标题过长（最多50字符）`);
			}
		}
	}
	
	return {
		valid: errors.length === 0,
		errors
	};
}

/**
 * 获取举报处理动作的显示名称
 * @param {string} action 处理动作
 * @returns {string} 显示名称
 */
export function getHandleActionDisplayName(action) {
	const actionMap = {
		'warned': '⚠️ 已警告',
		'muted': '🔇 已禁言', 
		'banned': '🚫 已封禁',
		'dismissed': '✅ 已驳回',
		'handled': '✅ 已处理',
		'ignored': '👁️ 已忽略',
		'escalated': '🆙 已升级'
	};
	
	return actionMap[action] || action;
}

// #endregion 🔧 辅助函数 