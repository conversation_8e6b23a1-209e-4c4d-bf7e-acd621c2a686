/**
 * 群组权限管理工具类
 * 处理群组管理员权限验证、层级管理等功能
 */

import Logger from './logger.js';
import { sendTelegramRequest } from './telegramApi.js';

// #region 🔍 权限查询

/**
 * 获取用户在指定群组的权限级别
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<string|null>} 权限级别：'super_admin'|'admin'|null
 */
export async function getUserPermissionLevel(env, chatId, userId) {
    try {
        const result = await env.DB.prepare(`
            SELECT permission_level 
            FROM group_admins 
            WHERE chat_id = ? AND user_id = ?
        `).bind(chatId, userId).first();
        
        return result?.permission_level || null;
    } catch (error) {
        Logger.error('查询用户权限级别失败:', error);
        return null;
    }
}

/**
 * 检查用户是否为指定群组的超级管理员
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否为超级管理员
 */
export async function isGroupSuperAdmin(env, chatId, userId) {
    const level = await getUserPermissionLevel(env, chatId, userId);
    return level === 'super_admin';
}

/**
 * 检查用户是否为指定群组的管理员（包括普通管理员和超级管理员）
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否为管理员
 */
export async function isGroupAdmin(env, chatId, userId) {
    const level = await getUserPermissionLevel(env, chatId, userId);
    return level === 'admin' || level === 'super_admin';
}

/**
 * 获取用户有管理权限的所有群组
 * @param {Object} env 环境变量
 * @param {number} userId 用户ID
 * @returns {Promise<Array>} 群组列表
 */
export async function getUserManagedGroups(env, userId) {
    try {
        const result = await env.DB.prepare(`
            SELECT 
                ga.chat_id,
                ga.permission_level,
                gc.chat_title,
                ga.created_at
            FROM group_admins ga
            LEFT JOIN group_configs gc ON ga.chat_id = gc.chat_id
            WHERE ga.user_id = ?
            ORDER BY ga.permission_level DESC, ga.created_at DESC
        `).bind(userId).all();
        
        return result.results || [];
    } catch (error) {
        Logger.error('获取用户管理群组失败:', error);
        return [];
    }
}

// #endregion 🔍 权限查询

// #region 🛡️ Telegram权限验证

/**
 * 验证用户在Telegram群组中的管理权限
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<Object>} 验证结果 {isCreator: boolean, canAddAdmins: boolean, isAdmin: boolean}
 */
export async function verifyTelegramAdminPermission(env, chatId, userId) {
    try {
        Logger.debug('验证Telegram群组管理权限:', { chatId, userId });
        
        // 获取用户在群组中的信息
        const memberInfo = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getChatMember`,
            {
                chat_id: chatId,
                user_id: userId
            }
        );
        
        if (!memberInfo.ok) {
            Logger.warn('获取群组成员信息失败:', {
                chatId,
                userId,
                error: memberInfo.error_code,
                description: memberInfo.description
            });
            return { 
                isCreator: false, 
                canAddAdmins: false, 
                isAdmin: false,
                error: memberInfo.description || '无法获取群组成员信息'
            };
        }
        
        const member = memberInfo.result;
        const status = member.status;
        
        // 检查用户状态
        const isCreator = status === 'creator';
        const isAdmin = status === 'administrator';
        
        // 检查管理员权限
        let canAddAdmins = false;
        if (isCreator) {
            canAddAdmins = true; // 创建者默认有所有权限
        } else if (isAdmin && member.can_promote_members) {
            canAddAdmins = true; // 管理员且有添加管理员权限
        }
        
        Logger.debug('Telegram权限验证结果:', {
            chatId,
            userId,
            status,
            isCreator,
            isAdmin,
            canAddAdmins
        });
        
        return {
            isCreator,
            canAddAdmins,
            isAdmin: isCreator || isAdmin
        };
        
    } catch (error) {
        Logger.error('验证Telegram管理权限失败:', error);
        return { isCreator: false, canAddAdmins: false, isAdmin: false };
    }
}

/**
 * 验证bot在群组中的管理权限
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @returns {Promise<boolean>} bot是否为群组管理员
 */
export async function verifyBotAdminPermission(env, chatId) {
    try {
        Logger.debug('验证bot管理权限:', { chatId });
        
        // 获取bot自己的信息
        const botInfo = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getMe`,
            {}
        );
        
        if (!botInfo.ok) {
            Logger.error('获取bot信息失败:', botInfo);
            return false;
        }
        
        // 获取bot在群组中的成员信息
        const memberInfo = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getChatMember`,
            {
                chat_id: chatId,
                user_id: botInfo.result.id
            }
        );
        
        if (!memberInfo.ok) {
            Logger.warn('获取bot群组成员信息失败:', memberInfo);
            return false;
        }
        
        const member = memberInfo.result;
        const isAdmin = member.status === 'administrator' || member.status === 'creator';
        
        Logger.debug('Bot权限验证结果:', {
            chatId,
            botId: botInfo.result.id,
            status: member.status,
            isAdmin
        });
        
        return isAdmin;
        
    } catch (error) {
        Logger.error('验证bot管理权限失败:', error);
        return false;
    }
}

// #endregion 🛡️ Telegram权限验证

// #region 👥 管理员管理

/**
 * 添加群组超级管理员（群组创建者或有权限的管理员）
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @param {string} userName 用户名
 * @returns {Promise<boolean>} 是否成功
 */
export async function addGroupSuperAdmin(env, chatId, userId, userName) {
    try {
        const result = await env.DB.prepare(`
            INSERT INTO group_admins (
                chat_id, user_id, user_name, permission_level
            ) VALUES (?, ?, ?, 'super_admin')
            ON CONFLICT(chat_id, user_id) DO UPDATE SET
                permission_level = 'super_admin',
                user_name = excluded.user_name,
                updated_at = CURRENT_TIMESTAMP
        `).bind(chatId, userId, userName).run();
        
        Logger.info('添加群组超级管理员成功:', {
            chatId,
            userId,
            userName,
            success: result.success
        });
        
        return result.success;
    } catch (error) {
        Logger.error('添加群组超级管理员失败:', error);
        return false;
    }
}

/**
 * 添加群组普通管理员（由超级管理员添加）
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @param {string} userName 用户名
 * @param {number} grantedBy 授权人用户ID
 * @param {string} grantedByName 授权人姓名
 * @returns {Promise<boolean>} 是否成功
 */
export async function addGroupAdmin(env, chatId, userId, userName, grantedBy, grantedByName) {
    try {
        // 检查授权人是否为超级管理员
        const granterLevel = await getUserPermissionLevel(env, chatId, grantedBy);
        if (granterLevel !== 'super_admin') {
            Logger.warn('非超级管理员尝试添加普通管理员:', {
                chatId,
                grantedBy,
                granterLevel
            });
            return false;
        }
        
        const result = await env.DB.prepare(`
            INSERT INTO group_admins (
                chat_id, user_id, user_name, permission_level, granted_by, granted_by_name
            ) VALUES (?, ?, ?, 'admin', ?, ?)
            ON CONFLICT(chat_id, user_id) DO UPDATE SET
                permission_level = 'admin',
                user_name = excluded.user_name,
                granted_by = excluded.granted_by,
                granted_by_name = excluded.granted_by_name,
                updated_at = CURRENT_TIMESTAMP
        `).bind(chatId, userId, userName, grantedBy, grantedByName).run();
        
        Logger.info('添加群组普通管理员成功:', {
            chatId,
            userId,
            userName,
            grantedBy,
            grantedByName,
            success: result.success
        });
        
        return result.success;
    } catch (error) {
        Logger.error('添加群组普通管理员失败:', error);
        return false;
    }
}

/**
 * 删除群组管理员
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @param {number} operatorId 操作者用户ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function removeGroupAdmin(env, chatId, userId, operatorId) {
    try {
        // 检查操作权限
        const operatorLevel = await getUserPermissionLevel(env, chatId, operatorId);
        const targetLevel = await getUserPermissionLevel(env, chatId, userId);
        
        // 只有超级管理员才能删除管理员
        if (operatorLevel !== 'super_admin') {
            Logger.warn('非超级管理员尝试删除管理员:', {
                chatId,
                operatorId,
                userId,
                operatorLevel
            });
            return false;
        }
        
        // 不能删除其他超级管理员
        if (targetLevel === 'super_admin') {
            Logger.warn('尝试删除超级管理员:', {
                chatId,
                operatorId,
                userId,
                targetLevel
            });
            return false;
        }
        
        const result = await env.DB.prepare(`
            DELETE FROM group_admins 
            WHERE chat_id = ? AND user_id = ?
        `).bind(chatId, userId).run();
        
        Logger.info('删除群组管理员成功:', {
            chatId,
            userId,
            operatorId,
            success: result.success
        });
        
        return result.success;
    } catch (error) {
        Logger.error('删除群组管理员失败:', error);
        return false;
    }
}

/**
 * 获取群组所有管理员列表
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @returns {Promise<Array>} 管理员列表
 */
export async function getGroupAdmins(env, chatId) {
    try {
        const result = await env.DB.prepare(`
            SELECT 
                user_id,
                user_name,
                permission_level,
                granted_by,
                granted_by_name,
                created_at
            FROM group_admins 
            WHERE chat_id = ?
            ORDER BY 
                CASE permission_level 
                    WHEN 'super_admin' THEN 1 
                    WHEN 'admin' THEN 2 
                    ELSE 3 
                END,
                created_at ASC
        `).bind(chatId).all();
        
        return result.results || [];
    } catch (error) {
        Logger.error('获取群组管理员列表失败:', error);
        return [];
    }
}

// #endregion 👥 管理员管理

// #region 🔧 辅助函数

/**
 * 获取群组信息
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @returns {Promise<Object|null>} 群组信息
 */
export async function getChatInfo(env, chatId) {
    try {
        const chatInfo = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getChat`,
            { chat_id: chatId }
        );
        
        if (chatInfo.ok) {
            return chatInfo.result;
        } else {
            Logger.warn('获取群组信息失败:', chatInfo);
            return null;
        }
    } catch (error) {
        Logger.error('获取群组信息时出错:', error);
        return null;
    }
}

/**
 * 验证群组ID格式
 * @param {string} chatId 群组ID
 * @returns {boolean} 格式是否正确
 */
export function isValidGroupId(chatId) {
    // Telegram群组ID格式：-100开头的负数
    return /^-100\d+$/.test(chatId);
}

// #endregion 🔧 辅助函数 