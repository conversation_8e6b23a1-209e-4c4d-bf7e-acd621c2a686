/**
 * 群规管理工具函数
 * 处理群规配置的设置、解析和发送
 */

import Logger from './logger.js';
import { sendTelegramRequest } from './telegramApi.js';

// #region 🔗 URL解析和验证相关

/**
 * 解析Telegram消息URL
 * @param {string} url Telegram消息URL
 * @returns {Object|null} 解析结果 {sourceChat, messageId, isChannel, originalUrl}
 */
export function parseTelegramUrl(url) {
	try {
		// 支持的URL格式：
		// https://t.me/c/1143091022/1 (私有群组/频道)
		// https://t.me/channel_username/1 (公开频道)
		
		const urlObj = new URL(url);
		if (urlObj.hostname !== 't.me') {
			return null;
		}
		
		const pathParts = urlObj.pathname.split('/').filter(part => part);
		
		if (pathParts.length >= 3 && pathParts[0] === 'c') {
			// 私有群组/频道格式: /c/1143091022/1
			const sourceChat = `-100${pathParts[1]}`;
			const messageId = parseInt(pathParts[2]);
			
			if (isNaN(messageId)) {
				return null;
			}
			
			return {
				sourceChat,
				messageId,
				isChannel: true,
				originalUrl: url
			};
		} else if (pathParts.length >= 2) {
			// 公开频道格式: /channel_username/1
			const channelUsername = pathParts[0];
			const messageId = parseInt(pathParts[1]);
			
			if (isNaN(messageId)) {
				return null;
			}
			
			return {
				sourceChat: `@${channelUsername}`,
				messageId,
				isChannel: true,
				originalUrl: url
			};
		}
		
		return null;
	} catch (error) {
		Logger.error('解析Telegram URL失败:', error);
		return null;
	}
}

/**
 * 验证是否为有效的Telegram URL
 * @param {string} url URL字符串
 * @returns {boolean} 是否为有效的Telegram URL
 */
export function isValidTelegramUrl(url) {
	return parseTelegramUrl(url) !== null;
}

/**
 * 验证bot是否能访问指定消息
 * @param {Object} env 环境变量
 * @param {string} chatId 聊天ID
 * @param {number} messageId 消息ID
 * @returns {Promise<Object>} 验证结果 {canAccess: boolean, chatInfo?: Object, error?: string}
 */
export async function verifyMessageAccess(env, chatId, messageId) {
	try {
		// 首先尝试获取聊天信息
		const chatResponse = await sendTelegramRequest(
			env,
			`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getChat`,
			{ chat_id: chatId }
		);
		
		if (!chatResponse.ok) {
			return {
				canAccess: false,
				error: `无法访问聊天: ${chatResponse.description}`
			};
		}
		
		// 尝试转发消息来测试访问权限（立即删除）
		const forwardResponse = await sendTelegramRequest(
			env,
			`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`,
			{
				chat_id: chatId, // 转发到同一个聊天（测试访问权限）
				from_chat_id: chatId,
				message_id: messageId,
				disable_notification: true
			}
		);
		
		if (forwardResponse.ok) {
			// 立即删除测试转发的消息
			await sendTelegramRequest(
				env,
				`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
				{
					chat_id: chatId,
					message_id: forwardResponse.result.message_id
				}
			);
			
			return {
				canAccess: true,
				chatInfo: chatResponse.result
			};
		} else {
			return {
				canAccess: false,
				error: `无法访问消息: ${forwardResponse.description}`
			};
		}
	} catch (error) {
		Logger.error('验证消息访问权限失败:', error);
		return {
			canAccess: false,
			error: error.message
		};
	}
}

// #endregion 🔗 URL解析和验证相关

// #region 💾 群规数据管理

/**
 * 保存群规配置（带访问验证）
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {Array} ruleUrls 群规URL数组 [{url: string, title?: string}]
 * @param {number} setByUserId 设置人用户ID
 * @param {string} setByUserName 设置人姓名
 * @param {boolean} skipVerification 是否跳过访问验证（默认false）
 * @returns {Promise<Object>} 保存结果 {success: boolean, results?: Array, errors?: Array}
 */
export async function saveGroupRules(env, chatId, ruleUrls, setByUserId, setByUserName, skipVerification = false) {
	try {
		const results = [];
		const errors = [];
		
		// 处理并验证每个规则
		for (let i = 0; i < ruleUrls.length; i++) {
			const rule = ruleUrls[i];
			const ruleResult = {
				url: rule.url,
				title: rule.title || '群规',
				index: i + 1
			};
			
			if (!skipVerification) {
				// 解析URL
				const parsedUrl = parseTelegramUrl(rule.url);
				if (parsedUrl) {
					// 验证访问权限
					const accessResult = await verifyMessageAccess(env, parsedUrl.sourceChat, parsedUrl.messageId);
					ruleResult.canAccess = accessResult.canAccess;
					ruleResult.accessError = accessResult.error;
					ruleResult.chatInfo = accessResult.chatInfo;
				} else {
					ruleResult.canAccess = false;
					ruleResult.accessError = 'URL格式无效';
				}
			} else {
				ruleResult.canAccess = true; // 跳过验证时假设可访问
			}
			
			results.push(ruleResult);
			
			if (!ruleResult.canAccess) {
				errors.push(`第${i + 1}个群规无法访问: ${ruleResult.accessError}`);
			}
		}
		
		// 构建保存数据（包含验证状态）
		const rulesData = results.map(result => ({
			url: result.url,
			title: result.title,
			canAccess: result.canAccess,
			accessError: result.accessError,
			chatTitle: result.chatInfo?.title
		}));
		
		// 先删除现有的群规配置
		await env.DB.prepare(`
			UPDATE group_rules
			SET is_active = FALSE
			WHERE chat_id = ?
		`).bind(chatId).run();

		let result = { success: true };

		// 只有当有群规时才插入新记录
		if (rulesData.length > 0) {
			result = await env.DB.prepare(`
				INSERT INTO group_rules (
					chat_id, rule_urls, set_by_user_id, set_by_user_name
				) VALUES (?, ?, ?, ?)
			`).bind(
				chatId,
				JSON.stringify(rulesData),
				setByUserId,
				setByUserName
			).run();
		}
		
		const accessibleCount = results.filter(r => r.canAccess).length;
		const totalCount = results.length;
		
		Logger.info('群规配置保存完成:', {
			chatId,
			totalCount,
			accessibleCount,
			hasErrors: errors.length > 0,
			setBy: setByUserName
		});
		
		return {
			success: result.success,
			results,
			errors,
			summary: {
				total: totalCount,
				accessible: accessibleCount,
				failed: totalCount - accessibleCount
			}
		};
	} catch (error) {
		Logger.error('保存群规配置失败:', error);
		return {
			success: false,
			errors: [error.message]
		};
	}
}

/**
 * 获取群规配置
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @returns {Promise<Object|null>} 群规配置
 */
export async function getGroupRules(env, chatId) {
	try {
		const result = await env.DB.prepare(`
			SELECT * FROM group_rules 
			WHERE chat_id = ? AND is_active = TRUE 
			ORDER BY created_at DESC 
			LIMIT 1
		`).bind(chatId).first();
		
		if (result && result.rule_urls) {
			try {
				result.rule_urls = JSON.parse(result.rule_urls);
			} catch (error) {
				Logger.warn('解析群规URLs JSON失败:', error);
				result.rule_urls = [];
			}
		}
		
		return result;
	} catch (error) {
		Logger.error('获取群规配置失败:', error);
		return null;
	}
}

// #endregion 💾 群规数据管理

// #region 📤 群规发送功能

/**
 * 发送群规给新成员
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @param {string} userFirstName 用户名
 * @returns {Promise<boolean>} 是否成功发送
 */
export async function sendRulesToNewMember(env, chatId, userId, userFirstName) {
	try {
		// 获取群规配置
		const rulesConfig = await getGroupRules(env, chatId);
		
		if (!rulesConfig || !rulesConfig.rule_urls || rulesConfig.rule_urls.length === 0) {
			Logger.debug('该群组没有配置群规，跳过发送');
			return { success: false, reason: 'no_rules' };
		}
		
		// 过滤出可访问的群规（如果有访问验证信息的话）
		const accessibleRules = rulesConfig.rule_urls.filter(rule => {
			// 如果没有访问验证信息，或者明确标记为可访问，则包含该规则
			return rule.canAccess !== false;
		});
		
		if (accessibleRules.length === 0) {
			Logger.warn('所有群规链接都无法访问，跳过发送:', chatId);
			return { success: false, reason: 'no_accessible_rules' };
		}
		
		// 构建群规链接文本
		const ruleLinks = accessibleRules.map((rule, index) => {
			let title = rule.title;
			
			// 如果存在多条群规且标题都是默认的"群规"，则加上序号
			if (accessibleRules.length > 1 && accessibleRules.every(r => r.title === '群规')) {
				title = `群规${index + 1}`;
			}
			
			return `[${title}](${rule.url})`;
		});
		
		// 构建欢迎消息
		const welcomeText = `[${userFirstName}](tg://user?id=${userId}) 欢迎加入\n请遵守群规: ${ruleLinks.join(' ')}`;
		
		// 发送消息
		const response = await sendTelegramRequest(
			env,
			`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
			{
				chat_id: chatId,
				text: welcomeText,
				parse_mode: 'Markdown',
				disable_web_page_preview: true
			}
		);
		
		if (response.ok) {
			Logger.info('群规发送成功:', {
				chatId,
				userId,
				userFirstName,
				totalRules: rulesConfig.rule_urls.length,
				accessibleRules: accessibleRules.length,
				messageId: response.result.message_id
			});
			return {
				success: true,
				messageId: response.result.message_id
			};
		} else {
			Logger.error('群规发送失败:', response);
			return {
				success: false,
				error: response.description
			};
		}
	} catch (error) {
		Logger.error('发送群规时出错:', error);
		return { success: false, error: error.message };
	}
}

/**
 * 格式化群规显示文本（用于命令回复）
 * @param {Object} rulesConfig 群规配置
 * @returns {string} 格式化后的文本
 */
export function formatRulesDisplay(rulesConfig) {
	if (!rulesConfig || !rulesConfig.rule_urls || rulesConfig.rule_urls.length === 0) {
		return '❌ 本群暂未配置群规';
	}
	
	const ruleUrls = rulesConfig.rule_urls;
	const rulesList = ruleUrls.map((rule, index) => {
		let title = rule.title;
		
		// 如果存在多条群规且标题都是默认的"群规"，则加上序号
		if (ruleUrls.length > 1 && ruleUrls.every(r => r.title === '群规')) {
			title = `群规${index + 1}`;
		}
		
		// 访问状态标识
		let statusIcon = '';
		if (rule.canAccess === true) {
			statusIcon = ' ✅';
		} else if (rule.canAccess === false) {
			statusIcon = ' ❌';
		}
		
		return `${index + 1}. [${title}](${rule.url})${statusIcon}`;
	}).join('\n');
	
	// 统计可访问的规则数量
	const accessibleCount = ruleUrls.filter(rule => rule.canAccess !== false).length;
	const totalCount = ruleUrls.length;
	let statusSummary = '';
	
	if (ruleUrls.some(rule => rule.canAccess !== undefined)) {
		const failedCount = totalCount - accessibleCount;
		if (failedCount > 0) {
			statusSummary = `\n\n📊 访问状态: ${accessibleCount}/${totalCount} 可访问`;
		} else {
			statusSummary = '\n\n📊 访问状态: 全部可访问';
		}
	}
	
	return `📋 当前群规配置:\n\n${rulesList}${statusSummary}\n\n👤 设置人: ${rulesConfig.set_by_user_name}\n⏰ 设置时间: ${new Date(rulesConfig.created_at).toLocaleString()}`;
}

// #endregion 📤 群规发送功能

// #region 🔧 辅助函数

/**
 * 验证群规URL列表
 * @param {Array} rules 群规数组 [{url: string, title?: string}]
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
 */
export function validateRuleUrls(rules) {
	const errors = [];
	
	if (!rules || rules.length === 0) {
		errors.push('至少需要提供一个群规链接');
		return { valid: false, errors };
	}
	
	if (rules.length > 10) {
		errors.push('群规链接数量不能超过10个');
		return { valid: false, errors };
	}
	
	for (let i = 0; i < rules.length; i++) {
		const rule = rules[i];
		
		if (!rule.url || !rule.url.trim()) {
			errors.push(`第${i + 1}个规则的链接为空`);
			continue;
		}
		
		if (!isValidTelegramUrl(rule.url.trim())) {
			errors.push(`第${i + 1}个规则的链接格式无效: ${rule.url}`);
			continue;
		}
		
		// 标题长度验证
		if (rule.title && rule.title.length > 50) {
			errors.push(`第${i + 1}个规则的标题过长（最多50字符）`);
		}
	}
	
	return {
		valid: errors.length === 0,
		errors
	};
}

// #endregion 🔧 辅助函数 