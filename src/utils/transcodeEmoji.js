/**
 * Emoji转码工具
 * 摘自 https://blog.csdn.net/kingAn123/article/details/99228290
 */

// 由于安全原因, 数据库返回的一些字符是经过转义的, 需要翻译回来
const changeList = {
  '&': '&amp;',
  '>': '&gt;',
  '<': '&lt;',
  "'": '&gt;',
  '"': '&quot;',
};

function decodeXML(origin) {
  let result = origin;
  for (const key in changeList) {
    if (changeList.hasOwnProperty(key)) {
      const item = changeList[key];
      result = result.replace(new RegExp(item, 'gm'), key);
    }
  }
  return result;
}

// emoji转字符
// 把utf16的emoji表情字符进行转码成八进制的字符
export function utf16toEntities(str) {
  // 检测utf16字符正则
  const patt = /[\ud800-\udbff][\udc00-\udfff]/g;
  return str.replace(patt, function (char) {
    let H, L, code;
    if (char.length === 2) {
      H = char.charCodeAt(0); // 取出高位
      L = char.charCodeAt(1); // 取出低位
      code = (H - 0xd800) * 0x400 + 0x10000 + L - 0xdc00; // 转换算法
      return '&#' + code + ';';
    } else {
      return char;
    }
  });
}

// 字符转emoji
// 将编码后的八进制的emoji表情重新解码成十六进制的表情字符
export function entitiesToUtf16(str) {
  str = decodeXML(str);
  return str.replace(/&#(\d+);/g, function (match, dec) {
    let H = Math.floor((dec - 0x10000) / 0x400) + 0xd800;
    let L = (Math.floor(dec - 0x10000) % 0x400) + 0xdc00;
    return String.fromCharCode(H, L);
  });
}

export const transcodeEmoji = {
  utf16toEntities,
  entitiesToUtf16
};