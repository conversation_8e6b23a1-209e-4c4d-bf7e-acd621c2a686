/**
 * 字符串安全处理工具
 * 处理emoji和特殊字符的编码问题，确保UTF-8兼容性
 * 
 * 使用指南：
 * - 所有用户输入的文本都应该经过 safeString() 处理
 * - 数据库存储前使用相应的 safe 函数
 * - 显示文本时使用 safe 函数确保格式正确
 * - 添加新的格式化规则时，请在下方扩展区域添加
 */

import { transcodeEmoji } from './transcodeEmoji.js';

/**
 * 安全地处理可能包含emoji的字符串，确保UTF-8编码
 * @param {string} str 输入字符串
 * @returns {string} 处理后的安全字符串
 */
export function safeString(str) {
	if (!str || typeof str !== 'string') {
		return '';
	}
	
	// 移除可能导致编码问题的控制字符
	let result = str.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
	
	// 处理emoji，确保正确编码
	try {
		// 先转换为实体，再转换回来，确保编码正确
		result = transcodeEmoji.entitiesToUtf16(transcodeEmoji.utf16toEntities(result));
	} catch (error) {
		console.warn('Emoji transcoding failed:', error);
		// 如果转换失败，移除可能有问题的emoji
		result = result.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');
	}
	
	return result;
}

/**
 * 安全地截断字符串，避免在emoji中间截断
 * @param {string} str 输入字符串
 * @param {number} maxLength 最大长度
 * @param {string} ellipsis 省略号，默认为'...'
 * @returns {string} 截断后的字符串
 */
export function safeTruncate(str, maxLength, ellipsis = '...') {
	if (!str || typeof str !== 'string') {
		return '';
	}
	
	// 先确保字符串安全
	const safeStr = safeString(str);
	
	if (safeStr.length <= maxLength) {
		return safeStr;
	}
	
	// 使用Array.from来正确处理Unicode字符
	const chars = Array.from(safeStr);
	if (chars.length <= maxLength) {
		return safeStr;
	}
	
	// 截断并添加省略号
	const truncated = chars.slice(0, maxLength - ellipsis.length).join('');
	return truncated + ellipsis;
}

/**
 * 安全地处理用户显示名称（支持自定义最大长度，中文算1，英文/emoji算1）
 * @param {Object} user 用户对象
 * @param {number} maxLength 最大长度，默认7
 * @returns {string} 安全的用户显示名称
 */
export function safeUserDisplayNameWithLimit(user, maxLength = 7) {
	if (!user) return '未知用户';

	let displayName = safeString(user.first_name || '');

	if (!displayName.trim()) {
		return '未知用户';
	}

	// 按字符数截断，中文/英文/emoji都算1
	const chars = Array.from(displayName);
	if (chars.length <= maxLength) {
		return displayName;
	}
	return chars.slice(0, maxLength).join('') + '...';
}

/**
 * 安全地处理频道显示名称
 * @param {Object} chat 频道对象
 * @param {string} signature 签名（可选）
 * @param {number} maxLength 最大长度，默认10
 * @returns {string} 安全的频道显示名称
 */
export function safeChannelDisplayName(chat, signature, maxLength = 10) {
	if (!chat) return '';

	let title = safeString(chat.title || '');
	
	if (!title.trim()) {
		return '';
	}

	// 如果标题较短且有签名，添加签名
	if (title.length <= 6 && signature) {
		const safeSignature = safeTruncate(safeString(signature), 6);
		if (safeSignature) {
			title += ` (${safeSignature})`;
		}
	}

	return safeTruncate(title, maxLength);
}

/**
 * 安全地处理caption文本
 * @param {string} caption 原始caption
 * @returns {string} 安全的caption
 */
export function safeCaptionText(caption) {
	if (!caption || typeof caption !== 'string') {
		return '';
	}
	
	return safeString(caption);
}

/**
 * 安全地处理实体数组，确保偏移量正确
 * @param {Array} entities 实体数组
 * @param {string} originalText 原始文本
 * @param {string} processedText 处理后的文本
 * @returns {Array} 处理后的实体数组
 */
export function safeEntities(entities, originalText, processedText) {
	if (!entities || !Array.isArray(entities)) {
		return [];
	}
	
	if (!originalText || !processedText) {
		return [];
	}
	
	// 如果文本长度没有变化，直接返回
	if (originalText.length === processedText.length) {
		return entities;
	}
	
	// 否则，重新计算偏移量（简单方法：清空实体，避免偏移量错误）
	// 在复杂的emoji处理场景下，重新计算偏移量可能很复杂
	console.warn('Text length changed after processing, clearing entities to avoid offset errors');
	return [];
}

// =============================================================================
// 扩展区域 - 在这里添加新的格式化规则和功能
// =============================================================================

/**
 * 清理文本中的特殊标签（如去除 #投稿 等）
 * @param {string} text 输入文本
 * @param {Array<string>} tagsToRemove 要移除的标签数组，默认 ['#投稿']
 * @returns {string} 清理后的文本
 */
export function removeSpecialTags(text, tagsToRemove = ['#投稿']) {
	if (!text || typeof text !== 'string') {
		return '';
	}
	
	let result = safeString(text);
	
	// 移除指定的标签
	tagsToRemove.forEach(tag => {
		result = result.replace(new RegExp(tag, 'g'), '').trim();
	});
	
	return result;
}

/**
 * 按字节长度截取字符串，正确处理复杂Unicode字符和emoji
 * 基于 https://juejin.cn/post/6844903970700263438 的实现进行现代化改造
 * @param {string} str 输入字符串
 * @param {number} maxBytes 最大字节数
 * @returns {string} 按字节截取的字符串
 */
export function substringByBytes(str, maxBytes) {
	if (!str || typeof str !== 'string') {
		return '';
	}
	
	if (typeof maxBytes !== 'number' || maxBytes <= 0) {
		return '';
	}
	
	// 先进行安全处理
	const safeStr = safeString(str);
	
	/**
	 * 通过二进制代码计算字节数
	 * @param {string} binaryCode 二进制代码
	 * @returns {number} 字节数
	 */
	function getBytesByBinary(binaryCode) {
		const byteLengthData = [0, 1, 2, 3, 4];
		const len = byteLengthData[Math.ceil(binaryCode.length / 8)];
		return len;
	}
	
	/**
	 * 通过十六进制代码计算字节数
	 * @param {string} hexCode 十六进制代码
	 * @returns {number} 字节数
	 */
	function getBytesByHex(hexCode) {
		return getBytesByBinary(parseInt(hexCode, 16).toString(2));
	}
	
	let result = '';
	let isComplexEmoji = false;
	let tempLen = 0;
	let currentByteLength = 0;
	let lastValidPosition = 0;
	
	for (let i = 0; i < safeStr.length; i++) {
		const codePoint = safeStr.codePointAt(i);
		if (!codePoint) continue;
		
		const hexCode = codePoint.toString(16);
		
		// 处理代理对（surrogate pairs）
		if (hexCode.length > 4) {
			i++; // 跳过下一个代理字符
			if (i + 1 < safeStr.length) {
				const nextCodePoint = safeStr.codePointAt(i + 1);
				isComplexEmoji = nextCodePoint && nextCodePoint.toString(16) === '200d'; // Zero Width Joiner
			}
		}
		
		if (isComplexEmoji) {
			// 复杂emoji序列（如👨‍👩‍👧‍👦）
			tempLen += getBytesByHex(hexCode);
			if (i === safeStr.length - 1) {
				// 序列结束
				currentByteLength += tempLen;
				if (currentByteLength <= maxBytes) {
					result += safeStr.substring(lastValidPosition, i + 1);
				}
				break;
			}
		} else {
			// 处理之前累积的复杂emoji
			if (tempLen !== 0) {
				currentByteLength += tempLen;
				currentByteLength += getBytesByHex(hexCode);
				if (currentByteLength <= maxBytes) {
					result += safeStr.substring(lastValidPosition, i + 1);
					lastValidPosition = i + 1;
				} else {
					break;
				}
				tempLen = 0;
				continue;
			}
			
			// 普通字符处理
			const charBytes = getBytesByHex(hexCode);
			if (currentByteLength + charBytes <= maxBytes) {
				currentByteLength += charBytes;
				if (hexCode.length <= 4) {
					result += safeStr[i];
				} else {
					result += safeStr[i - 1] + safeStr[i];
				}
				lastValidPosition = i + 1;
			} else {
				break;
			}
		}
	}
	
	return result;
}

/**
 * 移除字符串中的所有emoji表情符号
 * @param {string} str 输入字符串
 * @returns {string} 移除emoji后的字符串
 */
export function removeEmoji(str) {
	if (!str || typeof str !== 'string') {
		return '';
	}
	
	// 先进行安全处理
	const safeStr = safeString(str);
	
	// 全面的emoji正则表达式，包含各种Unicode表情符号范围
	const emojiRegex = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
	
	return safeStr.replace(emojiRegex, '');
}

/**
 * 深度复制并重新解析对象/字符串，用于避免引用问题
 * @param {any} data 要重新解析的数据
 * @returns {any} 重新解析后的数据
 */
export function deepClone(data) {
	if (data === null || data === undefined) {
		return data;
	}
	
	try {
		// 使用JSON序列化/反序列化进行深度复制
		// 注意：这种方法无法处理函数、Symbol、undefined等特殊值
		return JSON.parse(JSON.stringify(data));
	} catch (error) {
		console.warn('Deep clone failed, returning original data:', error);
		return data;
	}
}

/**
 * 格式化时间戳为可读格式
 * @param {number} timestamp Unix时间戳
 * @param {string} format 格式类型：'full', 'date', 'time', 'relative'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp(timestamp, format = 'full') {
	if (!timestamp || typeof timestamp !== 'number') {
		return '';
	}
	
	const date = new Date(timestamp * 1000);
	const now = new Date();
	
	switch (format) {
		case 'date':
			return date.toLocaleDateString('zh-CN');
		case 'time':
			return date.toLocaleTimeString('zh-CN');
		case 'relative':
			const diffMs = now - date;
			const diffMins = Math.floor(diffMs / 60000);
			const diffHours = Math.floor(diffMs / 3600000);
			const diffDays = Math.floor(diffMs / 86400000);
			
			if (diffMins < 1) return '刚刚';
			if (diffMins < 60) return `${diffMins}分钟前`;
			if (diffHours < 24) return `${diffHours}小时前`;
			if (diffDays < 7) return `${diffDays}天前`;
			return date.toLocaleDateString('zh-CN');
		case 'full':
		default:
			return date.toLocaleString('zh-CN');
	}
}

/**
 * 清理HTML标签（如果需要处理从web来源的文本）
 * @param {string} html 包含HTML的字符串
 * @returns {string} 清理后的纯文本
 */
export function stripHtmlTags(html) {
	if (!html || typeof html !== 'string') {
		return '';
	}
	
	// 移除HTML标签，保留文本内容
	let result = html.replace(/<[^>]*>/g, '');
	
	// 解码HTML实体
	result = result
		.replace(/&amp;/g, '&')
		.replace(/&lt;/g, '<')
		.replace(/&gt;/g, '>')
		.replace(/&quot;/g, '"')
		.replace(/&#39;/g, "'");
	
	return safeString(result);
}

/**
 * 验证和清理用户名（用于@username格式）
 * @param {string} username 用户名
 * @returns {string} 清理后的用户名
 */
export function sanitizeUsername(username) {
	if (!username || typeof username !== 'string') {
		return '';
	}
	
	// 移除@符号开头，只保留字母数字和下划线
	let result = username.replace(/^@/, '');
	result = result.replace(/[^a-zA-Z0-9_]/g, '');
	
	return result;
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
	if (!bytes || typeof bytes !== 'number') {
		return '0 B';
	}
	
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(1024));
	const size = (bytes / Math.pow(1024, i)).toFixed(1);
	
	return `${size} ${sizes[i]}`;
}

/**
 * 生成安全的文件名（移除特殊字符）
 * @param {string} filename 原始文件名
 * @param {string} fallback 默认文件名
 * @returns {string} 安全的文件名
 */
export function safeFilename(filename, fallback = 'untitled') {
	if (!filename || typeof filename !== 'string') {
		return fallback;
	}
	
	// 移除文件系统不支持的字符
	let result = safeString(filename);
	result = result.replace(/[<>:"/\\|?*]/g, '_');
	result = result.replace(/\s+/g, '_');
	
	// 确保不为空
	return result.trim() || fallback;
}

/**
 * 检测是否是bilifeedbot转换的视频
 * @param {string} caption 说明文字
 * @returns {boolean} 是否是bilifeedbot转换的视频
 */
export function isBilifeedbotContent(caption) {
	if (!caption || typeof caption !== 'string') {
		return false;
	}
	
	// 检测是否同时包含这些关键词
	const keywords = ['播放量', '弹幕', '评论', '点赞', '投币', '收藏', '转发', '发布日期'];
	
	// 必须同时包含所有关键词才认为是bilifeedbot内容
	return keywords.every(keyword => caption.includes(keyword));
}

/**
 * 精简bilifeedbot内容，只保留第一次换行前的带链接的文字
 * @param {string} caption 原始说明文字
 * @param {Array} entities 原始实体数组
 * @returns {Object} 精简后的说明文字和实体 {caption: string, entities: Array}
 */
export function simplifyBilifeedbotContent(caption, entities = []) {
	if (!caption || typeof caption !== 'string') {
		return { caption: '', entities: [] };
	}
	
	// 找到第一次换行的位置
	const firstNewlineIndex = caption.indexOf('\n');
	
	if (firstNewlineIndex === -1) {
		// 如果没有换行，返回原始内容
		return { caption, entities };
	}
	
	// 只保留第一次换行前的内容
	const simplifiedCaption = caption.substring(0, firstNewlineIndex);
	
	// 过滤实体，只保留在精简内容范围内的实体
	const filteredEntities = entities.filter(entity => {
		return entity.offset + entity.length <= simplifiedCaption.length;
	});
	
	return {
		caption: simplifiedCaption,
		entities: filteredEntities
	};
}

/**
 * 检测消息是否包含自定义表情（Telegram Premium表情）
 * @param {Object} message Telegram消息对象
 * @returns {boolean} 是否包含自定义表情
 */
export function hasCustomEmoji(message) {
	const entities = message.entities || [];
	
	// 检查entities中是否有type为'custom_emoji'的项目
	return entities.some(entity => entity.type === 'custom_emoji');
}

// =============================================================================
// 导出所有函数
// =============================================================================

export const safeUserDisplayName = safeUserDisplayNameWithLimit;

export default {
	safeString,
	safeTruncate,
	safeUserDisplayName,
	safeChannelDisplayName,
	safeCaptionText,
	safeEntities,
	// 扩展功能
	removeSpecialTags,
	substringByBytes,
	removeEmoji,
	deepClone,
	formatTimestamp,
	stripHtmlTags,
	sanitizeUsername,
	formatFileSize,
	safeFilename,
	isBilifeedbotContent,
	simplifyBilifeedbotContent,
	hasCustomEmoji
}; 