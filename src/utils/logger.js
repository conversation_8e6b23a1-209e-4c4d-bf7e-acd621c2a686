// logger.js
// 全局日志工具，支持彩色输出和不同日志级别，输出北京时间

/**
 * 获取北京时间格式化字符串
 * @returns {string} 格式化的北京时间 (YYYY-MM-DD HH:mm:ss)
 */
export function getBeijingTime() {
	const date = new Date();
	// 北京时间是UTC+8，直接加8小时
	const beijingTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
	
	// 格式化为 YYYY-MM-DD HH:mm:ss
	const YYYY = String(beijingTime.getUTCFullYear());
	const MM = String(beijingTime.getUTCMonth() + 1).padStart(2, '0');
	const DD = String(beijingTime.getUTCDate()).padStart(2, '0');
	const HH = String(beijingTime.getUTCHours()).padStart(2, '0');
	const mm = String(beijingTime.getUTCMinutes()).padStart(2, '0');
	const ss = String(beijingTime.getUTCSeconds()).padStart(2, '0');
	
	return `${HH}:${mm}:${ss}`;
}

// 日志级别配置
const LOG_LEVELS = {
	DEBUG: 0,
	INFO: 1,
	SUCCESS: 2,
	WARN: 3,
	ERROR: 4,
	NONE: 5,
};

// 当前日志级别，可以根据环境变量动态设置
let currentLogLevel = LOG_LEVELS.DEBUG;

// 日志样式配置
const STYLES = {
	debug: {
		bgColor: '#39485c',
		textColor: '#ffffff',
		label: 'DEBUG',
	},
	info: {
		bgColor: '#3498db',
		textColor: '#ffffff',
		label: 'INFO',
	},
	success: {
		bgColor: '#64b587',
		textColor: '#ffffff',
		label: 'SUCCESS',
	},
	warn: {
		bgColor: '#face51',
		textColor: '#000000',
		label: 'WARN',
	},
	error: {
		bgColor: '#ea3324',
		textColor: '#ffffff',
		label: 'ERROR',
	},
	time: {
		bgColor: '#9b59b6',
		textColor: '#ffffff',
		label: 'TIME',
	},
	tag: {
		bgColor: '#1abc9c',
		textColor: '#ffffff',
		label: 'TAG',
	},
};

// 非 dev 环境下用 emoji 表示不同级别颜色
const LEVEL_EMOJI = {
	debug: '🟦',
	info: '⬜️',
	success: '🟩',
	warn: '🟨',
	error: '🟥',
	time: '🟪',
	tag: '🟫',
};

// 时间戳样式 - 接近纯黑的深蓝灰背景，半黑半白的灰色文字
const timestampStyle = 'background:#777a7b;color:#eee;border-radius:4px;padding:2px 4px;margin-right:2px;font-weight:bold;';

// dev 环境判定 - 完全由外部入口（index.js）传入控制
// 若未设置，默认按"非 dev"处理，确保生产环境的安全性
let devModeOverride = null; // null 表示未手动设置

export function setDevMode(isDev) {
	devModeOverride = !!isDev;
}

function isDevEnvironment() {
	return devModeOverride === true;
}

/**
 * 设置当前日志级别
 * @param {string|number} level 日志级别名称或数值
 */
export function setLogLevel(level) {
	if (typeof level === 'string' && level in LOG_LEVELS) {
		currentLogLevel = LOG_LEVELS[level];
	} else if (typeof level === 'number' && level >= 0 && level <= 5) {
		currentLogLevel = level;
	} else {
		console.warn(`[${getBeijingTime()}] 无效的日志级别: ${level}`);
	}
}

/**
 * 创建控制台样式字符串
 * @param {string} level 日志级别
 * @returns {Object} 样式对象，包含标签样式和内容样式
 */
function createStyles(level) {
	const style = STYLES[level] || STYLES.info;

	// 标签样式：背景色、文字色、圆角等
	const labelStyle = `background:${style.bgColor};color:${style.textColor};border-radius:4px;padding:2px 4px;margin-right:2px;font-weight:bold;`;

	// 内容样式：与标签颜色匹配的文字颜色
	const contentStyle = `color:${style.bgColor};background:#f8f8f8;border-radius:4px;padding:2px 4px;margin-right:2px;`;

	return { labelStyle, contentStyle };
}

/**
 * 基础日志函数
 * @param {string} level 日志级别
 * @param {string|Array} args 日志参数
 */
function logBase(level, ...args) {
	// 检查日志级别
	const logLevelValue =
		level === 'success'
			? LOG_LEVELS.SUCCESS
			: level === 'debug'
			? LOG_LEVELS.DEBUG
			: level === 'info'
			? LOG_LEVELS.INFO
			: level === 'warn'
			? LOG_LEVELS.WARN
			: LOG_LEVELS.ERROR;

	if (logLevelValue < currentLogLevel) {
		return;
	}

	const timestamp = getBeijingTime();
	const { labelStyle, contentStyle } = createStyles(level);
	const style = STYLES[level] || STYLES.info;
	const dev = isDevEnvironment();

	// 格式化日志内容
	if (args.length === 0) {
		if (dev) {
			console.log(`%c${timestamp}%c${style.label}`, timestampStyle, labelStyle);
		} else {
			const emoji = LEVEL_EMOJI[level] || '';
			console.log(`${timestamp} - ${emoji}${style.label}`);
		}
		return;
	}

	// 第一个参数是消息，其余是数据
	const [message, ...data] = args;

	if (data.length === 0) {
		// 只有消息，没有额外数据
		if (dev) {
			console.log(`%c${timestamp}%c${style.label}%c${message}`, timestampStyle, labelStyle, contentStyle);
		} else {
			const emoji = LEVEL_EMOJI[level] || '';
			console.log(`${timestamp} - ${emoji}${style.label} ${message}`);
		}
	} else {
		// 有消息和额外数据
		if (dev) {
			console.log(`%c${timestamp}%c${style.label}%c${message}`, timestampStyle, labelStyle, contentStyle, ...data);
		} else {
			const emoji = LEVEL_EMOJI[level] || '';
			console.log(`${timestamp} - ${emoji}${style.label} ${message}`, ...data);
		}
	}
}

/**
 * 标准日志输出
 * @param {...any} args 日志参数
 */
export function log(...args) {
	logBase('info', ...args);
}

/**
 * 调试级别日志
 * @param {...any} args 日志参数
 */
export function debug(...args) {
	logBase('debug', ...args);
}

/**
 * 信息级别日志
 * @param {...any} args 日志参数
 */
export function info(...args) {
	logBase('info', ...args);
}

/**
 * 成功级别日志
 * @param {...any} args 日志参数
 */
export function success(...args) {
	logBase('success', ...args);
}

/**
 * 警告级别日志
 * @param {...any} args 日志参数
 */
export function warn(...args) {
	logBase('warn', ...args);
}

/**
 * 错误级别日志
 * @param {...any} args 日志参数
 */
export function error(...args) {
	logBase('error', ...args);
}

/**
 * 带标签的日志输出
 * @param {string} tag 标签
 * @param {...any} args 日志参数
 */
export function tagLog(tag, ...args) {
	const { labelStyle } = createStyles('tag');
	const timestamp = getBeijingTime();
	const dev = isDevEnvironment();

	if (args.length === 0) {
		if (dev) {
			console.log(`%c${timestamp}%c ${tag} `, timestampStyle, labelStyle);
		} else {
			console.log(`${timestamp} - ${LEVEL_EMOJI.tag}${tag}`);
		}
		return;
	}

	const [message, ...data] = args;

	if (data.length === 0) {
		if (dev) {
			console.log(`%c${timestamp}%c ${tag} %c${message}`, timestampStyle, labelStyle, `color:#1abc9c;`);
		} else {
			console.log(`${timestamp} - ${LEVEL_EMOJI.tag}${tag} ${message}`);
		}
	} else {
		if (dev) {
			console.log(`%c${timestamp}%c ${tag} %c${message}`, timestampStyle, labelStyle, `color:#1abc9c;`, ...data);
		} else {
			console.log(`${timestamp} - ${LEVEL_EMOJI.tag}${tag} ${message}`, ...data);
		}
	}
}

/**
 * 记录 HTTP 响应
 * @param {Object} response HTTP 响应对象
 */
export function logResponse(response) {
	if (currentLogLevel > LOG_LEVELS.DEBUG) {
		return;
	}

	const { labelStyle, contentStyle } = createStyles('info');
	const timestamp = getBeijingTime();
	const dev = isDevEnvironment();

	if (dev) {
		console.group(`%c${timestamp}%c API响应 %c ${response.url || 'N/A'} `, timestampStyle, labelStyle, contentStyle);
		console.log(`%c 状态码 `, 'background:#3498db;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', response.status || 'N/A');
		try {
			if (response.headers) {
				console.groupCollapsed('响应头');
				for (const [key, value] of response.headers.entries()) {
					console.log(`%c ${key} %c ${value} `, 'background:#f1c40f;color:black;border-radius:4px;padding:1px 3px;', 'color:#7f8c8d;');
				}
				console.groupEnd();
			}
			if (response.body) {
				console.log(`%c 响应体 `, 'background:#2ecc71;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', '(流，未读取)');
			} else {
				console.log(`%c 响应体 `, 'background:#e74c3c;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', '空');
			}
		} catch (err) {
			console.log(`%c 错误 `, 'background:#e74c3c;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', '无法完全记录响应:', err);
		}
		console.groupEnd();
	} else {
		const emoji = LEVEL_EMOJI.info;
		try {
			console.log(`${timestamp} - ${emoji}API响应 ${response.url || 'N/A'} status=${response.status || 'N/A'}`);
			if (response.headers) {
				// 简洁输出头部数量，避免刷屏
				let headerCount = 0;
				for (const _ of response.headers.entries()) headerCount++;
				console.log(`${timestamp} - ${emoji}headers=${headerCount}`);
			}
		} catch (err) {
			console.log(`${timestamp} - ${emoji}记录响应时出错`, err);
		}
	}
}

/**
 * 记录 HTTP 错误
 * @param {Error} error 错误对象
 */
export function logError(error) {
	if (currentLogLevel > LOG_LEVELS.ERROR) {
		return;
	}

	const { labelStyle, contentStyle } = createStyles('error');
	const timestamp = getBeijingTime();
	const dev = isDevEnvironment();

	const errorObj = {
		message: error.message,
		stack: error.stack,
		name: error.name,
	};

	if (error.response) {
		errorObj.response = {
			status: error.response.status,
			statusText: error.response.statusText,
			headers: Object.fromEntries([...error.response.headers]),
		};
	}

	if (error.request) {
		errorObj.request = {
			method: error.request.method,
			url: error.request.url,
		};
	}

	if (dev) {
		console.group(`%c${timestamp}%c API错误 %c ${error.message} `, timestampStyle, labelStyle, contentStyle);
		if (error.response) {
			console.log(`%c 状态码 `, 'background:#e74c3c;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', error.response.status);
		}
		if (error.request) {
			console.log(`%c 请求URL `, 'background:#3498db;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', error.request.url);
		}
		console.log(`%c 详细信息 `, 'background:#9b59b6;color:white;border-radius:4px;padding:2px 4px;margin-right:2px;', errorObj);
		console.groupEnd();
	} else {
		console.error(`${timestamp} - ${LEVEL_EMOJI.error}API错误 ${error.message}`, errorObj);
	}
}

/**
 * 计时器日志，用于性能测量
 */
export class Timer {
	constructor(label) {
		this.label = label;
		this.startTime = performance.now();
	}

	/**
	 * 结束计时并输出耗时
	 * @param {string} [message] 额外消息
	 */
	end(message = '') {
		const endTime = performance.now();
		const duration = endTime - this.startTime;
		const msg = message ? `${this.label} (${message})` : this.label;

		const { labelStyle, contentStyle } = createStyles('time');
		const timestamp = getBeijingTime();
		const dev = isDevEnvironment();

		if (dev) {
			console.log(`%c${timestamp}%c TIME %c ${msg} `, timestampStyle, labelStyle, contentStyle, `⏱️ 耗时: ${duration.toFixed(2)}ms`);
		} else {
			console.log(`${timestamp} - ${LEVEL_EMOJI.time}TIME ${msg} ⏱️ 耗时: ${duration.toFixed(2)}ms`);
		}
		return duration;
	}
}

/**
 * 创建一个计时器
 * @param {string} label 计时器标签
 * @returns {Timer} 计时器实例
 */
export function createTimer(label) {
	return new Timer(label);
}

// 导出日志级别常量
export const LogLevels = LOG_LEVELS;

// 创建一个默认导出对象，包含所有函数
const Logger = {
	log,
	debug,
	info,
	success,
	warn,
	error,
	tagLog,
	logResponse,
	logError,
	createTimer,
	setLogLevel,
	getBeijingTime,
	LogLevels,
	Timer,
	setDevMode,
};

// 默认导出
export default Logger;
