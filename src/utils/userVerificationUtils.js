/**
 * 用户验证工具类
 * 实现全局真人验证逻辑（一次验证，全群生效）
 * 基于内存的验证会话管理（利用CF Worker wall time无限制特性）
 */

import Logger from './logger.js';
import { sendTelegramRequest } from './telegramApi.js';
import { Resvg } from '@cf-wasm/resvg';

// 旧的内存会话管理已迁移到新系统 (memberManagement/modules/sessionManager.js)
// 现在使用 KV 存储进行会话管理

// #region 🔍 全局验证查询

// 内存缓存：记录最近处理过的用户（防止重复计数失败）
const recentlyProcessedUsers = new Map();

/**
 * 标记用户为最近已处理（防止重复计数失败）
 * @param {number} userId 用户ID
 * @param {string} chatId 群组ID
 */
export function markUserAsRecentlyProcessed(userId, chatId) {
    const key = `${userId}_${chatId}`;
    recentlyProcessedUsers.set(key, Date.now());
    
    // 清理1分钟前的记录
    const oneMinuteAgo = Date.now() - 60000;
    for (const [cacheKey, timestamp] of recentlyProcessedUsers.entries()) {
        if (timestamp < oneMinuteAgo) {
            recentlyProcessedUsers.delete(cacheKey);
        }
    }
    
    Logger.debug('标记用户为已处理:', { userId, chatId, cacheSize: recentlyProcessedUsers.size });
}

/**
 * 检查用户是否最近被处理过
 * @param {number} userId 用户ID
 * @param {string} chatId 群组ID
 * @returns {boolean} 是否最近被处理过
 */
export function isUserRecentlyProcessed(userId, chatId) {
    const key = `${userId}_${chatId}`;
    const timestamp = recentlyProcessedUsers.get(key);
    
    if (!timestamp) {
        return false;
    }
    
    // 检查是否在最近30秒内被处理过
    const thirtySecondsAgo = Date.now() - 30000;
    const isRecent = timestamp > thirtySecondsAgo;
    
    Logger.debug('检查用户是否最近被处理:', {
        userId,
        chatId,
        hasRecord: !!timestamp,
        isRecent,
        timeDiff: timestamp ? Date.now() - timestamp : null
    });
    
    return isRecent;
}

/**
 * 检查用户是否已通过全局真人验证
 * @param {Object} env 环境变量
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否已验证
 */
export async function isUserGloballyVerified(env, userId) {
    try {
        const result = await env.DB.prepare(`
            SELECT is_verified 
            FROM user_verification_records 
            WHERE user_id = ?
        `).bind(userId).first();
        
        const isVerified = result?.is_verified === 1 || result?.is_verified === true;
        
        Logger.debug('检查用户全局验证状态:', {
            userId,
            hasRecord: !!result,
            rawValue: result?.is_verified,
            valueType: typeof result?.is_verified,
            isVerified
        });
        
        // SQLite中BOOLEAN存储为INTEGER，1表示true，0表示false
        return isVerified;
    } catch (error) {
        Logger.error('查询用户全局验证状态失败:', error);
        return false; // 出错时默认为未验证
    }
}

/**
 * 获取用户验证记录详情
 * @param {Object} env 环境变量
 * @param {number} userId 用户ID
 * @returns {Promise<Object|null>} 验证记录或null
 */
export async function getUserVerificationRecord(env, userId) {
    try {
        return await env.DB.prepare(`
            SELECT * 
            FROM user_verification_records 
            WHERE user_id = ?
        `).bind(userId).first();
    } catch (error) {
        Logger.error('获取用户验证记录失败:', error);
        return null;
    }
}

// #endregion 🔍 全局验证查询

// #region 📝 验证记录管理

/**
 * 初始化用户验证记录（不增加失败次数）
 * @param {Object} env 环境变量
 * @param {number} userId 用户ID
 * @param {string} userName 用户名
 * @param {string} chatId 群组ID
 * @returns {Promise<boolean>} 是否操作成功
 */
export async function initializeUserVerificationRecord(env, userId, userName, chatId) {
    try {
        const now = new Date().toISOString();
        
        // 仅创建记录，不增加失败次数
        await env.DB.prepare(`
            INSERT INTO user_verification_records (
                user_id, user_name, first_verified_in_chat, failed_attempts, last_attempt_at, updated_at
            ) VALUES (?, ?, ?, 0, ?, ?)
            ON CONFLICT(user_id) DO UPDATE SET
                user_name = excluded.user_name,
                updated_at = excluded.updated_at
        `).bind(userId, userName, chatId, now, now).run();
        
        Logger.debug('用户验证记录初始化成功:', {
            userId,
            userName,
            chatId
        });
        
        return true;
    } catch (error) {
        Logger.error('初始化用户验证记录失败:', error);
        return false;
    }
}

/**
 * 创建或更新用户验证记录
 * @param {Object} env 环境变量
 * @param {number} userId 用户ID
 * @param {string} userName 用户名
 * @param {string} chatId 首次验证的群组ID
 * @param {boolean} isVerified 是否验证成功
 * @returns {Promise<boolean>} 是否操作成功
 */
export async function createOrUpdateVerificationRecord(env, userId, userName, chatId, isVerified = false) {
    try {
        const now = new Date().toISOString();
        
        if (isVerified) {
            // 验证成功：插入或更新为已验证状态
            await env.DB.prepare(`
                INSERT INTO user_verification_records (
                    user_id, user_name, first_verified_in_chat, is_verified, verified_at, updated_at
                ) VALUES (?, ?, ?, TRUE, ?, ?)
                ON CONFLICT(user_id) DO UPDATE SET
                    user_name = excluded.user_name,
                    is_verified = TRUE,
                    verified_at = excluded.verified_at,
                    updated_at = excluded.updated_at
            `).bind(userId, userName, chatId, now, now).run();
            
            Logger.info('用户全局验证成功:', {
                userId,
                userName,
                firstVerifiedIn: chatId
            });
        } else {
            // 验证失败：增加失败次数
            await env.DB.prepare(`
                INSERT INTO user_verification_records (
                    user_id, user_name, first_verified_in_chat, failed_attempts, last_attempt_at, updated_at
                ) VALUES (?, ?, ?, 1, ?, ?)
                ON CONFLICT(user_id) DO UPDATE SET
                    user_name = excluded.user_name,
                    failed_attempts = failed_attempts + 1,
                    last_attempt_at = excluded.last_attempt_at,
                    updated_at = excluded.updated_at
            `).bind(userId, userName, chatId, now, now).run();
            
            Logger.warn('用户验证失败，增加失败次数:', {
                userId,
                userName,
                attemptInChat: chatId
            });
        }
        
        return true;
    } catch (error) {
        Logger.error('创建或更新验证记录失败:', error);
        return false;
    }
}

// #endregion 📝 验证记录管理

// #region 💾 内存会话管理

/**
 * 生成数学验证题目
 * @returns {Object} 题目对象 {question, answer, options, correctIndex}
 */
export function generateMathQuestion() {
    // 生成简单的加法题（1-20 + 1-20）
    const num1 = Math.floor(Math.random() * 20) + 1;
    const num2 = Math.floor(Math.random() * 20) + 1;
    const correctAnswer = num1 + num2;
    
    // 生成4个选项，其中一个是正确答案
    const options = [];
    const correctIndex = Math.floor(Math.random() * 4);
    
    for (let i = 0; i < 4; i++) {
        if (i === correctIndex) {
            options[i] = correctAnswer;
        } else {
            // 生成接近但不等于正确答案的选项
            let wrongAnswer;
            do {
                wrongAnswer = correctAnswer + Math.floor(Math.random() * 6) - 3; // ±3范围
            } while (wrongAnswer === correctAnswer || wrongAnswer < 1 || options.includes(wrongAnswer));
            options[i] = wrongAnswer;
        }
    }
    
    return {
        question: `${num1} + ${num2} = ?`,
        answer: correctAnswer,
        options: options.map(String),
        correctIndex
    };
}

/**
 * 生成数学验证图像（SVG转PNG，纯图形化绘制）
 * @param {Object} mathQuestion 数学题对象，包含 question、options、correctIndex
 * @returns {Promise<ArrayBuffer>} PNG 图像的 ArrayBuffer
 */
export async function generateMathVerificationImage(mathQuestion) {
    try {
        // 获取题目和选项（数字格式化为2位数，只显示算式，不显示=?）
        const formatNumber = (num) => num.toString().padStart(2, '0');
        const questionText = `${formatNumber(mathQuestion.num1)} ${mathQuestion.operator || '+'} ${formatNumber(mathQuestion.num2)}`;
        const options = (mathQuestion.options || ['A', 'B', 'C', 'D']).map(opt =>
            typeof opt === 'number' ? formatNumber(opt) : opt
        );

        Logger.debug('生成验证图片', {
            question: questionText,
            options: options,
            correctIndex: mathQuestion.correctIndex
        });

        // 解析并绘制数学题（居中，适中字体）
        const mathElements = parseMathExpression(questionText);
        const questionPaths = generateMathExpressionPaths(mathElements, 241, 65, 1.4); // 垂直居中在蓝框内

        // 生成选项路径（2x2布局，更大字体）
        const optionsPaths = generateOptionsPathsGrid(options, 211, 190);
        
        // 生成简化的纯图形化 SVG（去掉顶部和底部装饰）
        const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="450" height="290" viewBox="0 0 450 290">
            <defs>
                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                </linearGradient>
                <!-- 彩虹条纹（斜着的硬朗切割） -->
                <linearGradient id="rainbowStripes" x1="0%" y1="0%" x2="100%" y2="100%" gradientUnits="objectBoundingBox">
                    <stop offset="0%" style="stop-color:#ff0000;stop-opacity:0.5" />
                    <stop offset="14.28%" style="stop-color:#ff0000;stop-opacity:0.5" />
                    <stop offset="14.29%" style="stop-color:#ff8000;stop-opacity:0.5" />
                    <stop offset="28.57%" style="stop-color:#ff8000;stop-opacity:0.5" />
                    <stop offset="28.58%" style="stop-color:#ffff00;stop-opacity:0.5" />
                    <stop offset="42.85%" style="stop-color:#ffff00;stop-opacity:0.5" />
                    <stop offset="42.86%" style="stop-color:#00ff00;stop-opacity:0.5" />
                    <stop offset="57.14%" style="stop-color:#00ff00;stop-opacity:0.5" />
                    <stop offset="57.15%" style="stop-color:#0080ff;stop-opacity:0.5" />
                    <stop offset="71.42%" style="stop-color:#0080ff;stop-opacity:0.5" />
                    <stop offset="71.43%" style="stop-color:#8000ff;stop-opacity:0.5" />
                    <stop offset="85.71%" style="stop-color:#8000ff;stop-opacity:0.5" />
                    <stop offset="85.72%" style="stop-color:#ff0080;stop-opacity:0.5" />
                    <stop offset="100%" style="stop-color:#ff0080;stop-opacity:0.5" />
                </linearGradient>
                <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.15)"/>
                </filter>
            </defs>

            <!-- 背景（彩虹条纹，去掉圆角） -->
            <rect width="450" height="290" fill="url(#rainbowStripes)" stroke="#dee2e6" stroke-width="0"/>

            <!-- 数学题背景框（蓝色边框，白底，增加高度） -->
            <rect x="70" y="30" width="310" height="80" fill="rgba(255,255,255,0.98)" rx="12"
                  stroke="#0066cc" stroke-width="2" filter="url(#dropShadow)"/>

            <!-- 数学题目（图形化，居中） -->
            ${questionPaths}

            <!-- 选项背景框（绿色边框，更高） -->
            <rect x="75" y="140" width="300" height="110" fill="rgba(255,255,255,0.95)" rx="8"
                  stroke="#28a745" stroke-width="2" filter="url(#dropShadow)"/>

            <!-- 选项（图形化，2x2布局） -->
            ${optionsPaths}
        </svg>`;
        
        // 使用 @cf-wasm/resvg 将 SVG 转换为 PNG（提高分辨率）
        const resvg = new Resvg(svg, {
            background: 'white',
            fitTo: {
                mode: 'width',
                value: 900  // 提高到2倍分辨率
            },
            logLevel: 'warn'
        });
        
        const pngData = resvg.render();
        const pngUint8Array = pngData.asPng();

        // 将 Uint8Array 转换为 ArrayBuffer
        const pngArrayBuffer = pngUint8Array.buffer.slice(
            pngUint8Array.byteOffset,
            pngUint8Array.byteOffset + pngUint8Array.byteLength
        );

        // 验证生成的PNG文件是否有效
        const pngValidation = validatePngBuffer(pngArrayBuffer);

        Logger.info('纯图形验证图片生成成功', {
            width: 450,
            height: 260,
            resolution: '900px (2x)',
            size: `${(pngArrayBuffer.byteLength / 1024).toFixed(1)}KB`,
            question: questionText,
            options: options,
            correctAnswer: `选项${['A','B','C','D'][mathQuestion.correctIndex]}: ${options[mathQuestion.correctIndex]}`,
            isValidPng: pngValidation.isValid,
            renderMethod: 'pure_graphics_optimized'
        });

        return pngArrayBuffer;
    } catch (error) {
        Logger.error('生成验证图片失败:', error);
        
        // 降级方案：生成最简化版本
        return generateMinimalVerificationImage(mathQuestion);
    }
}

/**
 * 降级方案：生成最简化的验证图片（纯几何图形）
 */
async function generateMinimalVerificationImage(mathQuestion) {
    try {
        const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="400" height="200" viewBox="0 0 400 200">
            <!-- 背景 -->
            <rect width="400" height="200" fill="#f8f9fa" stroke="#ddd" stroke-width="1" rx="8"/>
            
            <!-- 标题指示 -->
            <circle cx="200" cy="30" r="15" fill="#0066cc"/>
            <path d="M195,25 L195,35 L205,35 L205,25 Z M200,20 L200,40" stroke="white" stroke-width="2" fill="none"/>
            
            <!-- 题目区域 -->
            <rect x="50" y="50" width="300" height="50" fill="white" stroke="#0066cc" stroke-width="2" rx="6"/>
            
            <!-- 简单的数学表达式（用几何图形表示） -->
            <g transform="translate(200, 75)">
                <!-- 第一个数字（用点表示） -->
                <g transform="translate(-80, 0)">
                    ${generateNumberDots(mathQuestion.num1 || parseInt(mathQuestion.question?.split('+')[0]) || 5)}
                </g>
                
                <!-- 加号 -->
                <path d="M-20,-8 L-20,8 M-28,0 L-12,0" stroke="#0066cc" stroke-width="3" fill="none"/>
                
                <!-- 第二个数字（用点表示） -->
                <g transform="translate(20, 0)">
                    ${generateNumberDots(mathQuestion.num2 || parseInt(mathQuestion.question?.split('+')[1]?.split('=')[0]) || 3)}
                </g>
                
                <!-- 等号 -->
                <path d="M60,-4 L80,-4 M60,4 L80,4" stroke="#0066cc" stroke-width="3" fill="none"/>
                
                <!-- 问号 -->
                <circle cx="100" cy="-5" r="3" fill="none" stroke="#0066cc" stroke-width="2"/>
                <circle cx="100" cy="5" r="1" fill="#0066cc"/>
            </g>
            
            <!-- 选项区域 -->
            <rect x="50" y="120" width="300" height="40" fill="#f9f9f9" stroke="#28a745" stroke-width="1" rx="4"/>
            
            <!-- 选项指示（用不同形状表示ABCD） -->
            <g transform="translate(200, 140)">
                <!-- A选项：三角形 -->
                <path d="M-120,-8 L-110,8 L-130,8 Z" fill="#28a745"/>
                ${generateOptionValue(mathQuestion.options[0], -120, 12)}
                
                <!-- B选项：正方形 -->
                <rect x="-45" y="-8" width="16" height="16" fill="#28a745"/>
                ${generateOptionValue(mathQuestion.options[1], -37, 12)}
                
                <!-- C选项：圆形 -->
                <circle cx="37" cy="0" r="8" fill="#28a745"/>
                ${generateOptionValue(mathQuestion.options[2], 37, 12)}
                
                <!-- D选项：菱形 -->
                <path d="M120,-8 L128,0 L120,8 L112,0 Z" fill="#28a745"/>
                ${generateOptionValue(mathQuestion.options[3], 120, 12)}
            </g>
            
            <!-- 倒计时指示 -->
            <circle cx="50" cy="180" r="6" fill="#ffc107"/>
            <path d="M50,174 L50,180 L54,180" stroke="white" stroke-width="1" fill="none"/>
        </svg>`;
        
        const resvg = new Resvg(svg, {
            background: 'white',
            fitTo: { mode: 'width', value: 400 }
        });
        
        const pngData = resvg.render();
        const pngUint8Array = pngData.asPng();

        // 将 Uint8Array 转换为 ArrayBuffer
        const pngArrayBuffer = pngUint8Array.buffer.slice(
            pngUint8Array.byteOffset,
            pngUint8Array.byteOffset + pngUint8Array.byteLength
        );

        Logger.warn('使用最简化方案生成验证图片', {
            size: `${(pngArrayBuffer.byteLength / 1024).toFixed(1)}KB`
        });

        return pngArrayBuffer;
    } catch (error) {
        Logger.error('连降级方案也失败了:', error);
        throw new Error(`验证图片生成完全失败: ${error.message}`);
    }
}

/**
 * 生成用点表示的数字（最简单的视觉表示）
 */
function generateNumberDots(num) {
    let dots = '';
    const maxDots = Math.min(num, 12); // 最多显示12个点
    const cols = Math.ceil(Math.sqrt(maxDots));
    
    for (let i = 0; i < maxDots; i++) {
        const x = (i % cols) * 8 - (cols * 4);
        const y = Math.floor(i / cols) * 8 - 8;
        dots += `<circle cx="${x}" cy="${y}" r="2" fill="#0066cc"/>`;
    }
    return dots;
}

/**
 * 生成选项数值的点表示
 */
function generateOptionValue(value, centerX, y) {
    const num = parseInt(value) || 0;
    const maxDots = Math.min(num, 8);
    let dots = '';
    
    for (let i = 0; i < maxDots; i++) {
        const x = centerX + (i % 4) * 4 - 6;
        const yPos = y + Math.floor(i / 4) * 4;
        dots += `<circle cx="${x}" cy="${yPos}" r="1" fill="#555"/>`;
    }
    return dots;
}

/**
 * 验证PNG文件的有效性
 * @param {ArrayBuffer} buffer PNG文件的ArrayBuffer
 * @returns {Object} 验证结果
 */
function validatePngBuffer(buffer) {
    try {
        const bytes = new Uint8Array(buffer);
        
        // PNG文件签名: 89 50 4E 47 0D 0A 1A 0A
        const pngSignature = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
        
        if (bytes.length < 8) {
            return { isValid: false, signature: 'too_short', error: '文件太短' };
        }
        
        // 检查PNG签名
        const actualSignature = Array.from(bytes.slice(0, 8));
        const signatureMatch = actualSignature.every((byte, index) => byte === pngSignature[index]);
        
        return {
            isValid: signatureMatch,
            signature: actualSignature.map(b => b.toString(16).padStart(2, '0')).join(' '),
            expectedSignature: pngSignature.map(b => b.toString(16).padStart(2, '0')).join(' '),
            fileSize: bytes.length
        };
    } catch (error) {
        return { isValid: false, signature: 'error', error: error.message };
    }
}

/**
 * 将ArrayBuffer转换为Base64（修复了二进制数据编码问题）
 * @param {ArrayBuffer} buffer 
 * @returns {string} Base64字符串
 */
export function arrayBufferToBase64(buffer) {
    try {
        const bytes = new Uint8Array(buffer);
        
        // 使用分块处理避免栈溢出，但保持简单可靠
        const chunkSize = 8192; // 8KB chunks
        let binaryString = '';
        
        for (let i = 0; i < bytes.length; i += chunkSize) {
            const end = Math.min(i + chunkSize, bytes.length);
            const chunk = bytes.subarray(i, end);
            
            // 使用 String.fromCharCode.apply 确保正确处理字节
            binaryString += String.fromCharCode.apply(null, chunk);
        }
        
        const base64 = btoa(binaryString);
        
        Logger.debug('Base64编码成功', {
            inputSize: `${(buffer.byteLength / 1024).toFixed(1)}KB`,
            outputSize: `${(base64.length / 1024).toFixed(1)}KB`,
            method: 'chunked'
        });
        
        return base64;
    } catch (error) {
        Logger.error('Base64编码失败:', error);
        throw new Error(`Base64编码失败: ${error.message}`);
    }
}

// createVerificationSession 已迁移到新系统
// 现在使用 memberManagement/modules/sessionManager.js 中的 createSession

// getVerificationSession 已迁移到新系统
// 现在使用 memberManagement/modules/sessionManager.js 中的 getSession

// findActiveVerificationSession 已迁移到新系统
// 现在使用 memberManagement/modules/sessionManager.js 中的会话查找逻辑

// deleteVerificationSession 和 verifyAnswer 已迁移到新系统
// 现在使用 memberManagement/modules/sessionManager.js 中的对应方法

// #endregion 💾 内存会话管理

// #region 🔐 权限控制

/**
 * 限制用户权限
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function restrictUserPermissions(env, chatId, userId) {
    try {
        const response = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/restrictChatMember`,
            {
                chat_id: chatId,
                user_id: userId,
                permissions: {
                    can_send_messages: false,
                    can_send_audios: false,
                    can_send_documents: false,
                    can_send_photos: false,
                    can_send_videos: false,
                    can_send_video_notes: false,
                    can_send_voice_notes: false,
                    can_send_polls: false,
                    can_send_other_messages: false,
                    can_add_web_page_previews: false,
                    can_change_info: false,
                    can_invite_users: false,
                    can_pin_messages: false,
                    can_manage_topics: false
                }
            }
        );
        
        if (response.ok) {
            Logger.info('用户权限限制成功:', { chatId, userId });
            return true;
        } else {
            Logger.error('用户权限限制失败:', response);
            return false;
        }
    } catch (error) {
        Logger.error('限制用户权限时出错:', error);
        return false;
    }
}

/**
 * 恢复用户权限
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function restoreUserPermissions(env, chatId, userId) {
    try {
        const response = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/restrictChatMember`,
            {
                chat_id: chatId,
                user_id: userId,
                permissions: {
                    can_send_messages: true,
                    can_send_audios: true,
                    can_send_documents: true,
                    can_send_photos: true,
                    can_send_videos: true,
                    can_send_video_notes: true,
                    can_send_voice_notes: true,
                    can_send_polls: true,
                    can_send_other_messages: true,
                    can_add_web_page_previews: true,
                    can_change_info: false,
                    can_invite_users: false,
                    can_pin_messages: false,
                    can_manage_topics: false
                }
            }
        );
        
        if (response.ok) {
            Logger.info('用户权限恢复成功:', { chatId, userId });
            return true;
        } else {
            Logger.error('用户权限恢复失败:', response);
            return false;
        }
    } catch (error) {
        Logger.error('恢复用户权限时出错:', error);
        return false;
    }
}

/**
 * 踢出用户
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function kickUser(env, chatId, userId) {
    try {
        const response = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/banChatMember`,
            {
                chat_id: chatId,
                user_id: userId,
                until_date: Math.floor(Date.now() / 1000) + 60 // 1分钟后解禁（即踢出但不永久封禁）
            }
        );
        
        if (response.ok) {
            Logger.info('用户踢出成功:', { chatId, userId });
            return true;
        } else {
            Logger.error('用户踢出失败:', response);
            return false;
        }
    } catch (error) {
        Logger.error('踢出用户时出错:', error);
        return false;
    }
}

/**
 * 封禁用户
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} userId 用户ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function banUser(env, chatId, userId) {
    try {
        const response = await sendTelegramRequest(
            env,
            `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/banChatMember`,
            {
                chat_id: chatId,
                user_id: userId
            }
        );
        
        if (response.ok) {
            Logger.info('用户封禁成功:', { chatId, userId });
            return true;
        } else {
            Logger.error('用户封禁失败:', response);
            return false;
        }
    } catch (error) {
        Logger.error('封禁用户时出错:', error);
        return false;
    }
}

// #endregion 🔐 权限控制

// #region 🔧 辅助函数

/**
 * 生成验证码字符串
 * @param {number} length 长度
 * @returns {string} 验证码
 */
export function generateVerificationCode(length = 8) {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 检查用户是否达到最大失败次数
 * @param {Object} env 环境变量
 * @param {number} userId 用户ID
 * @param {number} maxAttempts 最大尝试次数
 * @returns {Promise<boolean>} 是否达到最大失败次数
 */
export async function userExceedsFailureLimit(env, userId, maxAttempts = 5) {
    const record = await getUserVerificationRecord(env, userId);
    return record && record.failed_attempts >= maxAttempts;
}

// #endregion 🔧 辅助函数

// #region 📊 统计信息

/**
 * 获取验证统计信息
 * @param {Object} env 环境变量
 * @param {number} days 统计天数（默认30天）
 * @returns {Promise<Object>} 统计信息
 */
export async function getVerificationStats(env, days = 30) {
    try {
        const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
        
        const stats = await env.DB.prepare(`
            SELECT 
                COUNT(*) as total_users,
                SUM(CASE WHEN is_verified = TRUE THEN 1 ELSE 0 END) as verified_users,
                SUM(failed_attempts) as total_failed_attempts,
                COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_records_period
            FROM user_verification_records
        `).bind(since).first();
        
        return {
            totalUsers: stats.total_users || 0,
            verifiedUsers: stats.verified_users || 0,
            failedAttempts: stats.total_failed_attempts || 0,
            newRecords: stats.new_records_period || 0,
            verificationRate: stats.total_users > 0 ? 
                ((stats.verified_users / stats.total_users) * 100).toFixed(1) + '%' : '0%'
        };
    } catch (error) {
        Logger.error('获取验证统计失败:', error);
        return {
            totalUsers: 0,
            verifiedUsers: 0,
            failedAttempts: 0,
            newRecords: 0,
            verificationRate: '0%'
        };
    }
}

// #endregion 📊 统计信息 

// #region 🎨 图形化绘制函数

/**
 * 导入从真实 SVG 文件生成的图标模块
 * 这确保了与原始 Tabler Icons 完全一致的渲染效果
 */
import { generateCharacterPath } from '../icons/index.js';

/**
 * 解析数学表达式为组件
 */
function parseMathExpression(expression) {
    const elements = [];
    let current = '';
    let i = 0;
    
    while (i < expression.length) {
        const char = expression[i];
        
        if (char >= '0' && char <= '9') {
            current += char;
        } else if (char === ' ') {
            // 跳过空格
        } else {
            // 遇到操作符时，先处理累积的数字
            if (current) {
                elements.push({ type: 'number', value: current });
                current = '';
            }
            
            // 添加操作符
            if (char === '+' || char === '-' || char === '×' || char === '÷' || char === '=' || char === '?') {
                elements.push({ type: 'operator', value: char });
            }
        }
        i++;
    }
    
    // 处理最后的数字
    if (current) {
        elements.push({ type: 'number', value: current });
    }
    
    return elements;
}

/**
 * 生成数学表达式的 SVG 路径
 */
function generateMathExpressionPaths(elements, centerX, centerY, scale = 1.0) {
    let paths = '';

    // 计算总宽度用于居中（考虑缩放）
    const baseCharWidth = 35 * scale;
    const baseSpacing = 10 * scale;

    let totalWidth = 0;
    elements.forEach(element => {
        if (element.type === 'number') {
            totalWidth += element.value.length * baseCharWidth; // 每个数字宽度
        } else {
            totalWidth += baseCharWidth; // 操作符宽度
        }
    });

    let currentX = centerX - totalWidth / 2;

    elements.forEach((element, index) => {
        if (element.type === 'number') {
            // 绘制多位数字
            const digits = element.value.split('');
            digits.forEach((digit, digitIndex) => {
                const digitX = currentX + digitIndex * (32 * scale);
                paths += generateCharacterPath(digit, digitX - (12 * scale), centerY - (12 * scale), 1.4 * scale, '#0066cc');
            });
            currentX += digits.length * (32 * scale);
        } else if (element.type === 'operator') {
            // 绘制操作符
            paths += generateCharacterPath(element.value, currentX - (12 * scale), centerY - (12 * scale), 1.2 * scale, '#0066cc');
            currentX += baseCharWidth;
        }

        // 在元素间添加间距
        currentX += baseSpacing;
    });
    
    return paths;
}

/**
 * 生成选项的 SVG 路径（2x2网格布局）
 */
function generateOptionsPathsGrid(options, centerX, centerY) {
    let paths = '';
    const letters = ['A', 'B', 'C', 'D'];

    // 2x2布局参数（增大间距和字体）
    const rowSpacing = 40; // 增加行间距
    const colSpacing = 120; // 增加列间距
    const letterScale = 1.3; // 字母放大
    const numberScale = 1.3; // 数字放大

    // 计算起始位置（居中）
    const startX = centerX - colSpacing / 2;
    const startY = centerY - rowSpacing / 2;

    options.forEach((option, index) => {
        // 计算2x2网格位置
        const row = Math.floor(index / 2); // 0或1
        const col = index % 2; // 0或1

        const x = startX + col * colSpacing;
        const y = startY + row * rowSpacing;

        const letter = letters[index];

        // 绘制选项字母（灰色，更大）
        paths += generateCharacterPath(letter, x - 40, y - 10, letterScale, '#6c757d');

        // 绘制冒号（灰色，位置在字母和数字之间）
        paths += `<circle cx="${x - 2}" cy="${y - 0}" r="2" fill="#6c757d"/>`;
        paths += `<circle cx="${x - 2}" cy="${y + 10}" r="2" fill="#6c757d"/>`;

        // 绘制选项数字（绿色，更大）
        const optionStr = option.toString();
        let digitStartX = x + 5;
        optionStr.split('').forEach((digit, digitIndex) => {
            const digitX = digitStartX + digitIndex * (18 * numberScale);
            paths += generateCharacterPath(digit, digitX, y - 10, numberScale, '#28a745');
        });
    });

    return paths;
}

/**
 * 生成选项的 SVG 路径（原版单行布局）
 */
function generateOptionsPaths(options, centerX, centerY) {
    let paths = '';
    const letters = ['A', 'B', 'C', 'D'];
    const optionWidth = 85; // 每个选项的宽度
    const startX = centerX - (4 * optionWidth) / 2;
    
    letters.forEach((letter, index) => {
        const x = startX + index * optionWidth;
        const y = centerY;
        
        // 绘制字母标识
        paths += generateCharacterPath(letter, x - 12, y - 12, 1.0, '#28a745');
        
        // 绘制冒号
        paths += `<circle cx="${x + 18}" cy="${y + 2}" r="1.5" fill="#495057"/>`;
        paths += `<circle cx="${x + 18}" cy="${y + 8}" r="1.5" fill="#495057"/>`;
        
        // 绘制数值
        const value = options[index] || '?';
        const digits = value.toString().split('');
        digits.forEach((digit, digitIndex) => {
            const digitX = x + 30 + digitIndex * 22;
            paths += generateCharacterPath(digit, digitX - 12, y - 12, 0.7, '#495057');
        });
    });
    
    return paths;
}

/**
 * 生成标题装饰路径
 */
function generateTitlePaths(x, y) {
    return `
        <!-- 验证标识 -->
        <g transform="translate(${x}, ${y}) scale(0.8)">
            <path d="M12 1L21.5 7v10L12 23L2.5 17V7L12 1z" fill="#0066cc" opacity="0.8"/>
            <path d="M9 12l2 2l4 -4" stroke="white" stroke-width="2" fill="none"/>
        </g>
        <rect x="${x + 20}" y="${y - 4}" width="80" height="3" rx="1.5" fill="#495057"/>
        <rect x="${x + 25}" y="${y + 2}" width="70" height="2" rx="1" fill="#495057" opacity="0.7"/>
    `;
}

/**
 * 生成单个数字路径（用于装饰）
 */
function generateDigitPath(digit, x, y, scale = 1) {
    return generateCharacterPath(digit, x, y, scale, '#ffc107');
}

// #endregion 🎨 图形化绘制函数 