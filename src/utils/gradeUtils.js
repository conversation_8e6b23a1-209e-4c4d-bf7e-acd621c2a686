/**
 * 分级处理工具函数
 * 用于统一处理投稿分级相关的逻辑
 */

/**
 * 分级配置
 */
export const GRADE_CONFIG = {
	clear: {
		prefix: '',
		needsSpoiler: false,
		displayName: '已清空说明'
	},
	sfw: {
		prefix: '🍀SFW\n',
		needsSpoiler: false,
		displayName: 'SFW'
	},
	nsfw: {
		prefix: '🔞NSFW⚠️\n',
		needsSpoiler: true,
		displayName: 'NSFW'
	},
	qwyl: {
		prefix: '#奇闻异录\n',
		needsSpoiler: false,
		displayName: '奇闻异录'
	},
	qw: {
		prefix: '#奇闻异录\n',
		needsSpoiler: false,
		displayName: '奇闻异录'
	},
	R18G: {
		prefix: '#奇闻异录\n🔞NSFW⚠\n️🔞no safe for eyes⚠️\n⚠️三重警告TAG注意|自愿点开\n',
		needsSpoiler: true,
		displayName: '三重警告'
	},
	waitHeroes: {
		prefix: '🔞NSFW⚠\n#等一位英雄\n',
		needsSpoiler: true,
		displayName: '等一位英雄'
	},
	wh: {
		prefix: '🔞NSFW⚠\n#等一位英雄\n',
		needsSpoiler: true,
		displayName: '等一位英雄'
	},
	cf: {
		prefix: '🐴WTF❓\n#WTF #迷惑\n',
		needsSpoiler: false,
		displayName: '迷惑'
	},
	confuse: {
		prefix: '🐴WTF❓\n#WTF #迷惑\n',
		needsSpoiler: false,
		displayName: '迷惑'
	}
};

/**
 * 所有分级前缀的正则表达式模式
 */
export const GRADE_PATTERNS = [
	/🍀SFW\n/,
	/🔞NSFW⚠️\n/,
	/#奇闻异录\n/,
	/#奇闻异录\n🔞NSFW⚠\n️🔞no safe for eyes⚠️\n⚠️三重警告TAG注意\|自愿点开\n/,
	/🔞NSFW⚠\n#等一位英雄\n/,
	/🐴WTF❓\n#WTF #迷惑\n/
];

/**
 * 获取分级配置
 * @param {string} grade 分级类型
 * @returns {Object} 分级配置对象
 */
export function getGradeConfig(grade) {
	return GRADE_CONFIG[grade] || { prefix: '', needsSpoiler: false, displayName: grade };
}

/**
 * 移除文本中的分级前缀
 * @param {string} text 原始文本
 * @returns {Object} { cleanText: string, hadPrefix: boolean }
 */
export function removeGradePrefix(text) {
	if (!text) return { cleanText: '', hadPrefix: false };
	
	let cleanText = text;
	let hadPrefix = false;
	
	for (const pattern of GRADE_PATTERNS) {
		if (pattern.test(cleanText)) {
			cleanText = cleanText.replace(pattern, '');
			hadPrefix = true;
			break; // 只移除第一个匹配的前缀
		}
	}
	
	return { cleanText, hadPrefix };
}

/**
 * 应用分级前缀到文本
 * @param {string} text 原始文本
 * @param {string} grade 分级类型
 * @returns {string} 添加前缀后的文本
 */
export function applyGradePrefix(text, grade) {
	const config = getGradeConfig(grade);
	if (!config.prefix) return text;
	
	return config.prefix + (text || '');
}

/**
 * 调整实体偏移量
 * @param {Array} entities 原始实体数组
 * @param {number} offset 偏移量
 * @param {number} maxLength 最大长度（可选，用于过滤越界的实体）
 * @returns {Array} 调整后的实体数组
 */
export function adjustEntitiesOffset(entities, offset, maxLength = null) {
	if (!entities || !Array.isArray(entities) || entities.length === 0) {
		return [];
	}
	
	return entities
		.map(entity => ({
			...entity,
			offset: entity.offset + offset
		}))
		.filter(entity => {
			// 过滤掉偏移为负数的实体
			if (entity.offset < 0) return false;
			
			// 如果指定了最大长度，过滤掉越界的实体
			if (maxLength !== null && entity.offset + entity.length > maxLength) {
				return false;
			}
			
			return true;
		});
}

/**
 * 合并多个实体数组
 * @param {...Array} entityArrays 多个实体数组
 * @returns {Array} 合并后的实体数组
 */
export function mergeEntities(...entityArrays) {
	const result = [];
	
	for (const entities of entityArrays) {
		if (entities && Array.isArray(entities)) {
			result.push(...entities);
		}
	}
	
	return result;
}

/**
 * 通用的分级文本处理函数
 * @param {Object} options 处理选项
 * @param {string} options.originalText 原始文本
 * @param {Array} options.originalEntities 原始实体数组
 * @param {string} options.grade 分级类型
 * @param {string} options.viaText via信息文本（可选）
 * @param {Array} options.viaEntities via信息实体数组（可选）
 * @param {string} options.separator 原文和via信息之间的分隔符（默认'\n'）
 * @returns {Object} { text: string, entities: Array, needsSpoiler: boolean }
 */
export function processGradeText(options) {
	const {
		originalText = '',
		originalEntities = [],
		grade,
		viaText = '',
		viaEntities = [],
		separator = '\n'
	} = options;
	
	const gradeConfig = getGradeConfig(grade);
	
	// 如果是清空分级，特殊处理
	if (grade === 'clear') {
		return {
			text: viaText,
			entities: viaEntities,
			needsSpoiler: false
		};
	}
	
	// 移除现有的分级前缀
	const { cleanText, hadPrefix } = removeGradePrefix(originalText);
	
	// 构建最终文本
	let finalText = gradeConfig.prefix;
	let finalEntities = [];
	
	if (cleanText.trim()) {
		// 有原文：分级前缀 + 原文 + (换行 + via信息)
		finalText += cleanText;
		
		// 处理原文实体：需要重新计算偏移
		if (originalEntities.length > 0) {
			let adjustedOriginalEntities;
			
			if (hadPrefix) {
				// 如果之前有前缀，需要重新计算实体位置
				adjustedOriginalEntities = originalEntities
					.map(entity => {
						// 计算新的偏移量：分级前缀长度 + 实体在清理后文本中的位置
						const newOffset = gradeConfig.prefix.length + Math.max(0, entity.offset - (originalText.length - cleanText.length));
						return {
							...entity,
							offset: newOffset
						};
					})
					.filter(entity => entity.offset >= 0 && entity.offset + entity.length <= finalText.length);
			} else {
				// 如果之前没有前缀，只需添加新前缀的长度
				adjustedOriginalEntities = adjustEntitiesOffset(
					originalEntities, 
					gradeConfig.prefix.length,
					finalText.length
				);
			}
			
			finalEntities.push(...adjustedOriginalEntities);
		}
		
		// 如果有via信息，添加分隔符和via信息
		if (viaText.trim()) {
			finalText += separator + viaText;
			
			// 处理via实体：需要加上分级前缀 + 原文 + 分隔符的偏移
			if (viaEntities.length > 0) {
				const viaOffset = gradeConfig.prefix.length + cleanText.length + separator.length;
				const adjustedViaEntities = adjustEntitiesOffset(viaEntities, viaOffset);
				finalEntities.push(...adjustedViaEntities);
			}
		}
	} else {
		// 无原文：分级前缀 + via信息
		finalText += viaText;
		
		// 只处理via实体：需要加上分级前缀的偏移
		if (viaEntities.length > 0) {
			const adjustedViaEntities = adjustEntitiesOffset(
				viaEntities, 
				gradeConfig.prefix.length
			);
			finalEntities.push(...adjustedViaEntities);
		}
	}
	
	return {
		text: finalText,
		entities: finalEntities,
		needsSpoiler: gradeConfig.needsSpoiler
	};
} 