/**
 * 媒体组处理工具模块
 * 提供媒体组相关的通用处理功能
 */

import { sendTelegramRequest } from './telegramApi.js';
import Logger from './logger.js';
import { safeCaptionText, safeUserDisplayNameWithLimit, safeChannelDisplayName, isBilifeedbotContent, simplifyBilifeedbotContent } from './stringUtils.js';
import { BOT_ADMINS } from '../config/constants.js';

/**
 * 创建带有原始消息链接的说明文字，使用via/from格式
 * @param {Object} message 原始消息对象
 * @param {String} originalCaption 原始说明文字
 * @param {String} grade 内容分级
 * @param {String} targetChannelId 目标频道ID（可选，用于特定频道的引流）
 * @returns {Object} 包含说明文字和实体的对象
 */
export function createCaptionWithSource(message, originalCaption = '', grade = 'normal', targetChannelId = null) {
	const chatId = message.chat.id.toString();
	const messageId = message.message_id;
	const user = message.from;
	const userDisplayName = safeUserDisplayNameWithLimit(user, 7);

	// 创建原始消息链接
	const sourceUrl = `https://t.me/c/${chatId.replace('-100', '')}/` + messageId;

	// 准备新的说明文字
	let newCaption = '';
	let entities = [];

	// 检查原始说明文字是否已经包含via信息
	const hasViaInfo = originalCaption && (
		originalCaption.includes('via ') || 
		originalCaption.includes('from ')
	);

	// 如果已经有via信息，直接返回原始内容，避免重复
	if (hasViaInfo) {
		Logger.debug('说明文字已包含via信息，跳过重复添加');
		return {
			caption: safeCaptionText(originalCaption || ''),
			entities: message.caption_entities || [],
		};
	}

	// 如果有原始说明文字，先添加它（去除可能的#投稿标签）
	if (originalCaption && originalCaption.trim() !== '') {
		// 使用安全字符串处理原始caption
		let cleanCaption = safeCaptionText(originalCaption);
		let cleanEntities = message.caption_entities || [];
		
		// 检测是否是bilifeedbot转换的视频
		if (isBilifeedbotContent(cleanCaption)) {
			Logger.debug('检测到bilifeedbot转换视频，精简说明文字');
			const simplified = simplifyBilifeedbotContent(cleanCaption, cleanEntities);
			cleanCaption = simplified.caption;
			cleanEntities = simplified.entities;
		}
		
		// 去除#投稿标签
		if (cleanCaption.includes('#投稿')) {
			cleanCaption = cleanCaption.replace('#投稿', '').trim();
		}

		if (cleanCaption) {
			const beforeLength = newCaption.length;
			newCaption += cleanCaption + '\n';

			// 如果原始消息有实体，调整偏移量
			if (cleanEntities && cleanEntities.length > 0) {
				entities = cleanEntities
					.map((entity) => {
						// 跳过可能的#投稿标签实体
						if (originalCaption.indexOf('#投稿') >= 0 &&
							entity.offset <= originalCaption.indexOf('#投稿') &&
							entity.offset + entity.length >= originalCaption.indexOf('#投稿') + 3
						) {
							return null;
						}

						// 调整偏移量
						return {
							...entity,
							offset: beforeLength + Math.max(0, entity.offset - (originalCaption.indexOf('#投稿') > 0 ? 3 : 0))
						};
					})
					.filter((entity) => entity !== null);
			}
		}
	}

	// 检查是否是机器人管理员
	const isAdmin = user && BOT_ADMINS.includes(user.id);
	
	// 标记是否有via或from信息
	let hasViaOrFromInfo = false;
	
	// 如果不是管理员，添加via用户名
	if (!isAdmin && userDisplayName) {
		const viaOffset = newCaption.length;
		newCaption += `via ${userDisplayName}`;
		hasViaOrFromInfo = true;

		// 添加用户链接实体
		entities.push({
			type: 'text_link',
			offset: viaOffset + 4, // "via "的长度
			length: userDisplayName.length,
			url: sourceUrl,
		});
	}

	// 添加from频道（如果有）
	if (message.forward_from_chat) {
		const channelName = safeChannelDisplayName(message.forward_from_chat, message.forward_signature, 10);

		// 创建频道链接 - 优先使用username，没有则使用chat_id
		let channelUrl;
		if (message.forward_from_chat.username) {
			// 使用username形式的链接（公开可访问）
			channelUrl = `https://t.me/${message.forward_from_chat.username}/${message.forward_from_message_id}`;
		} else {
			// 使用chat_id形式的链接（需要成员身份）
			channelUrl = `https://t.me/c/${message.forward_from_chat.id.toString().replace('-100', '')}/${message.forward_from_message_id}`;
		}

		// 对于管理员：如果有via xxx from xxx格式，变成只有from xxx
		// 对于普通用户：正常添加空格
		if (!isAdmin && userDisplayName) {
			newCaption += ' ';
		}

		const fromOffset = newCaption.length;
		newCaption += `from ${channelName}`;
		hasViaOrFromInfo = true;

		// 添加频道链接实体
		entities.push({
			type: 'text_link',
			offset: fromOffset + 5, // "from "的长度
			length: channelName.length,
			url: channelUrl,
		});
	}

	// 检查是否是特定频道，如果是则添加引流入口
	if (targetChannelId === '-1001252514147') {
		const promotionText = '传播正能量';
		const channelText = '»频道';
		const channelUrl = 'https://t.me/+SqfZYyn9zZIXpp0R';
		
		// 根据是否有via/from信息决定分隔符
		const separator = hasViaOrFromInfo ? '\n' : '';
		const promotionStartOffset = newCaption.length + separator.length;
		
		newCaption += separator + promotionText + ' ';
		
		// 为第三个"正"字添加链接
		const thirdCharOffset = promotionStartOffset + 2; // "传播正"的第三个字符位置
		entities.push({
			type: 'text_link',
			offset: thirdCharOffset,
			length: 1, // 一个字符的长度
			url: channelUrl,
		});
		
		// 添加频道链接部分
		const channelLinkOffset = newCaption.length;
		newCaption += channelText;
		
		// 添加频道链接实体
		entities.push({
			type: 'text_link',
			offset: channelLinkOffset,
			length: channelText.length,
			url: channelUrl,
		});
	}

	return {
		caption: newCaption,
		entities: entities,
	};
}

/**
 * 从数据库获取媒体组的所有消息（从media_group表）
 * @param {Object} env 环境变量
 * @param {string} mediaGroupId 媒体组ID
 * @returns {Array} 媒体组消息数组
 */
export async function getMediaGroupMessages(env, mediaGroupId) {
	try {
		Logger.debug('查询媒体组消息, mediaGroupId:', mediaGroupId, 'type:', typeof mediaGroupId);
		const stmt = env.DB.prepare(`SELECT raw_json FROM tg_log_media_group WHERE media_group_id = ? ORDER BY msg_id ASC`);
		const result = await stmt.bind(mediaGroupId).all();
		return result.results || [];
	} catch (error) {
		Logger.error('查询媒体组消息失败:', error);
		return [];
	}
}

/**
 * 从转发提交数据库获取媒体组的所有消息（从forwarded_submissions表）
 * @param {Object} env 环境变量
 * @param {string} mediaGroupId 媒体组ID
 * @returns {Array} 转发记录数组
 */
export async function getMediaGroupForwardedRecords(env, mediaGroupId) {
	try {
		Logger.debug('查询媒体组转发记录, mediaGroupId:', mediaGroupId, 'type:', typeof mediaGroupId);
		
		// 先尝试通过转发后的媒体组ID查询（如果字段存在）
		try {
			const stmt1 = env.DB.prepare(`SELECT * FROM tg_log_forwarded_submissions WHERE forwarded_media_group_id = ? ORDER BY msg_id ASC`);
			const result1 = await stmt1.bind(mediaGroupId).all();
			
			if (result1.results && result1.results.length > 0) {
				Logger.debug('通过转发后媒体组ID找到记录:', result1.results.length);
				return result1.results;
			}
		} catch (fieldError) {
			// 如果forwarded_media_group_id字段不存在，忽略这个错误
			Logger.debug('forwarded_media_group_id字段可能不存在，降级为原始ID查询');
		}
		
		// 尝试通过原始媒体组ID查询
		const stmt2 = env.DB.prepare(`SELECT * FROM tg_log_forwarded_submissions WHERE media_group_id = ? ORDER BY msg_id ASC`);
		const result2 = await stmt2.bind(mediaGroupId).all();
		
		Logger.debug('通过原始媒体组ID查询结果:', result2.results ? result2.results.length : 0);
		return result2.results || [];
	} catch (error) {
		Logger.error('查询媒体组转发记录失败:', error);
		return [];
	}
}

/**
 * 批量删除消息
 * @param {Object} env 环境变量
 * @param {string} chatId 聊天ID
 * @param {Array} messageIds 消息ID数组
 * @returns {Promise<boolean>} 删除是否成功
 */
export async function batchDeleteMessages(env, chatId, messageIds) {
	if (!messageIds || messageIds.length === 0) {
		return true;
	}

	try {
		if (messageIds.length === 1) {
			// 单条消息删除
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
				chat_id: chatId,
				message_id: messageIds[0],
			});
			Logger.debug('已删除消息:', messageIds[0]);
		} else {
			// 批量删除
			try {
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessages`, {
					chat_id: chatId,
					message_ids: messageIds,
				});
				Logger.debug('已批量删除消息:', messageIds);
			} catch (error) {
				if (error.description && error.description.includes('deleteMessages')) {
					// 如果批量删除失败，降级为逐个删除
					Logger.debug('批量删除失败，降级为逐个删除');
					for (const messageId of messageIds) {
						try {
							await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
								chat_id: chatId,
								message_id: messageId,
							});
							Logger.debug('已删除消息:', messageId);
						} catch (singleError) {
							Logger.debug('删除单条消息失败:', messageId, singleError);
						}
					}
				} else {
					throw error;
				}
			}
		}
		return true;
	} catch (error) {
		Logger.error('批量删除消息失败:', error);
		return false;
	}
}

/**
 * 批量编辑媒体消息
 * @param {Object} env 环境变量
 * @param {string} chatId 聊天ID
 * @param {Array} mediaMessages 媒体消息数组
 * @param {string} newCaption 新的说明文字（只应用到第一条消息）
 * @param {Array} newCaptionEntities 新的说明文字实体
 * @param {boolean} needsSpoiler 是否需要遮罩
 * @returns {Promise<Array>} 编辑结果数组
 */
export async function batchEditMediaMessages(env, chatId, mediaMessages, newCaption, newCaptionEntities, needsSpoiler) {
	const results = [];
	let firstMessageEdited = false;

	for (const msg of mediaMessages) {
		try {
			const messageData = typeof msg === 'string' ? JSON.parse(msg) : msg;
			let captionToUse = '';
			let entitiesToUse = [];

			// 只有第一条消息使用新的caption，其他消息保留原有caption
			if (!firstMessageEdited) {
				captionToUse = newCaption;
				entitiesToUse = newCaptionEntities;
				firstMessageEdited = true;
			} else {
				captionToUse = messageData.caption || '';
				entitiesToUse = messageData.caption_entities || [];
			}

			const result = await editSingleMediaMessage(
				env,
				chatId,
				messageData,
				captionToUse,
				entitiesToUse,
				needsSpoiler
			);

			results.push(result);
			Logger.debug('编辑媒体消息结果:', messageData.message_id, result?.ok);
		} catch (msgError) {
			Logger.error('编辑单个媒体消息时出错:', msgError);
			results.push({ ok: false, error: msgError.message });
		}
	}

	return results;
}

/**
 * 编辑单个媒体消息
 * @param {Object} env 环境变量
 * @param {string} chatId 聊天ID
 * @param {Object} messageData 消息数据
 * @param {string} caption 说明文字
 * @param {Array} captionEntities 说明文字实体
 * @param {boolean} needsSpoiler 是否需要遮罩
 * @returns {Object} API响应结果
 */
export async function editSingleMediaMessage(env, chatId, messageData, caption, captionEntities, needsSpoiler) {
	try {
		// 确保caption是安全的字符串
		const safeCaption = safeCaptionText(caption);
		
		if (messageData.photo) {
			return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageMedia`, {
				chat_id: chatId,
				message_id: messageData.message_id,
				media: {
					type: 'photo',
					media: messageData.photo[messageData.photo.length - 1].file_id,
					caption: safeCaption,
					caption_entities: captionEntities.length > 0 ? captionEntities : undefined,
					has_spoiler: needsSpoiler,
				},
			});
		} else if (messageData.video) {
			return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageMedia`, {
				chat_id: chatId,
				message_id: messageData.message_id,
				media: {
					type: 'video',
					media: messageData.video.file_id,
					caption: safeCaption,
					caption_entities: captionEntities.length > 0 ? captionEntities : undefined,
					has_spoiler: needsSpoiler,
				},
			});
		} else if (messageData.animation) {
			return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageMedia`, {
				chat_id: chatId,
				message_id: messageData.message_id,
				media: {
					type: 'animation',
					media: messageData.animation.file_id,
					caption: safeCaption,
					caption_entities: captionEntities.length > 0 ? captionEntities : undefined,
					has_spoiler: needsSpoiler,
				},
			});
		} else if (messageData.document) {
			return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageMedia`, {
				chat_id: chatId,
				message_id: messageData.message_id,
				media: {
					type: 'document',
					media: messageData.document.file_id,
					caption: safeCaption,
					caption_entities: captionEntities.length > 0 ? captionEntities : undefined,
				},
			});
		} else if (messageData.audio) {
			return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageMedia`, {
				chat_id: chatId,
				message_id: messageData.message_id,
				media: {
					type: 'audio',
					media: messageData.audio.file_id,
					caption: safeCaption,
					caption_entities: captionEntities.length > 0 ? captionEntities : undefined,
				},
			});
		} else {
			// 不支持的媒体类型或纯文本
			Logger.warn('不支持编辑的媒体类型:', Object.keys(messageData));
			return { ok: false, description: '不支持的媒体类型' };
		}
	} catch (error) {
		Logger.error('编辑单个媒体消息失败:', error);
		return { ok: false, error: error.message };
	}
}

/**
 * 发送媒体组到指定频道
 * @param {Object} env 环境变量
 * @param {string} targetChannelId 目标频道ID
 * @param {Array} mediaMessages 媒体消息数组
 * @param {Object} options 附加选项
 * @returns {Object} 发送结果
 */
export async function sendMediaGroupToChannel(env, targetChannelId, mediaMessages, options = {}) {
	try {
		if (mediaMessages.length === 1) {
			// 单条消息
			const messageData = typeof mediaMessages[0] === 'string' 
				? JSON.parse(mediaMessages[0]) 
				: mediaMessages[0];
			
			return await sendSingleMediaToChannel(env, targetChannelId, messageData, options);
		} else if (mediaMessages.length > 1) {
			// 媒体组
			const media = [];
			
			for (let i = 0; i < mediaMessages.length; i++) {
				const messageData = typeof mediaMessages[i] === 'string' 
					? JSON.parse(mediaMessages[i]) 
					: mediaMessages[i];
				
				const mediaItem = createMediaItem(messageData);
				if (mediaItem) {
									// 只有第一条消息保留caption并添加来源信息
				if (i === 0) {
					// 检查是否已经有处理好的caption（来自投稿群的消息）
					// 如果消息来自机器人，说明已经是处理过的，直接使用现有caption
					if (messageData.from && messageData.from.is_bot) {
						// 直接使用现有caption
						mediaItem.caption = messageData.caption || '';
						mediaItem.caption_entities = messageData.caption_entities || [];
					} else {
						// 重新生成via信息
						const captionData = createCaptionWithSource(messageData, messageData.caption || '', 'normal', targetChannelId);
						mediaItem.caption = captionData.caption;
						mediaItem.caption_entities = captionData.entities;
					}
				}
					media.push(mediaItem);
				}
			}
			
			if (media.length > 0) {
				const params = {
					chat_id: targetChannelId,
					media: media,
				};
				
				// 如果有目标话题ID，添加到参数中
				if (options.targetTopicId) {
					params.message_thread_id = options.targetTopicId;
				}
				
				return await sendTelegramRequest(
					env, 
					`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMediaGroup`, 
					params
				);
			}
		}
		
		return { ok: false, description: '没有有效的媒体消息' };
	} catch (error) {
		Logger.error('发送媒体组到频道失败:', error);
		return { ok: false, error: error.message };
	}
}

/**
 * 发送单个媒体到频道
 * @param {Object} env 环境变量
 * @param {string} targetChannelId 目标频道ID
 * @param {Object} messageData 消息数据
 * @param {Object} options 附加选项
 * @returns {Object} API响应结果
 */
export async function sendSingleMediaToChannel(env, targetChannelId, messageData, options = {}) {
	// 检查是否已经有处理好的caption（来自投稿群的消息）
	// 如果消息来自机器人，说明已经是处理过的，直接使用现有caption
	let safeCaption = messageData.caption || '';
	let captionEntities = messageData.caption_entities || [];
	
	// 只有当消息不是来自机器人时，才重新生成via信息
	if (!messageData.from || !messageData.from.is_bot) {
		const captionData = createCaptionWithSource(messageData, messageData.caption || '', 'normal', targetChannelId);
		safeCaption = captionData.caption;
		captionEntities = captionData.entities;
	}
	
	if (messageData.photo) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendPhoto`, {
			chat_id: targetChannelId,
			photo: messageData.photo[messageData.photo.length - 1].file_id,
			caption: safeCaption,
			caption_entities: captionEntities,
			has_spoiler: messageData.has_media_spoiler || false,
		});
	} else if (messageData.video) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVideo`, {
			chat_id: targetChannelId,
			video: messageData.video.file_id,
			caption: safeCaption,
			caption_entities: captionEntities,
			has_spoiler: messageData.has_media_spoiler || false,
		});
	} else if (messageData.animation) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAnimation`, {
			chat_id: targetChannelId,
			animation: messageData.animation.file_id,
			caption: safeCaption,
			caption_entities: captionEntities,
			has_spoiler: messageData.has_media_spoiler || false,
		});
	} else if (messageData.document) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendDocument`, {
			chat_id: targetChannelId,
			document: messageData.document.file_id,
			caption: safeCaption,
			caption_entities: captionEntities,
		});
	} else if (messageData.audio) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAudio`, {
			chat_id: targetChannelId,
			audio: messageData.audio.file_id,
			caption: safeCaption,
			caption_entities: captionEntities,
		});
	} else if (messageData.voice) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVoice`, {
			chat_id: targetChannelId,
			voice: messageData.voice.file_id,
			caption: safeCaption,
			caption_entities: captionEntities,
		});
	} else if (messageData.video_note) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVideoNote`, {
			chat_id: targetChannelId,
			video_note: messageData.video_note.file_id,
		});
	} else {
		return { ok: false, description: '不支持的媒体类型' };
	}
}

/**
 * 创建媒体项目对象
 * @param {Object} messageData 消息数据
 * @returns {Object|null} 媒体项目或null
 */
function createMediaItem(messageData) {
	if (messageData.photo) {
		return {
			type: 'photo',
			media: messageData.photo[messageData.photo.length - 1].file_id,
			has_spoiler: messageData.has_media_spoiler || false,
		};
	} else if (messageData.video) {
		return {
			type: 'video',
			media: messageData.video.file_id,
			has_spoiler: messageData.has_media_spoiler || false,
		};
	} else if (messageData.animation) {
		return {
			type: 'animation',
			media: messageData.animation.file_id,
			has_spoiler: messageData.has_media_spoiler || false,
		};
	} else if (messageData.document) {
		return {
			type: 'document',
			media: messageData.document.file_id,
		};
	} else if (messageData.audio) {
		return {
			type: 'audio',
			media: messageData.audio.file_id,
		};
	}
	
	return null;
} 