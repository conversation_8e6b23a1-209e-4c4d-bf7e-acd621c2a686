/**
 * 新成员加入去重器
 * 防止同一用户短时间内重复触发验证流程
 */

const recentJoins = new Map(); // 存储最近加入的用户
const DEDUP_WINDOW = 5000; // 5秒去重窗口

/**
 * 检查用户是否在去重窗口内
 * @param {string} userId 用户ID
 * @param {string} chatId 群组ID
 * @returns {boolean} 如果是重复请求返回true
 */
export function isDuplicateJoin(userId, chatId) {
    const key = `${chatId}:${userId}`;
    const now = Date.now();
    const lastJoin = recentJoins.get(key);
    
    if (lastJoin && (now - lastJoin) < DEDUP_WINDOW) {
        return true; // 重复请求
    }
    
    // 记录本次加入时间
    recentJoins.set(key, now);
    
    // 清理过期记录（避免内存泄漏）
    if (recentJoins.size > 1000) {
        const cutoff = now - DEDUP_WINDOW;
        for (const [k, time] of recentJoins) {
            if (time < cutoff) {
                recentJoins.delete(k);
            }
        }
    }
    
    return false; // 不是重复请求
}