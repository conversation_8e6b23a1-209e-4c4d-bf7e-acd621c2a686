import Logger from './logger.js';

/**
 * 获取消息类型用于调试显示
 * @param {Object} message 消息对象
 * @returns {string} 消息类型描述
 */
export function getMessageType(message) {
	const types = [];
	
	// 媒体类型
	if (message.photo) types.push('图片');
	if (message.video) types.push('视频');
	if (message.video_note) types.push('视频留言');
	if (message.voice) types.push('语音');
	if (message.audio) types.push('音频');
	if (message.document) types.push('文档');
	if (message.sticker) types.push('贴纸');
	if (message.animation) types.push('GIF');
	
	// 特殊内容
	if (message.text) types.push('文字');
	if (message.caption) types.push('说明文字');
	if (message.location) types.push('位置');
	if (message.poll) types.push('投票');
	if (message.contact) types.push('联系人');
	
	// 特殊消息类型
	if (message.reply_to_message) types.push('回复');
	if (message.new_chat_members) types.push('新成员加入');
	if (message.left_chat_member) types.push('成员离开');
	if (message.pinned_message) types.push('置顶消息');
	if (message.media_group_id) types.push('媒体组');
	
	return types.length > 0 ? types.join('+') : '其他';
}

/**
 * 检查消息是否过期
 * @param {Object} update Telegram更新对象
 * @param {number} maxAgeMinutes 最大允许的消息年龄（分钟）
 * @returns {boolean} 如果消息已过期则返回true
 */
export function isUpdateExpired(update, maxAgeMinutes = 10) {
	// 获取当前时间戳（秒）
	const now = Math.floor(Date.now() / 1000);

	// 对于这些交互性更新，我们不考虑年龄，因为它们都是实时的用户交互
	if (
		update.callback_query || // 点击按钮的回调
		update.chosen_inline_result || // 选中的内联消息
		update.inline_query || // 内联查询
		update.my_chat_member || // 机器人成员状态更新
		(update.edited_message && update.edited_message.via_bot) // 通过机器人编辑的消息
	) {
		return false;
	}

	// 检查是否是常规消息或编辑消息
	let messageToCheck = update.message || update.edited_message;
	if (!messageToCheck) {
		return false; // 没有需要检查的消息
	}

	// 按照以下情况直接确认不过期：
	// 1. 回复给机器人的消息
	// 2. 机器人发送的消息
	// 3. 有按钮的消息
	if (
		// 回复给机器人的消息
		(messageToCheck.reply_to_message && messageToCheck.reply_to_message.from && messageToCheck.reply_to_message.from.is_bot) ||
		// 机器人自己发送的消息
		(messageToCheck.from && messageToCheck.from.is_bot) ||
		// 有按钮的消息
		messageToCheck.reply_markup
	) {
		return false;
	}

	// 为编辑消息使用 edit_date，为普通消息使用 date
	let timestamp;
	if (update.edited_message) {
		timestamp = update.edited_message.edit_date;
	} else {
		timestamp = messageToCheck.date;
	}

	if (!timestamp) {
		return false; // 如果没有时间戳，默认不过期
	}

	// 计算消息年龄（分钟）
	const messageAgeMinutes = (now - timestamp) / 60;

	// 检查是否超过最大允许年龄
	if (messageAgeMinutes > maxAgeMinutes) {
		Logger.warn(`丢弃过期${update.edited_message ? '编辑' : ''}消息，年龄: ${messageAgeMinutes.toFixed(2)} 分钟`);
		return true;
	}

	return false;
} 