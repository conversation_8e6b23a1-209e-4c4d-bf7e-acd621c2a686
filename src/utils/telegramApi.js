/**
 * Telegram API 请求工具
 * 使用 Durable Object 进行速率限制
 */

import Logger from './logger.js';

/**
 * 重置速率限制器状态
 * @param {Object} env 环境变量
 */
export async function resetRateLimiter(env) {
	try {
		Logger.debug('重置速率限制器状态');
		
		const rateLimiterId = env.TELEGRAM_RATE_LIMITER.idFromName('telegram-rate-limiter');
		const rateLimiterStub = env.TELEGRAM_RATE_LIMITER.get(rateLimiterId);

		const resetRequest = new Request('https://dummy.url/resetLimits', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ action: 'resetLimits' })
		});

		const response = await rateLimiterStub.fetch(resetRequest);
		const result = await response.json();
		
		Logger.debug('速率限制器重置结果:', result);
		return result;
	} catch (error) {
		Logger.error('重置速率限制器失败:', error);
		return null;
	}
}

/**
 * 发送带图片的 Telegram 消息（使用 multipart/form-data）
 * @param {Object} env 环境变量
 * @param {string} chatId 聊天ID
 * @param {ArrayBuffer} imageBuffer 图片数据
 * @param {string} caption 图片说明
 * @param {Object} replyMarkup 回复标记
 * @returns {Promise<Object>} API 响应
 */
export async function sendTelegramPhotoMultipart(env, chatId, imageBuffer, caption, replyMarkup) {
	const url = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendPhoto`;
	const apiMethod = 'sendPhoto';
	
	try {
		// 检查速率限制
		const rateLimitResult = await checkRateLimit(env, { apiMethod, apiUrl: url });
		if (rateLimitResult.isThrottled) {
			return {
				ok: false,
				error_code: 429,
				description: 'Request throttled by local rate limiter due to high load.',
				parameters: { retry_after: Math.ceil(rateLimitResult.waitTime / 1000) }
			};
		}

		Logger.debug('发送多媒体 API 请求: sendPhoto');

		// 创建 FormData
		const formData = new FormData();
		formData.append('chat_id', chatId.toString());
		formData.append('caption', caption);
		formData.append('parse_mode', 'Markdown');
		formData.append('reply_markup', JSON.stringify(replyMarkup));
		
		// 创建 Blob 并添加到 FormData
		const imageBlob = new Blob([imageBuffer], { type: 'image/png' });
		formData.append('photo', imageBlob, 'verification.png');

		Logger.debug('FormData 构造完成', {
			chatId,
			captionLength: caption.length,
			imageSize: `${(imageBuffer.byteLength / 1024).toFixed(1)}KB`,
			hasReplyMarkup: !!replyMarkup
		});

		const response = await fetch(url, {
			method: 'POST',
			body: formData,
		});

		const data = await response.json();

		if (!data.ok) {
			const errorCode = data.error_code;
			const description = data.description || "";

			// 报告错误
			if (errorCode === 429 || String(errorCode).startsWith('5') || description.includes('flood')) {
				Logger.error(`API ${apiMethod} 触发可报告错误 (${errorCode}):`, data);
				await reportError(env, {
					error_code: errorCode,
					description: description,
					parameters: data.parameters,
					apiMethod,
					apiUrl: url
				});
			} else {
				Logger.warn(`API ${apiMethod} multipart 请求失败 (${errorCode}):`, data);
				Logger.error('sendPhoto multipart 详细错误', {
					errorCode,
					description,
					imageSize: imageBuffer.byteLength,
					responseData: data
				});
			}
		} else {
			Logger.debug(`API ${apiMethod} multipart 请求成功`);
		}

		return data;
	} catch (error) {
		Logger.error(`API ${apiMethod} multipart 请求异常:`, error);
		throw error;
	}
}

/**
 * 发送 Telegram API 请求，带速率限制
 * @param {Object} env 环境变量
 * @param {string} url API URL
 * @param {Object} params 请求参数
 * @returns {Promise<Object>} API 响应
 */
export async function sendTelegramRequest(env, url, params = {}) {
	// 从URL中提取API方法
	const apiMethod = url.split('/').pop().split('?')[0];
	
	const rateLimitResult = await checkRateLimit(env, { apiMethod, apiUrl: url });

	// 如果速率限制器决定主动节流，则立即停止并返回一个模拟的错误响应
    if (rateLimitResult.isThrottled) {
        Logger.warn('[RateLimiter] 本地主动节流短路', {
            apiMethod,
            waitTime: rateLimitResult.waitTime,
            requestType: rateLimitResult.requestType
        });
        return {
            ok: false,
            error_code: 429,
            description: 'Request throttled by local rate limiter due to high load.',
            parameters: {
                retry_after: Math.ceil(rateLimitResult.waitTime / 1000)
            }
        };
    }
	
	// Durable Object 已在内部处理了所有必要的等待。
	// 一旦上面的 await 完成且未被节流，我们就可以继续了。
	
			try {
		Logger.debug(`发送 API 请求: ${apiMethod}`);
		
		// 为 sendPhoto 请求添加特殊调试信息
		if (apiMethod === 'sendPhoto' && params.photo) {
			Logger.debug('sendPhoto 请求详情', {
				chatId: params.chat_id,
				photoType: typeof params.photo,
				photoLength: params.photo.length,
				isDataUrl: params.photo.startsWith('data:'),
				mimeType: params.photo.match(/^data:([^;]+)/)?.[1],
				hasBase64: params.photo.includes('base64,'),
				base64Start: params.photo.indexOf('base64,') + 7,
				actualBase64Length: params.photo.split('base64,')[1]?.length || 0
			});
		}
		
		const requestOptions = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(params),
		};
		
		Logger.debug('请求体大小', {
			apiMethod,
			bodySize: `${(JSON.stringify(params).length / 1024).toFixed(1)}KB`
		});
		
		const response = await fetch(url, requestOptions);
		const data = await response.json();
		
		// 如果请求不成功，将潜在的错误报告给速率限制器
		if (!data.ok) {
			const errorCode = data.error_code;
			const description = data.description || "";

			// 向 DO 报告可操作的错误（速率限制、服务器错误、洪泛）
			if (errorCode === 429 || String(errorCode).startsWith('5') || description.includes('flood')) {
				Logger.error(`API ${apiMethod} 触发可报告错误 (${errorCode}):`, data);
				// 在后台报告错误，不阻塞主流程
				await reportError(env, {
					error_code: errorCode,
					description: description,
					parameters: data.parameters,
					apiMethod,
					apiUrl: url
				});
			} else if (apiMethod === 'deleteMessage' && description.includes('message to delete not found')) {
				// 针对特定可忽略的错误进行降级
				Logger.debug(`API ${apiMethod} 消息已不存在:`, data);
			} else {
				// 其他业务逻辑失败 - 添加更详细的调试信息
				Logger.warn(`API ${apiMethod} 请求失败 (${errorCode}):`, data);
				
				// 为特定错误添加额外调试信息
				if (apiMethod === 'sendPhoto' && description.includes('wrong remote file identifier')) {
					Logger.error('sendPhoto 详细错误分析', {
						errorCode,
						description,
						photoParam: typeof params.photo,
						photoSize: params.photo?.length,
						isDataUrl: params.photo?.startsWith('data:'),
						base64Part: params.photo?.split('base64,')[1]?.substring(0, 100) + '...',
						parameters: data.parameters
					});
				}
			}
		} else {
			Logger.debug(`API ${apiMethod} 请求成功`);
		}
		
		return data;
	} catch (error) {
		// 为网络错误添加标记，便于上层处理
		if (error.message?.includes('Network connection lost') ||
			error.message?.includes('timeout') ||
			error.name === 'TypeError' && error.message?.includes('fetch')) {
			error.retryable = true;
			Logger.debug(`API ${apiMethod} 网络错误:`, error.message);
		} else {
			Logger.error(`API ${apiMethod} 请求异常:`, error);
		}
		throw error;
	}
}

/**
 * 检查速率限制
 * @param {Object} env 环境变量
 * @param {Object} requestData 请求数据
 * @returns {Promise<Object>} 检查结果
 */
async function checkRateLimit(env, requestData) {
	try {
		const rateLimiterId = env.TELEGRAM_RATE_LIMITER.idFromName('telegram-rate-limiter');
		const rateLimiterStub = env.TELEGRAM_RATE_LIMITER.get(rateLimiterId);

		const checkRequest = new Request('https://dummy.url/checkLimit', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				action: 'checkLimit',
				...requestData
			})
		});

		const response = await rateLimiterStub.fetch(checkRequest);
		const result = await response.json();

		if (result.waited > 0) {
			Logger.debug(`速率限制器内部等待了 ${result.waited}ms (类型: ${result.requestType})`);
		}

		if (result.isThrottled) {
			Logger.warn(`请求被本地速率限制器主动节流，等待时间: ${result.waitTime}ms`);
		}
		
		return result;
	} catch (error) {
		Logger.error('速率限制检查失败，将直接放行请求:', error);
		// 如果速率限制器失败，应选择开放失败（fail-open）以避免阻塞机器人
		return { canProceed: true, isThrottled: false };
	}
}

/**
 * 报告API错误给速率限制器
 * @param {Object} env 环境变量
 * @param {Object} errorData 错误数据
 */
async function reportError(env, errorData) {
	try {
		const rateLimiterId = env.TELEGRAM_RATE_LIMITER.idFromName('telegram-rate-limiter');
		const rateLimiterStub = env.TELEGRAM_RATE_LIMITER.get(rateLimiterId);

		const errorRequest = new Request('https://dummy.url/reportError', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				action: 'reportError',
				...errorData
			})
		});

		await rateLimiterStub.fetch(errorRequest);
	} catch (error) {
		Logger.error('报告错误失败:', error);
	}
}
