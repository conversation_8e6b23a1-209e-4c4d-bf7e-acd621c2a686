import Logger from './logger.js';
import { sendTelegramRequest } from './telegramApi.js';
import { batchDeleteMessages, getMediaGroupForwardedRecords } from './mediaGroupUtils.js';

/**
 * 统一的投稿删除处理函数
 * 用于删除投稿群内的消息和数据库记录
 * @param {Object} env 环境变量
 * @param {number} originalMessageId 原始消息ID
 * @param {string} sourceGroupId 源群组ID  
 * @param {Object} options 选项
 * @param {boolean} options.deleteMessages 是否删除投稿群内的消息（默认true）
 * @param {boolean} options.deleteDatabase 是否删除数据库记录（默认true）
 * @param {Object} options.buttonMessage 按钮消息对象（用于删除操作按钮，可选）
 * @param {boolean} options.forceCleanup 是否强制清理数据库记录（即使消息删除失败）
 * @returns {Promise<boolean>} 操作是否成功
 */
export async function deleteSubmissionData(env, originalMessageId, sourceGroupId, options = {}) {
	const {
		deleteMessages = true,
		deleteDatabase = true,
		buttonMessage = null,
		forceCleanup = false
	} = options;
	
	const TARGET_GROUP_ID = '-1002599022189'; // 投稿处理群
	
	try {
		Logger.debug('开始统一删除投稿数据');
		Logger.debug('参数:', { originalMessageId, sourceGroupId, deleteMessages, deleteDatabase, hasButtonMessage: !!buttonMessage });
		
		// 查询数据库获取转发到投稿群的消息记录
		const stmt = env.DB.prepare(`
			SELECT sub_message_id, media_group_id 
			FROM tg_log_forwarded_submissions 
			WHERE msg_id = ? AND group_id = ?
		`).bind(originalMessageId, sourceGroupId);
		
		const records = await stmt.all();
		
		if (!records.results || records.results.length === 0) {
			Logger.debug('未找到对应的投稿记录，跳过删除');
			return true; // 没有记录不算错误
		}
		
		Logger.debug('找到投稿记录数量:', records.results.length);
		
		// 收集需要删除的消息ID和数据库记录
		const messageIdsToDelete = [];
		const recordsToDelete = [];
		
		for (const record of records.results) {
			messageIdsToDelete.push(record.sub_message_id);
			recordsToDelete.push(record);
			
			// 如果是媒体组，查找所有相关消息
			if (record.media_group_id) {
				Logger.debug('处理媒体组删除, media_group_id:', record.media_group_id);
				
				const mediaGroupStmt = env.DB.prepare(`
					SELECT sub_message_id 
					FROM tg_log_forwarded_submissions 
					WHERE media_group_id = ?
				`).bind(record.media_group_id);
				
				const mediaGroupRecords = await mediaGroupStmt.all();
				
				if (mediaGroupRecords.results) {
					for (const mgRecord of mediaGroupRecords.results) {
						if (!messageIdsToDelete.includes(mgRecord.sub_message_id)) {
							messageIdsToDelete.push(mgRecord.sub_message_id);
						}
					}
				}
			}
		}
		
		// 去重
		const uniqueMessageIds = [...new Set(messageIdsToDelete)];
		Logger.debug('准备删除的投稿消息ID:', uniqueMessageIds);
		
		let deleteSuccess = true;
		
		// 删除投稿群内的消息
		if (deleteMessages && uniqueMessageIds.length > 0) {
			// 查找并添加对应的按钮消息ID
			const buttonMessageIds = [];
			for (const messageId of uniqueMessageIds) {
				try {
					// 通常按钮消息是紧跟在投稿消息之后的
					const nextMessageId = messageId + 1;
					buttonMessageIds.push(nextMessageId);
				} catch (error) {
					Logger.debug('查找按钮消息失败:', error);
				}
			}
			
			// 合并所有需要删除的消息ID
			const allMessageIds = [...uniqueMessageIds, ...buttonMessageIds];
			
			// 如果有传入的按钮消息，也加入删除列表
			if (buttonMessage) {
				if (!allMessageIds.includes(buttonMessage.message_id)) {
					allMessageIds.push(buttonMessage.message_id);
				}
			}
			
			if (allMessageIds.length > 0) {
				const deleteResult = await batchDeleteMessages(env, TARGET_GROUP_ID, allMessageIds);
				
				if (deleteResult) {
					Logger.debug('成功删除投稿群内的消息:', allMessageIds);
				} else {
					Logger.warn('删除投稿群内消息失败');
					deleteSuccess = false;
				}
			}
		}
		
		// 删除数据库记录
		if ((deleteDatabase && recordsToDelete.length > 0) || (forceCleanup && recordsToDelete.length > 0)) {
			for (const record of recordsToDelete) {
				try {
					if (record.media_group_id) {
						// 删除整个媒体组的记录
						const deleteStmt = env.DB.prepare(`
							DELETE FROM tg_log_forwarded_submissions 
							WHERE media_group_id = ?
						`).bind(record.media_group_id);
						await deleteStmt.run();
						Logger.debug('已删除媒体组记录:', record.media_group_id);
					} else {
						// 删除单个记录
						const deleteStmt = env.DB.prepare(`
							DELETE FROM tg_log_forwarded_submissions 
							WHERE sub_message_id = ?
						`).bind(record.sub_message_id);
						await deleteStmt.run();
						Logger.debug('已删除单个记录:', record.sub_message_id);
					}
				} catch (dbError) {
					Logger.error('删除数据库记录失败:', dbError);
					if (!forceCleanup) {
					deleteSuccess = false;
					}
					// 如果是强制清理模式，即使数据库操作失败也不影响整体成功状态
				}
			}
		}
		
		Logger.debug('统一删除投稿数据完成:', deleteSuccess);
		return deleteSuccess;
		
	} catch (error) {
		Logger.error('统一删除投稿数据时出错:', error);
		return false;
	}
}

/**
 * 根据按钮消息删除相关的投稿数据
 * 通过按钮消息的reply_to_message找到原始投稿，然后删除相关数据
 * @param {Object} env 环境变量
 * @param {Object} buttonMessage 按钮消息对象
 * @param {Object} options 选项
 * @returns {Promise<boolean>} 操作是否成功
 */
export async function deleteSubmissionByButtonMessage(env, buttonMessage, options = {}) {
	try {
		// 从按钮消息的reply_to_message找到目标投稿消息
		if (!buttonMessage.reply_to_message) {
			Logger.warn('按钮消息没有reply_to_message，无法确定投稿来源');
			return false;
		}
		
		const targetMessage = buttonMessage.reply_to_message;
		const submissionMessageId = targetMessage.message_id;
		
		// 从数据库查找原始消息信息
		const stmt = env.DB.prepare(`
			SELECT msg_id, group_id 
			FROM tg_log_forwarded_submissions 
			WHERE sub_message_id = ?
		`).bind(submissionMessageId);
		
		const record = await stmt.first();
		
		if (!record) {
			Logger.warn('未找到投稿消息对应的数据库记录:', submissionMessageId);
			// 即使没有数据库记录，也尝试删除消息
			if (options.deleteMessages !== false) {
				const messageIdsToDelete = [buttonMessage.message_id];
				if (targetMessage.media_group_id) {
					// 如果是媒体组，需要处理整个媒体组
					// 这里简化处理，直接删除目标消息
					messageIdsToDelete.push(targetMessage.message_id);
				} else {
					messageIdsToDelete.push(targetMessage.message_id);
				}
				
				await batchDeleteMessages(env, buttonMessage.chat.id, messageIdsToDelete);
			}
			return false;
		}
		
		// 使用统一的删除函数
		return await deleteSubmissionData(env, record.msg_id, record.group_id, {
			...options,
			buttonMessage
		});
		
	} catch (error) {
		Logger.error('根据按钮消息删除投稿数据时出错:', error);
		return false;
	}
} 