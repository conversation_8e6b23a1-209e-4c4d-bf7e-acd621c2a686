
import { log, debug, info, success, warn, error, tagLog, createTimer, setLogLevel, logResponse, LogLevels } from '../utils/logger';
// 设置日志级别（生产环境可以设置为更高级别以减少输出）
setLogLevel(LogLevels.DEBUG);

// 基本日志
log('这是一条普通日志');
debug('这是一条调试日志');
info('这是一条信息日志');
success('操作成功！');
warn('这是一条警告');
error('发生错误', new Error('示例错误'));

// 带标签的日志
tagLog('API', '正在请求数据...');
tagLog('数据库', '查询完成');

// 性能计时
const timer = createTimer('数据处理');
// ... 执行一些操作
timer.end('第一阶段');
// ... 执行更多操作
timer.end('完成');

// API响应日志
const response = await fetch('https://api.example.com/data');
logResponse(response);

// // #### 方式一：命名导入（单个函数）

import { log, debug, success } from './utils/logger';

log('这是一条普通日志');
debug('这是一条调试日志');
success('操作成功！');

// // #### 方式二：默认导入（一次导入全部）

import Logger from './utils/logger';

Logger.log('这是一条普通日志');
Logger.debug('这是一条调试日志');
Logger.success('操作成功！');

// 设置日志级别
Logger.setLogLevel(Logger.LogLevels.INFO);

// 创建计时器
const timer2 = Logger.createTimer('操作');
// ... 执行操作
timer2.end();

// ### 3. 更高级的日志格式
// - 使用 `console.group` 和 `console.groupCollapsed` 来组织复杂的日志输出，如 API 响应和错误
// - 为不同类型的信息（如状态码、响应头等）使用不同的样式
// - 在时间戳前添加标签，使日志更加清晰

// ### 4. 更灵活的参数处理
// 现在日志函数可以接受更灵活的参数：

// 简单消息
Logger.log('用户已登录');

// 带数据的消息
Logger.log('用户信息', { id: 123, name: 'Alice' });

// 多个数据参数
// Logger.log('API调用', '请求成功', { status: 200, data: {...} });

// 标签日志
Logger.tagLog('AUTH', '用户验证', { userId: 123 });

// ### 使用示例

// 导入整个日志工具 （默认都按照这个导入方式去处理）
import Logger from './utils/logger';

// 或者只导入需要的函数
// import { log, debug, error, tagLog } from './utils/logger';

// 基本日志
Logger.log('应用已启动');
Logger.debug('调试信息', { config: '...' });
Logger.info('用户访问了首页');
Logger.success('数据保存成功！');
Logger.warn('磁盘空间不足', { available: '120MB' });
Logger.error('无法连接到服务器', new Error('Connection timeout'));

// 带标签的日志
Logger.tagLog('API', '发送请求', { endpoint: '/users', method: 'GET' });
Logger.tagLog('DB', '查询执行时间过长', { query: 'SELECT * FROM users', time: '2.5s' });

// API响应日志
const response2 = await fetch('https://api.example.com/data');
Logger.logResponse(response2);

// 性能计时
const timer3 = Logger.createTimer('数据处理');
// ... 执行一些操作
timer3.end('第一阶段');
// ... 执行更多操作
timer3.end('完成');

// 在生产环境中设置更高的日志级别
if (process.env.NODE_ENV === 'production') {
	Logger.setLogLevel(Logger.LogLevels.WARN);
}

// 这个优化版本不仅保留了原来的功能，还增加了更丰富的样式和更灵活的使用方式，同时通过默认导出解决了一次导入所有功能的需求。在 Chrome 控制台中，日志将显示美观的彩色标签和格式化的内容，大大提高了可读性和调试效率。
