/**
 * Cloudflare Worker Telegram Bot + Admin Interface
 */

// #region 📦 依赖与导出
// 导入并导出 Durable Object
import { TelegramRateLimiter } from './durableObjects/rateLimiter.js';
import { KeyValueStore } from './durableObjects/keyValueStore.js';
import { VerificationTimer } from './durableObjects/verificationTimer.js';
import { MessageScheduler } from './durableObjects/messageScheduler.js';
export { TelegramRateLimiter, KeyValueStore, VerificationTimer, MessageScheduler };

import Logger, { setDevMode, setLogLevel, LogLevels } from './utils/logger.js';
// 导入 Mini App 处理器
import { handleMiniApp } from './miniappapi/index.js';
// 导入 Webhook 处理器
import { handleWebhook } from './webhook.js';
// #endregion 📦 依赖与导出

// Logger.setLogLevel('INFO');

// #region 🔧 公共环境初始化函数
/**
 * 统一的环境判定和日志初始化逻辑
 * @param {Object} env - Worker 环境变量
 */
function initializeEnvironment(env) {
	// Cloudflare Workers 环境判定：通过 .dev.vars 文件 (本地) 或 wrangler secret (生产)
	// 默认为生产模式，确保安全性
	const isDevMode = env?.DEV_MODE === 'true';
	setDevMode(isDevMode);
	
	if (env?.LOG_LEVEL && (env.LOG_LEVEL in LogLevels)) {
		setLogLevel(env.LOG_LEVEL);
	} else if (env?.LOG_LEVEL && !isNaN(+env.LOG_LEVEL)) {
		setLogLevel(+env.LOG_LEVEL);
	}
}
// #endregion 🔧 公共环境初始化函数

export default {
	async fetch(request, env, ctx) {
		// 初始化环境（每次请求都幂等设置，成本极低）
		initializeEnvironment(env);
		const url = new URL(request.url);
		
		// #region 🤖 Telegram Webhook 路由
		// Telegram Webhook 处理（专用路径）
		if (url.pathname === '/webhook' && request.method === 'POST') {
			// 立即返回200 OK，并将实际处理逻辑放入后台执行。
			// 这可以防止 Telegram Webhook 因等待时间过长而超时。
			// 关键：必须在调用 waitUntil 之前读取 request body，因为 body 只能读取一次。
			const update = await request.json();
			ctx.waitUntil(handleWebhook(update, env, ctx));
			return new Response('OK', { status: 200 });
		}
		// #endregion 🤖 Telegram Webhook 路由
		
		// #region 📱 Mini App 和 API 路由
		// Mini App 静态资源和 API 路由统一处理
		if (url.pathname.startsWith('/miniapp/') || url.pathname === '/miniapp' || 
		    url.pathname.startsWith('/api/') || url.pathname === '/icon.svg') {
			return await handleMiniApp(request, env, ctx);
		}
		// #endregion 📱 Mini App 和 API 路由
		
		// #region 📄 静态资源处理
		// 静态资源处理（只处理GET请求）
		if (request.method === 'GET' && (url.pathname === '/' || url.pathname.startsWith('/admin') || url.pathname.includes('.'))) {
			try {
				// 只有在 ASSETS 绑定存在时才尝试获取静态资源
				if (env.ASSETS) {
					return await env.ASSETS.fetch(request);
				} else {
					// 在本地开发环境中，如果没有 ASSETS 绑定，对根路径返回默认响应
					if (url.pathname === '/' || url.pathname.startsWith('/admin')) {
						return new Response('Development mode - Static assets not available', { 
							status: 200,
							headers: { 'Content-Type': 'text/plain; charset=utf-8' }
						});
					}
					return new Response('Not Found', { status: 404 });
				}
			} catch (error) {
				// 如果静态资源不存在，对于根路径返回 index.html
				if (url.pathname === '/' || url.pathname.startsWith('/admin')) {
					if (env.ASSETS) {
						const indexRequest = new Request(new URL('/index.html', request.url), request);
						return await env.ASSETS.fetch(indexRequest);
					} else {
						return new Response('Development mode - Static assets not available', { 
							status: 200,
							headers: { 'Content-Type': 'text/plain; charset=utf-8' }
						});
					}
				}
				return new Response('Not Found', { status: 404 });
			}
		}
		// #endregion 📄 静态资源处理
		
		// #region 🧪 广告时区修复测试 API
		// 广告时间触发测试的API
		if (url.pathname === '/test-timezone-fix' && request.method === 'POST') {
			try {
				// 验证token
				const requestBody = await request.json();
				if (requestBody.token !== 'UiGZnHei6lp39V') {
					return new Response('Not Found', { status: 404 });
				}
				
				const nowUTC = new Date();
				const beijingOffset = 8 * 60 * 60 * 1000;
				const nowBeijing = new Date(nowUTC.getTime() + beijingOffset);
				
				// 获取所有主频道广告规则
				const campaigns = await env.DB.prepare(`
					SELECT id, name, publish_time, target_channel_id, is_active, frequency_days 
					FROM ad_campaigns 
					WHERE is_active = 1
					ORDER BY id
				`).all();

				const testResults = [];
				for (const campaign of campaigns.results || []) {
					const [targetHour, targetMinute] = campaign.publish_time.split(':').map(Number);
					const currentHourBeijing = nowBeijing.getHours();
					const currentMinuteBeijing = nowBeijing.getMinutes();
					
					// 1. 检查时间条件
					const currentTimeMinutes = currentHourBeijing * 60 + currentMinuteBeijing;
					const targetTimeMinutes = targetHour * 60 + targetMinute;
					const timeReached = currentTimeMinutes >= targetTimeMinutes;
					
					// 2. 检查频率条件 - 查找最后一次发送记录
					// 检查频率限制（是否已发送过）- 简化逻辑只匹配campaign_id
					const lastPost = await env.DB.prepare(`
						SELECT sent_at FROM ad_posts 
						WHERE campaign_id = ?
						ORDER BY sent_at DESC 
						LIMIT 1
					`).bind(campaign.id).first();
					
					let frequencyOk = false;
					let lastSentInfo = "从未发送";
					let daysSinceLastSent = "N/A";
					
					if (!lastPost) {
						frequencyOk = true;
						lastSentInfo = "从未发送过";
					} else {
						// 计算距离上次发送的天数（使用北京时间）
						const beijingNow = new Date(nowUTC.getTime() + beijingOffset);
						const lastSentDate = new Date(lastPost.sent_at);
						const beijingLastSent = new Date(lastSentDate.getTime() + beijingOffset);
						
						// 基于北京时间的日期计算天数差
						const beijingToday = new Date(beijingNow.getFullYear(), beijingNow.getMonth(), beijingNow.getDate());
						const beijingLastSentDay = new Date(beijingLastSent.getFullYear(), beijingLastSent.getMonth(), beijingLastSent.getDate());
						const daysDiff = Math.floor((beijingToday - beijingLastSentDay) / (1000 * 60 * 60 * 24));
						
						daysSinceLastSent = daysDiff;
						frequencyOk = daysDiff > campaign.frequency_days;
						lastSentInfo = `${daysDiff}天前 (需间隔${campaign.frequency_days}天)`;
					}
					
					// 3. 综合判断
					const canTrigger = timeReached && frequencyOk;
					
					let status;
					if (!timeReached) {
						status = "⏰ 时间未到";
					} else if (!frequencyOk) {
						status = "🚫 频率限制";
					} else {
						status = "✅ 可以触发";
					}
					
					testResults.push({
						id: campaign.id,
						name: campaign.name,
						publishTime: campaign.publish_time,
						currentBeijing: `${currentHourBeijing.toString().padStart(2, '0')}:${currentMinuteBeijing.toString().padStart(2, '0')}`,
						timeReached,
						frequencyOk,
						canTrigger,
						lastSent: lastSentInfo,
						daysSinceLastSent,
						frequencyDays: campaign.frequency_days,
						status
					});
				}

				return new Response(JSON.stringify({
					currentUTC: nowUTC.toISOString(),
					currentBeijing: `${nowBeijing.getHours().toString().padStart(2, '0')}:${nowBeijing.getMinutes().toString().padStart(2, '0')}`,
					campaigns: testResults,
					explanation: {
						timeReached: "当前北京时间是否已达到设定的发布时间",
						frequencyOk: "是否满足发送频率要求（距上次发送超过指定天数）",
						canTrigger: "综合判断：时间+频率都满足才能触发",
						status: "⏰时间未到 | 🚫频率限制 | ✅可以触发"
					}
				}, null, 2), {
					headers: { 'Content-Type': 'application/json' }
				});
			} catch (error) {
				return new Response(JSON.stringify({ 
					error: error.message 
				}), { 
					status: 500, 
					headers: { 'Content-Type': 'application/json' } 
				});
			}
		}
		// #endregion 🧪 广告时区修复测试 API
		
		// #region 🔚 默认与回退响应
		// 默认响应（非POST请求）
		if (request.method !== 'POST') {
			return new Response('Telegram Bot is running!', { 
				status: 200,
				headers: { 'Content-Type': 'text/plain; charset=utf-8' }
			});
		}

		// 未匹配的请求
		return new Response('Bad Request', { status: 400 });
		// #endregion 🔚 默认与回退响应
	},

	// #region ⏰ 定时任务处理器 (scheduled)
	// 新增：定时任务处理器
	async scheduled(event, env, ctx) {
		// 初始化环境（定时任务同样设置一次）
		initializeEnvironment(env);
		Logger.tagLog('CRON', '🕐 定时任务被触发', {
			scheduledTime: event.scheduledTime,
			cron: event.cron,
			eventType: event.type || 'scheduled'
		});

		try {
			Logger.info('📋 开始执行定时任务列表...');
			
			// #region 📢 广告调度
			// 导入并执行广告管理器
			Logger.debug('📢 导入广告管理器模块...');
			const { AdManager } = await import('./handlers/adManager.js');
			globalThis.env = env; // 设置全局env，供AdManager使用
			
			Logger.debug('📢 执行广告调度检查...');
			await AdManager.checkAndExecuteScheduledAds(env.DB);
			Logger.success('✅ 广告调度检查完成');
			// #endregion 📢 广告调度
			
			// #region 🧹 记录清理
			// 导入并执行投稿记录清理
			Logger.debug('🧹 导入投稿记录清理模块...');
			const { cleanupExpiredSubmissions } = await import('./utils/submissionCleanup.js');
			
			Logger.debug('🧹 执行投稿记录清理...');
			await cleanupExpiredSubmissions(env.DB, env.TELEGRAM_BOT_TOKEN);
			Logger.success('✅ 投稿记录清理完成');
			// #endregion 🧹 记录清理
			
			Logger.success('🎉 定时任务执行完成');
		} catch (error) {
			Logger.error('❌ 定时任务执行失败', error);
			// 注意：不要抛出异常，避免影响 Worker 的稳定性
		}
	},
	// #endregion ⏰ 定时任务处理器 (scheduled)

	// #region 🔄 队列处理器 (queue)
	// 处理验证清理队列
	async queue(batch, env) {
		// 初始化环境（队列处理器也设置一次）
		initializeEnvironment(env);
		Logger.tagLog('QUEUE', '🔄 队列处理器被触发', {
			batchSize: batch.messages.length,
			queueName: batch.queue
		});

		try {
			const { handleVerificationCleanupQueue } = await import('./handlers/memberManagement/index.js');
			
			for (const message of batch.messages) {
				try {
					Logger.debug('🧹 处理队列消息:', { 
						messageId: message.id, 
						body: message.body 
					});
					
					if (message.body && message.body.action) {
						await handleVerificationCleanupQueue(message.body, env);
						message.ack(); // 确认消息处理成功
						Logger.debug('✅ 队列消息处理成功');
					} else {
						Logger.warn('⚠️ 队列消息格式不正确，跳过处理');
						message.ack(); // 确认消息（避免重复处理）
					}
				} catch (error) {
					Logger.error('❌ 队列消息处理失败:', error);
					message.retry(); // 重试消息
				}
			}
			
			Logger.success('🎉 队列批次处理完成');
		} catch (error) {
			Logger.error('❌ 队列处理器执行失败:', error);
			// 标记整个批次重试
			for (const message of batch.messages) {
				message.retry();
			}
		}
	}
	// #endregion 🔄 队列处理器 (queue)
};


