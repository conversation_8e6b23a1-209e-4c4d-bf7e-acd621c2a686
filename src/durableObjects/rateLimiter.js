import Logger from '../utils/logger.js';

export class TelegramRateLimiter {
	constructor(state, env) {
		this.state = state;
		this.env = env;

		this.stats = {
			totalRequests: 0,
			rateLimitHits: 0, // Requests that had to wait
			averageWaitTime: 0,
			lastResetTime: Date.now(),
		};

		this.initialized = false;
		// The initPromise ensures that the state is loaded before any requests are processed.
		this.initPromise = this.initializeState();

		// Set a conservative maximum wait time to prevent fetch timeouts from the calling worker.
		// Cloudflare's worker-to-worker fetch timeout is around 100 seconds.
		this.MAX_WAIT_TIME = 90000; // 90 seconds
	}

	async initializeState() {
		if (this.initialized) return;

		try {
			const persistedState = await this.state.storage.get('rateLimiterState');
			if (persistedState) {
				// The next time a request of a given type can be sent.
				this.nextAvailableTime = persistedState.nextAvailableTime || { default: 0, callbackQuery: 0 };
				Logger.debug('从存储中恢复状态:', this.nextAvailableTime);
			} else {
				this.nextAvailableTime = { default: 0, callbackQuery: 0 };
			}
		} catch (error) {
			Logger.error('恢复状态失败，使用默认值:', error);
			this.nextAvailableTime = { default: 0, callbackQuery: 0 };
		}

		this.initialized = true;
	}

	// Persist the critical state to durable storage.
	async persistState() {
		try {
			await this.state.storage.put('rateLimiterState', {
				nextAvailableTime: this.nextAvailableTime,
			});
		} catch (error) {
			Logger.error('持久化状态失败:', error);
		}
	}

	async fetch(request) {
		if (!request) {
			return new Response(JSON.stringify({ error: 'Invalid request object' }), { status: 400 });
		}

		let requestData = {};
		try {
			requestData = await request.json();
		} catch (e) {
			// It might be a GET request for stats, proceed with empty data.
		}

		const url = new URL(request.url);
		const action = url.pathname.split('/').pop() || requestData.action || 'checkLimit';

		return this.processAction(action, requestData);
	}

	async processAction(action, data) {
		Logger.debug('处理操作:', action, data);

		switch (action) {
			case 'checkLimit':
				return this.handleCheckLimit(data);
			case 'reportError':
				return this.handleReportError(data);
			case 'resetLimits':
				return this.handleResetLimits();
			case 'getStats':
				return new Response(JSON.stringify(this.getStats()), { headers: { 'Content-Type': 'application/json' } });
			default:
				return new Response(JSON.stringify({ error: 'Unknown action' }), { status: 400 });
		}
	}

	async handleCheckLimit(data) {
		await this.initPromise;

		const requestType = this.getRequestType(data);
		const now = Date.now();
		this.stats.totalRequests++;

		// callbackQuery: 30/s -> 34ms interval.
		// default: 26/s -> 39ms interval.
		const minInterval = requestType === 'callbackQuery' ? 34 : 39;

		// The single-threaded nature of DOs guarantees this is atomic.
		let nextAvailableTime = this.nextAvailableTime[requestType] || 0;
		const scheduledTime = Math.max(nextAvailableTime, now);

		// Set the time for the *next* request.
		this.nextAvailableTime[requestType] = scheduledTime + minInterval;

		const waitTime = scheduledTime - now;

		if (waitTime > this.MAX_WAIT_TIME) {
			Logger.warn(`请求被拒绝，因计算出的等待时间 ${waitTime}ms 超过了最大阈值 ${this.MAX_WAIT_TIME}ms`);
			return new Response(JSON.stringify({
				canProceed: false,
				isThrottled: true, // A specific flag to indicate that this was a deliberate rejection due to high load.
				waitTime: waitTime,
				requestType,
			}), {
				status: 429, // "Too Many Requests" is a fitting HTTP status code.
				headers: { 'Content-Type': 'application/json' },
			});
		}

		if (waitTime > 0) {
			this.stats.rateLimitHits++;
			if (this.stats.rateLimitHits > 0) {
				this.stats.averageWaitTime = (this.stats.averageWaitTime * (this.stats.rateLimitHits - 1) + waitTime) / this.stats.rateLimitHits;
			} else {
				this.stats.averageWaitTime = waitTime;
			}
			await new Promise(resolve => setTimeout(resolve, waitTime));
		}

		// Persist the new state asynchronously without blocking the response.
		this.state.waitUntil(this.persistState());

		return new Response(JSON.stringify({
			canProceed: true,
			isThrottled: false,
			waited: waitTime,
			requestType,
		}), {
			headers: { 'Content-Type': 'application/json' },
		});
	}

	async handleReportError(data) {
		await this.initPromise;
		const { error_code, retry_after, description } = data;
		const requestType = this.getRequestType(data);
		const now = Date.now();

		let penalty = 0;
		if (error_code === 429) {
			penalty = (retry_after || 1) * 1000;
			Logger.warn(`速率限制错误(429)报告: ${requestType} 类型增加等待 ${penalty}ms`);
		} else if (String(error_code).startsWith('5')) {
			penalty = 2000; // For 5xx server errors, add a fixed 2s penalty.
			Logger.warn(`服务器错误(5xx)报告: ${requestType} 类型增加等待 ${penalty}ms`);
		} else if (description && description.includes('flood')) {
			penalty = 5000; // For flood waits, add a larger 5s penalty.
			Logger.warn(`洪泛错误报告: ${requestType} 类型增加等待 ${penalty}ms`);
		}

		if (penalty > 0) {
			// Push the next available time further into the future by adding the penalty.
			this.nextAvailableTime[requestType] = (this.nextAvailableTime[requestType] || now) + penalty;
			this.state.waitUntil(this.persistState());
		}

		return new Response(JSON.stringify({ success: true, newNextAvailableTime: this.nextAvailableTime[requestType] }), {
			headers: { 'Content-Type': 'application/json' },
		});
	}

	getStats() {
		return {
			...this.stats,
			uptime: (Date.now() - this.stats.lastResetTime) / 1000,
			currentTime: Date.now(),
			nextAvailableTime: this.nextAvailableTime,
		};
	}

	async handleResetLimits() {
		await this.initPromise;
		Logger.info('重置所有速率限制状态');
		this.nextAvailableTime = { default: 0, callbackQuery: 0 };
		this.stats = {
			totalRequests: 0,
			rateLimitHits: 0,
			averageWaitTime: 0,
			lastResetTime: Date.now(),
		};

		await this.state.storage.deleteAll();
		await this.persistState(); // Persist the reset state

		return new Response(JSON.stringify({ success: true, message: '所有限制已重置' }), {
			headers: { 'Content-Type': 'application/json' },
		});
	}

	getRequestType(data) {
		const { apiMethod, apiUrl } = data || {};
		if (apiMethod === 'answerCallbackQuery' || (apiUrl && apiUrl.includes('answerCallbackQuery'))) {
			return 'callbackQuery';
		}
		return 'default';
	}
}
