import Logger from '../utils/logger.js';

/**
 * 通用键值存储 Durable Object
 * 提供简单的 get/set/delete/clear 操作
 */
export class KeyValueStore {
	constructor(state, env) {
		this.state = state;
		this.env = env;
		this.storage = state.storage;
	}

	/**
	 * 处理请求
	 * @param {Request} request 
	 * @returns {Response}
	 */
	async fetch(request) {
		try {
			const url = new URL(request.url);
			const action = url.pathname.split('/').pop();
			
			let data = {};
			if (request.method === 'POST' && request.body) {
				data = await request.json();
			}

			Logger.debug('KeyValueStore 操作:', action, data);

			switch (action) {
				case 'get':
					return this.handleGet(data);
				case 'set':
					return this.handleSet(data);
				case 'delete':
					return this.handleDelete(data);
				case 'clear':
					return this.handleClear();
				case 'keys':
					return this.handleKeys(data);
				case 'size':
					return this.handleSize();
				default:
					return new Response(
						JSON.stringify({ error: 'Unknown action' }),
						{
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						}
					);
			}
		} catch (error) {
			Logger.error('KeyValueStore 错误:', error);
			return new Response(
				JSON.stringify({ error: error.message }),
				{
					status: 500,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}
	}

	/**
	 * 获取值
	 * @param {Object} data - {key: string}
	 */
	async handleGet(data) {
		const { key } = data;
		if (!key) {
			return new Response(
				JSON.stringify({ error: 'Key is required' }),
				{ status: 400, headers: { 'Content-Type': 'application/json' } }
			);
		}

		const value = await this.storage.get(key);
		return new Response(
			JSON.stringify({ key, value }),
			{ headers: { 'Content-Type': 'application/json' } }
		);
	}

	/**
	 * 设置值
	 * @param {Object} data - {key: string, value: any, ttl?: number}
	 */
	async handleSet(data) {
		const { key, value, ttl } = data;
		if (!key) {
			return new Response(
				JSON.stringify({ error: 'Key is required' }),
				{ status: 400, headers: { 'Content-Type': 'application/json' } }
			);
		}

		// 如果设置了 TTL，使用 putWithOptions
		if (ttl && ttl > 0) {
			const expirationTime = Date.now() + (ttl * 1000); // TTL in seconds
			await this.storage.put(key, value, { 
				expirationTtl: ttl 
			});
		} else {
			await this.storage.put(key, value);
		}

		return new Response(
			JSON.stringify({ success: true, key, value }),
			{ headers: { 'Content-Type': 'application/json' } }
		);
	}

	/**
	 * 删除值
	 * @param {Object} data - {key: string}
	 */
	async handleDelete(data) {
		const { key } = data;
		if (!key) {
			return new Response(
				JSON.stringify({ error: 'Key is required' }),
				{ status: 400, headers: { 'Content-Type': 'application/json' } }
			);
		}

		const deleted = await this.storage.delete(key);
		return new Response(
			JSON.stringify({ success: true, key, deleted }),
			{ headers: { 'Content-Type': 'application/json' } }
		);
	}

	/**
	 * 清空所有数据
	 */
	async handleClear() {
		await this.storage.deleteAll();
		return new Response(
			JSON.stringify({ success: true, message: 'All data cleared' }),
			{ headers: { 'Content-Type': 'application/json' } }
		);
	}

	/**
	 * 获取所有键
	 * @param {Object} data - {prefix?: string, limit?: number}
	 */
	async handleKeys(data) {
		const { prefix, limit } = data;
		const options = {};
		
		if (prefix) options.prefix = prefix;
		if (limit) options.limit = limit;

		const keys = await this.storage.list(options);
		const keyArray = Array.from(keys.keys());

		return new Response(
			JSON.stringify({ keys: keyArray }),
			{ headers: { 'Content-Type': 'application/json' } }
		);
	}

	/**
	 * 获取存储大小
	 */
	async handleSize() {
		const keys = await this.storage.list();
		const size = keys.size;

		return new Response(
			JSON.stringify({ size }),
			{ headers: { 'Content-Type': 'application/json' } }
		);
	}
} 