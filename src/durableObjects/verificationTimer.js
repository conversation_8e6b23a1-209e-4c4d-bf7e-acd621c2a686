import Logger from '../utils/logger.js';
import { sendTelegramRequest } from '../utils/telegramApi.js';
import { handleVerificationFailure } from '../handlers/memberManagement/index.js';
import { SessionManager } from '../handlers/memberManagement/modules/sessionManager.js';

/**
 * 验证超时定时器 Durable Object
 * 专门处理验证流程中的超时任务，替代不稳定的setTimeout
 */
export class VerificationTimer {
	constructor(state, env) {
		this.state = state;
		this.env = env;
		this.timers = new Map(); // 内存中的定时器映射
	}

	// #region 📅 定时器管理

	/**
	 * 设置验证超时任务
	 * @param {Object} request 包含定时器信息的请求
	 */
	async fetch(request) {
		const url = new URL(request.url);
		const action = url.pathname.split('/').pop();

		try {
			switch (action) {
				case 'schedule':
					return await this.handleSchedule(request);
				case 'cancel':
					return await this.handleCancel(request);
				case 'alarm':
					// 这个是系统调用的alarm处理
					return new Response('Alarm handled', { status: 200 });
				default:
					return new Response('Unknown action', { status: 400 });
			}
		} catch (error) {
			Logger.error('❌ VerificationTimer 处理失败:', error);
			return new Response('Internal error', { status: 500 });
		}
	}

	/**
	 * 调度新的超时任务
	 */
	async handleSchedule(request) {
		const data = await request.json();
		const { sessionId, userId, chatId, action, delaySeconds } = data;

		Logger.debug('⏰ [VT.schedule] 调度验证超时任务', { sessionId, userId, chatId, action, delaySeconds });

		// 计算触发时间
		const triggerTime = Date.now() + (delaySeconds * 1000);
		
		// 存储任务信息
		const taskData = { sessionId, userId, chatId, action, triggerTime };
		await this.state.storage.put(`task:${sessionId}:${action}`, taskData);
		
		// 设置系统alarm（Durable Objects的内置定时功能）
		const currentAlarm = await this.state.storage.getAlarm();
		const now = Date.now();
		Logger.debug('[VT.schedule] 当前Alarm与新触发时间', {
			currentAlarmISO: currentAlarm ? new Date(currentAlarm).toISOString() : null,
			newTriggerISO: new Date(triggerTime).toISOString()
		});
		if (!currentAlarm || currentAlarm < now || triggerTime < currentAlarm) {
			await this.state.storage.setAlarm(triggerTime);
			Logger.debug('🔔 [VT.schedule] 已更新 alarm', { triggerTimeISO: new Date(triggerTime).toISOString() });
		}

		return new Response('Task scheduled', { status: 200 });
	}

	/**
	 * 取消超时任务
	 */
	async handleCancel(request) {
		const data = await request.json();
		const { sessionId, action } = data;

		Logger.debug('❌ 取消验证超时任务:', { sessionId, action });

		// 删除任务
		await this.state.storage.delete(`task:${sessionId}:${action}`);

		return new Response('Task cancelled', { status: 200 });
	}

	/**
	 * 处理 Durable Objects alarm 触发
	 * 这是系统调用的方法，当alarm时间到达时会自动触发
	 */
	async alarm() {
		const alarmStart = Date.now();
		Logger.debug('🔔 [VT.alarm] 触发', { nowISO: new Date(alarmStart).toISOString() });

		try {
			const now = Date.now();
			const tasks = await this.state.storage.list({ prefix: 'task:' });
			let nextAlarmTime = null;
			const dueTasks = [];
            let processedAny = false;

			for (const [key, taskData] of tasks) {
				if (taskData.triggerTime <= now) {
					// 任务已到期，执行任务
					dueTasks.push(`${taskData.sessionId}:${taskData.action}`);
					Logger.info('⏰ [VT.alarm] 执行到期的验证超时任务', {
						sessionId: taskData.sessionId,
						action: taskData.action,
						chatId: taskData.chatId,
						plannedISO: new Date(taskData.triggerTime).toISOString()
					});
					await this.executeTask(taskData);
					processedAny = true;
					
					// 删除已执行的任务
					await this.state.storage.delete(key);
				} else {
					// 任务未到期，更新下次alarm时间
					if (!nextAlarmTime || taskData.triggerTime < nextAlarmTime) {
						nextAlarmTime = taskData.triggerTime;
					}
				}
			}

			// 设置下次alarm（优先确保1秒后再跑一轮以消化剩余已到期任务）
			let targetAlarm = null;
			if (nextAlarmTime) targetAlarm = nextAlarmTime;
			if (processedAny) {
				const soon = Date.now() + 1000;
				targetAlarm = targetAlarm ? Math.min(targetAlarm, soon) : soon;
			}
			if (targetAlarm) {
				await this.state.storage.setAlarm(targetAlarm);
				Logger.debug('🔔 [VT.alarm] 已设置下次 alarm', { nextAlarmISO: new Date(targetAlarm).toISOString() });
			}

			Logger.debug('📊 [VT.alarm] 处理完成', {
				processedCount: dueTasks.length,
				durationMs: Date.now() - alarmStart
			});
		} catch (error) {
			Logger.error('❌ [VT.alarm] 处理失败:', error);
		}
	}

	// #endregion 📅 定时器管理

	// #region ⚡ 任务执行

	/**
	 * 执行超时任务
	 */
	async executeTask(taskData) {
		const { sessionId, userId, chatId, action } = taskData;

		try {
			const sessionManager = new SessionManager(this.env);
			const session = await sessionManager.getSession(sessionId);

			if (!session) {
				Logger.debug('⏭️ 会话不存在，跳过任务:', { sessionId });
				return;
			}

			switch (action) {
				case 'group_timeout':
					await this.handleGroupTimeout(session, sessionId, sessionManager);
					break;
				case 'math_timeout':
					await this.handleMathTimeout(session, sessionId, sessionManager);
					break;
				default:
					Logger.warn('⚠️ 未知的超时任务类型:', action);
			}
		} catch (error) {
			Logger.error('❌ 执行超时任务失败:', error);
		}
	}

	/**
	 * 处理群组验证超时
	 */
	async handleGroupTimeout(session, sessionId, sessionManager) {
		const { userID: userId, chatID: chatId, userName } = session;

		if (session.skipGroupTimeout) {
			Logger.debug('✅ 用户已点击验证按钮，跳过群组超时:', { userId });
			return;
		}

		Logger.info('⏰ 群组验证超时，用户未点击按钮:', { userId, chatId });

		// 删除群组中的验证消息
		if (session.verificationMessageId) {
			try {
				await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
					chat_id: chatId,
					message_id: session.verificationMessageId
				});
				Logger.debug('🗑️ 验证消息已删除:', { messageId: session.verificationMessageId });
			} catch (error) {
				Logger.debug('⚠️ 删除验证消息失败（可能已不存在）:', error.message);
			}
		}

		// 发送超时提示消息给用户
		try {
			await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
				chat_id: userId,
				text: '⏰ 验证超时，您未能在规定时间内点击验证按钮'
			});
			Logger.debug('📤 已发送群组超时提示消息给用户:', { userId });
		} catch (error) {
			Logger.debug('⚠️ 发送群组超时提示消息失败:', error.message);
		}

		// 处理验证失败
		await handleVerificationFailure(userId, chatId, userName || '用户', this.env);

		// 删除会话
		await sessionManager.deleteSession(sessionId);
	}

	/**
	 * 处理数学题验证超时
	 */
	async handleMathTimeout(session, sessionId, sessionManager) {
		const { userID: userId, chatID: chatId, userName } = session;

		if (session.skipMathTimeout) {
			Logger.debug('✅ 用户已完成答题，跳过数学题超时:', { userId });
			return;
		}

		Logger.info('⏰ 数学题验证超时，用户未完成答题:', { userId, chatId });

		// 删除私聊中的验证消息
		if (session.privateMessageId) {
			try {
				await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
					chat_id: userId,
					message_id: session.privateMessageId
				});
				Logger.debug('🗑️ 数学题超时，私聊验证消息已删除:', { messageId: session.privateMessageId });
			} catch (error) {
				Logger.debug('⚠️ 删除私聊验证消息失败（可能已不存在）:', error.message);
			}
		}

		// 发送超时提示消息给用户
		try {
			await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
				chat_id: userId,
				text: '⏰ 验证超时，您未能在规定时间内完成验证'
			});
			Logger.debug('📤 已发送超时提示消息给用户:', { userId });
		} catch (error) {
			Logger.debug('⚠️ 发送超时提示消息失败:', error.message);
		}

		// 处理验证失败
		await handleVerificationFailure(userId, chatId, userName || '用户', this.env);

		// 删除会话
		await sessionManager.deleteSession(sessionId);
	}

	// #endregion ⚡ 任务执行
}