import Logger from '../utils/logger.js';
import { sendTelegramRequest } from '../utils/telegramApi.js';

/**
 * 消息调度器 Durable Object
 * 处理各种延迟消息操作，如自动删除、定时发送等
 */
export class MessageScheduler {
	constructor(state, env) {
		this.state = state;
		this.env = env;
	}

	/**
	 * 处理调度请求
	 */
	async fetch(request) {
		const url = new URL(request.url);
		const action = url.pathname.split('/').pop();

		try {
			switch (action) {
				case 'schedule-delete':
					return await this.handleScheduleDelete(request);
				case 'cancel-delete':
					return await this.handleCancelDelete(request);
				case 'alarm':
					return new Response('Alarm handled', { status: 200 });
				default:
					return new Response('Unknown action', { status: 400 });
			}
		} catch (error) {
			Logger.error('❌ MessageScheduler 处理失败:', error);
			return new Response('Internal error', { status: 500 });
		}
	}

	/**
	 * 调度消息删除
	 */
	async handleScheduleDelete(request) {
		const data = await request.json();
		const { taskId, chatId, messageId, delaySeconds, description } = data;

		Logger.debug('⏰ [MS.schedule] 调度消息删除任务', {
			taskId,
			chatId,
			messageId,
			delaySeconds,
			description
		});

		// 计算触发时间
		const triggerTime = Date.now() + (delaySeconds * 1000);
		
		// 存储任务信息
		const taskData = { 
			taskId, 
			chatId, 
			messageId, 
			triggerTime, 
			description: description || '消息删除',
			action: 'delete_message'
		};
		await this.state.storage.put(`task:${taskId}`, taskData);
		
		// 设置系统alarm
		const currentAlarm = await this.state.storage.getAlarm();
		const now = Date.now();
		Logger.debug('🔎 [MS.schedule] 当前Alarm与新触发时间', {
			currentAlarmISO: currentAlarm ? new Date(currentAlarm).toISOString() : null,
			newTriggerISO: new Date(triggerTime).toISOString(),
		});
		if (!currentAlarm || currentAlarm < now || triggerTime < currentAlarm) {
			await this.state.storage.setAlarm(triggerTime);
			Logger.debug('🔔 [MS.schedule] 已更新 alarm', { triggerTimeISO: new Date(triggerTime).toISOString() });
		}

		return new Response('Delete task scheduled', { status: 200 });
	}

	/**
	 * 取消消息删除
	 */
	async handleCancelDelete(request) {
		const data = await request.json();
		const { taskId } = data;

		Logger.debug('❌ 取消消息删除任务:', { taskId });

		await this.state.storage.delete(`task:${taskId}`);

		return new Response('Delete task cancelled', { status: 200 });
	}

	/**
	 * 处理 alarm 触发
	 */
	async alarm() {
		const alarmStart = Date.now();
		Logger.debug('🔔 [MS.alarm] 触发', { nowISO: new Date(alarmStart).toISOString() });

		try {
			const now = Date.now();
			const tasks = await this.state.storage.list({ prefix: 'task:' });
			let nextAlarmTime = null;
			const dueTasks = [];
            let processedAny = false;

			for (const [key, taskData] of tasks) {
				if (taskData.triggerTime <= now) {
					// 任务已到期，执行任务
					dueTasks.push(taskData.taskId || key);
					Logger.info('⏰ [MS.alarm] 执行到期的消息任务', {
						taskId: taskData.taskId || key,
						chatId: taskData.chatId,
						messageId: taskData.messageId,
						plannedISO: new Date(taskData.triggerTime).toISOString()
					});
					await this.executeTask(taskData);
					processedAny = true;
					// 删除已执行的任务
					await this.state.storage.delete(key);
				} else {
					// 任务未到期，更新下次alarm时间
					if (!nextAlarmTime || taskData.triggerTime < nextAlarmTime) {
						nextAlarmTime = taskData.triggerTime;
					}
				}
			}

			// 设置下次alarm（优先确保1秒后再跑一轮以消化剩余已到期任务）
			let targetAlarm = null;
			if (nextAlarmTime) targetAlarm = nextAlarmTime;
			if (processedAny) {
				const soon = Date.now() + 1000;
				targetAlarm = targetAlarm ? Math.min(targetAlarm, soon) : soon;
			}
			if (targetAlarm) {
				await this.state.storage.setAlarm(targetAlarm);
				Logger.debug('🔔 [MS.alarm] 已设置下次 alarm', { nextAlarmISO: new Date(targetAlarm).toISOString() });
			}

			Logger.debug('📊 [MS.alarm] 处理完成', {
				processedCount: dueTasks.length,
				durationMs: Date.now() - alarmStart
			});
		} catch (error) {
			Logger.error('❌ [MS.alarm] 处理失败:', error);
		}
	}

	/**
	 * 执行具体任务
	 */
	async executeTask(taskData) {
		const { action, chatId, messageId, description } = taskData;

		try {
			switch (action) {
				case 'delete_message':
					await this.deleteMessage(chatId, messageId, description);
					break;
				default:
					Logger.warn('⚠️ 未知的消息任务类型:', action);
			}
		} catch (error) {
			Logger.error('❌ 执行消息任务失败:', error);
		}
	}

	/**
	 * 删除消息
	 */
	async deleteMessage(chatId, messageId, description) {
		try {
			const res = await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
				chat_id: chatId,
				message_id: messageId
			});
			if (res?.ok) {
				Logger.debug('🗑️ [MS.delete] 自动删除消息成功', { chatId, messageId, description });
			} else {
				Logger.warn('⚠️ [MS.delete] 删除消息失败(非异常)', {
					chatId,
					messageId,
					description,
					ok: res?.ok,
					error_code: res?.error_code,
					descriptionText: res?.description
				});
			}
		} catch (error) {
			Logger.debug('⚠️ [MS.delete] 自动删除消息失败（可能已不存在）', { 
				chatId, 
				messageId, 
				description,
				error: error.message 
			});
		}
	}
}