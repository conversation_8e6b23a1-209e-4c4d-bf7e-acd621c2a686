import Logger from '../../../utils/logger.js';

/**
 * 验证定时器工具函数
 * 封装对 VerificationTimer Durable Object 的调用
 */

/**
 * 调度验证超时任务
 * @param {Object} env 环境变量
 * @param {string} sessionId 会话ID
 * @param {number} userId 用户ID
 * @param {string} chatId 群组ID
 * @param {string} action 任务类型 (group_timeout/math_timeout)
 * @param {number} delaySeconds 延迟秒数
 */
export async function scheduleVerificationTimeout(env, sessionId, userId, chatId, action, delaySeconds) {
    try {
        // 获取 Durable Object 实例
        const timerId = env.VERIFICATION_TIMER.idFromName('verification-timer');
        const timerObj = env.VERIFICATION_TIMER.get(timerId);

        // 调用调度方法
        const response = await timerObj.fetch(new Request('https://timer/schedule', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                sessionId,
                userId,
                chatId,
                action,
                delaySeconds
            })
        }));

        if (!response.ok) {
            throw new Error(`调度失败: ${response.status}`);
        }

        Logger.debug('✅ 验证超时任务已调度:', { sessionId, action, delaySeconds });
        return true;

    } catch (error) {
        Logger.error('❌ 调度验证超时任务失败:', error);
        return false;
    }
}

/**
 * 取消验证超时任务
 * @param {Object} env 环境变量
 * @param {string} sessionId 会话ID
 * @param {string} action 任务类型 (group_timeout/math_timeout)
 */
export async function cancelVerificationTimeout(env, sessionId, action) {
    try {
        // 获取 Durable Object 实例
        const timerId = env.VERIFICATION_TIMER.idFromName('verification-timer');
        const timerObj = env.VERIFICATION_TIMER.get(timerId);

        // 调用取消方法
        const response = await timerObj.fetch(new Request('https://timer/cancel', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                sessionId,
                action
            })
        }));

        if (!response.ok) {
            throw new Error(`取消失败: ${response.status}`);
        }

        Logger.debug('✅ 验证超时任务已取消:', { sessionId, action });
        return true;

    } catch (error) {
        Logger.error('❌ 取消验证超时任务失败:', error);
        return false;
    }
}

/**
 * 标记会话跳过超时（用户已完成验证）
 * @param {Object} sessionManager 会话管理器
 * @param {string} sessionId 会话ID
 * @param {string} timeoutType 超时类型 (group/math)
 */
export async function markSkipTimeout(sessionManager, sessionId, timeoutType) {
    try {
        const session = await sessionManager.getSession(sessionId);
        if (!session) {
            Logger.debug('⚠️ 会话不存在，无法标记跳过超时:', { sessionId });
            return false;
        }

        // 设置跳过标志
        if (timeoutType === 'group') {
            session.skipGroupTimeout = true;
        } else if (timeoutType === 'math') {
            session.skipMathTimeout = true;
        }

        // 更新会话
        await sessionManager.kv.put(sessionId, JSON.stringify(session), { expirationTtl: 120 });
        
        Logger.debug('✅ 已标记跳过超时:', { sessionId, timeoutType });
        return true;

    } catch (error) {
        Logger.error('❌ 标记跳过超时失败:', error);
        return false;
    }
}