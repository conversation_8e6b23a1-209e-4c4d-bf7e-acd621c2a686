// #region 📦 依赖导入
import Logger from '../../utils/logger.js';
import { getGroupConfig } from '../../utils/groupConfigUtils.js';
import { userExceedsFailureLimit, createOrUpdateVerificationRecord } from '../../utils/userVerificationUtils.js';
import { sendTelegramRequest } from '../../utils/telegramApi.js';
import { SessionManager } from './modules/sessionManager.js';
import { VerificationManager } from './modules/verificationManager.js';
import { executeVerificationActions } from './actions/verificationActions.js';
import { executeWelcomeActions } from './actions/welcomeActions.js';
// #endregion 📦 依赖导入

// #region 🚪 成员变动主处理器

/**
 * 处理新成员加入
 * @param {Object} member 成员对象
 * @param {string} chatId 群组ID
 * @param {Object} env 环境变量
 * @param {Object} ctx 执行上下文
 * @returns {Promise<Object>} 处理结果
 */
export async function handleNewMember(member, chatId, env, ctx) {
    const startTime = Date.now();
    const userId = member.id;
    const userName = member.first_name || '新成员';
    const callId = `${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

    Logger.info('🆕 处理新成员加入:', { chatId, userId, userName, callId });

    try {
        // 🔧 调试：先恢复用户权限，防止之前的限制状态影响
        const verificationManager = new VerificationManager(env);
        try {
            await verificationManager.restoreUserPermissions(chatId, userId);
            Logger.debug('🔄 已重置用户权限状态:', { chatId, userId });
        } catch (error) {
            Logger.debug('⚠️ 重置用户权限失败（可能是新用户）:', error.message);
        }

        // 1. 检查群组配置
        const groupConfig = await getGroupConfig(env, chatId);
        if (!groupConfig || !groupConfig.new_member_verification) {
            Logger.debug('群组未启用验证，直接欢迎:', { chatId });
            await executeWelcomeActions({
                type: 'verified_user_welcome',
                member,
                chatId,
                env,
                ctx  // 🔧 修复：添加 ctx 参数
            });
            return { success: true, action: 'welcomed_without_verification' };
        }

        // 2. 检查用户是否已验证
        const isVerified = await verificationManager.isUserGloballyVerified(userId);
        
        if (isVerified) {
            Logger.info('✅ 用户已验证，直接欢迎:', { userId });
            await executeWelcomeActions({
                type: 'verified_user_welcome',
                member,
                chatId,
                env,
                ctx  // 🔧 修复：添加 ctx 参数
            });
            return { success: true, action: 'welcomed_verified_user' };
        }

        // 3. 检查失败次数
        if (await userExceedsFailureLimit(env, userId)) {
            Logger.info('🚫 用户失败次数过多，直接封禁:', { userId });
            await verificationManager.banUser(chatId, userId);
            return { success: true, action: 'banned_exceeded_attempts' };
        }

        // 4. 开始验证流程
        Logger.info('🚀 启动验证流程:', { userId, callId });
        await executeVerificationActions({ 
            type: 'start_verification', 
            member, 
            chatId, 
            env, 
            ctx 
        });
        
        return { success: true, action: 'verification_started' };

    } catch (error) {
        Logger.error('❌ 处理新成员失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 处理验证成功
 * @param {number} userId 用户ID
 * @param {string} chatId 群组ID
 * @param {string} userName 用户名
 * @param {Object} env 环境变量
 * @param {Object} ctx 执行上下文
 * @returns {Promise<Object>} 处理结果
 */
export async function handleVerificationSuccess(userId, chatId, userName, env, ctx) {
	const startTime = Date.now();
	
	Logger.info('🎉 处理验证成功:', { userId, chatId, userName });
	
	try {
		// 1. 初始化管理器
		const verificationManager = new VerificationManager(env);
		const sessionManager = new SessionManager(env);
		
		// 2. 清理验证会话
		await sessionManager.deleteSession(userId, chatId);
		
		// 3. 更新验证记录
		await verificationManager.markUserAsVerified(userId, userName, chatId);
		
		// 4. 恢复用户权限
		await verificationManager.restoreUserPermissions(chatId, userId);
		
		// 5. 发送验证成功和群规消息
		const member = { id: userId, first_name: userName };
		const result = await executeWelcomeActions({
			type: 'verification_success_welcome',
			member,  // 🔧 修复：使用正确的 member 对象结构
			chatId,
			env,
			ctx
		});
		
		Logger.info('✅ 验证成功处理完成:', { userId, chatId, userName });
		
		return {
			success: true,
			action: 'verification_completed',
			executionTime: Date.now() - startTime,
			...result
		};
	} catch (error) {
		Logger.error('❌ 处理验证成功失败:', error);
		return {
			success: false,
			error: error.message,
			executionTime: Date.now() - startTime
		};
	}
}

// 简化的验证失败处理
export async function handleVerificationFailure(userId, chatId, userName, env, ctx = null) {
    Logger.info('❌ 处理验证失败:', { userId, chatId, userName });

    try {
        const verificationManager = new VerificationManager(env);

        // 🔧 修复：删除群内验证消息
        // 由于KV存储限制，我们无法直接查找用户会话，但可以通过其他方式清理
        // 这个问题主要出现在直接调用 handleVerificationFailure 的场景
        // 在正常的超时流程中，消息已经被正确删除了
        Logger.debug('⚠️ 注意：直接调用验证失败处理，可能存在未删除的验证消息');

        // 记录失败次数
        await createOrUpdateVerificationRecord(env, userId, userName, chatId, false);

        // 检查是否超过限制
        if (await userExceedsFailureLimit(env, userId)) {
            Logger.info('🚫 失败次数超限，封禁用户:', { userId });
            const banResult = await verificationManager.banUser(chatId, userId);
            if (!banResult.success) {
                Logger.error('❌ 封禁用户失败，可能是权限问题:', {
                    userId,
                    chatId,
                    error: banResult.error
                });
            }
        } else {
            Logger.info('👋 踢出用户:', { userId });
            const kickResult = await verificationManager.kickUser(chatId, userId, ctx);
            if (!kickResult.success) {
                Logger.error('❌ 踢出用户失败，可能是权限问题:', {
                    userId,
                    chatId,
                    error: kickResult.error
                });
            }
        }

        return { success: true };
    } catch (error) {
        Logger.error('❌ 处理验证失败异常:', error);
        return { success: false, error: error.message };
    }
}

// #endregion 🚪 成员变动主处理器

// #region 🔄 队列处理器

/**
 * 处理验证清理队列任务
 * @param {Object} message 队列消息
 * @param {Object} env 环境变量
 * @returns {Promise<void>}
 */
export async function handleVerificationCleanupQueue(message, env, ctx = null) {
    const { userId, chatId, sessionId, action } = message;
    
    Logger.info('🧹 处理清理队列任务:', { userId, chatId, sessionId, action });
    
    try {
        const sessionManager = new SessionManager(env);
        const session = await sessionManager.getSession(sessionId);

        if (!session) {
            Logger.debug('⏭️ 会话不存在，跳过清理:', { sessionId });
            return;
        }

        switch (action) {
            case 'group_timeout':
                // 群组28秒超时 - 检查是否已点击按钮
                if (!session.skipGroupTimeout) {
                    Logger.info('⏰ 群组验证超时，用户未点击按钮:', { userId, chatId });

                    // 删除群组中的验证消息
                    if (session.verificationMessageId) {
                        try {
                            await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
                                chat_id: chatId,
                                message_id: session.verificationMessageId
                            });
                            Logger.debug('🗑️ 验证消息已删除:', { messageId: session.verificationMessageId });
                        } catch (error) {
                            Logger.debug('⚠️ 删除验证消息失败（可能已不存在）:', error.message);
                        }
                    }

                    // 发送超时提示消息给用户
                    try {
                        await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                            chat_id: userId,
                            text: '⏰ 验证超时，您未能在规定时间内点击验证按钮'
                        });
                        Logger.debug('📤 已发送群组超时提示消息给用户:', { userId });
                    } catch (error) {
                        Logger.debug('⚠️ 发送群组超时提示消息失败:', error.message);
                    }

                    await handleVerificationFailure(userId, chatId, session.userName || '用户', env, ctx);

                    // 群组超时失败，删除会话
                    await sessionManager.deleteSession(sessionId);
                } else {
                    Logger.debug('✅ 用户已点击验证按钮，跳过群组超时:', { userId });
                    // 用户已点击按钮，不删除会话，让数学题超时继续处理
                }
                break;
                
            case 'math_timeout':
                // 数学题28秒超时 - 检查是否已完成答题
                if (!session.skipMathTimeout) {
                    Logger.info('⏰ 数学题验证超时，用户未完成答题:', { userId, chatId });

                    // 删除私聊中的验证消息
                    if (session.privateMessageId) {
                        try {
                            await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
                                chat_id: userId,
                                message_id: session.privateMessageId
                            });
                            Logger.debug('🗑️ 数学题超时，私聊验证消息已删除:', { messageId: session.privateMessageId });
                        } catch (error) {
                            Logger.debug('⚠️ 删除私聊验证消息失败（可能已不存在）:', error.message);
                        }
                    }

                    // 发送超时提示消息给用户
                    try {
                        await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                            chat_id: userId,
                            text: '⏰ 验证超时，您未能在规定时间内完成验证'
                        });
                        Logger.debug('📤 已发送超时提示消息给用户:', { userId });
                    } catch (error) {
                        Logger.debug('⚠️ 发送超时提示消息失败:', error.message);
                    }

                    await handleVerificationFailure(userId, chatId, session.userName || '用户', env, ctx);

                    // 数学题超时失败，删除会话
                    await sessionManager.deleteSession(sessionId);
                } else {
                    Logger.debug('✅ 用户已完成答题，跳过数学题超时:', { userId });
                    // 用户已完成答题，会话应该已经在答题处理时被删除了，这里不需要再删除
                }
                break;
                
            default:
                Logger.warn('❓ 未知的清理动作:', { action });
        }
        
    } catch (error) {
        Logger.error('❌ 清理队列任务失败:', error);
    }
}

// #endregion 🔄 队列处理器 