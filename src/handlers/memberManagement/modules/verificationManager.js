// #region 📦 依赖导入
import Logger from '../../../utils/logger.js';
import { sendTelegramRequest } from '../../../utils/telegramApi.js';
import { createOrUpdateVerificationRecord, isUserGloballyVerified } from '../../../utils/userVerificationUtils.js';
// #endregion 📦 依赖导入

// #region 🛡️ 验证管理器类
/**
 * 验证管理器 - 负责处理 D1 数据库相关的验证操作
 * 封装现有的验证工具函数，提供更清晰的接口
 */
export class VerificationManager {
    constructor(env) {
        this.env = env;
        this.botToken = env.TELEGRAM_BOT_TOKEN;
    }

    // 检查用户是否已全局验证
    async isUserGloballyVerified(userId) {
        return await isUserGloballyVerified(this.env, userId);
    }

    // 标记用户为已验证
    async markUserAsVerified(userId, userName = '用户', chatId = null) {
        return await createOrUpdateVerificationRecord(this.env, userId, userName, chatId, true);
    }

    // 限制用户权限
    async restrictUserPermissions(chatId, userId) {
        try {
            await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.botToken}/restrictChatMember`, {
                chat_id: chatId,
                user_id: userId,
                permissions: {
                    can_send_messages: false,
                    can_send_media_messages: false,
                    can_send_polls: false,
                    can_send_other_messages: false,
                    can_add_web_page_previews: false,
                    can_change_info: false,
                    can_invite_users: false,
                    can_pin_messages: false
                }
            });
            Logger.debug('🔒 用户权限已限制:', { chatId, userId });
        } catch (error) {
            Logger.error('❌ 限制用户权限失败:', error);
        }
    }

    // 恢复用户权限
    async restoreUserPermissions(chatId, userId) {
        try {
            await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.botToken}/restrictChatMember`, {
                chat_id: chatId,
                user_id: userId,
                permissions: {
                    can_send_messages: true,
                    can_send_media_messages: true,
                    can_send_polls: true,
                    can_send_other_messages: true,
                    can_add_web_page_previews: true,
                    can_change_info: false,
                    can_invite_users: false,
                    can_pin_messages: false
                }
            });
            Logger.debug('🔓 用户权限已恢复:', { chatId, userId });
        } catch (error) {
            Logger.error('❌ 恢复用户权限失败:', error);
        }
    }

    // 踢出用户
    async kickUser(chatId, userId, ctx = null) {
        try {
            Logger.debug('🚀 开始踢出用户:', { chatId, userId });

            // 1. 先封禁用户
            const banResult = await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.botToken}/banChatMember`, {
                chat_id: chatId,
                user_id: userId
            });

            if (!banResult.ok) {
                Logger.error('❌ 封禁用户失败:', {
                    chatId,
                    userId,
                    error: banResult.description,
                    errorCode: banResult.error_code
                });
                return { success: false, error: banResult.description };
            }

            Logger.debug('✅ 用户封禁成功，准备延迟解封以允许重新加入');

            // 2. 延迟解封逻辑 - 等待1秒后解封，避免时机问题
            const unbanOperation = async () => {
                // 在Worker环境中使用简单延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const unbanResult = await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.botToken}/unbanChatMember`, {
                    chat_id: chatId,
                    user_id: userId
                });

                if (!unbanResult.ok) {
                    Logger.warn('⚠️ 延迟解封用户失败（用户已被踢出但无法重新加入）:', {
                        chatId,
                        userId,
                        error: unbanResult.description
                    });
                } else {
                    Logger.debug('✅ 延迟解封成功，用户现在可以重新加入群组');
                }
            };

            // 如果有 ctx，使用 waitUntil 确保后台任务完成
            if (ctx && ctx.waitUntil) {
                ctx.waitUntil(unbanOperation());
                Logger.debug('🕒 已安排延迟解封任务（通过 ctx.waitUntil）');
            } else {
                // 没有 ctx 的情况下，直接执行后台任务
                unbanOperation().catch(error => {
                    Logger.warn('⚠️ 后台延迟解封失败:', { chatId, userId, error: error.message });
                });
                Logger.debug('🕒 已启动后台延迟解封任务');
            }

            Logger.info('👢 用户已被踢出（解封任务已安排）:', { chatId, userId });
            return { success: true };

        } catch (error) {
            Logger.error('❌ 踢出用户异常:', { chatId, userId, error: error.message });
            return { success: false, error: error.message };
        }
    }

    // 封禁用户
    async banUser(chatId, userId) {
        try {
            Logger.debug('🚀 开始封禁用户:', { chatId, userId });

            const banResult = await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.botToken}/banChatMember`, {
                chat_id: chatId,
                user_id: userId
            });

            if (!banResult.ok) {
                Logger.error('❌ 封禁用户失败:', {
                    chatId,
                    userId,
                    error: banResult.description,
                    errorCode: banResult.error_code
                });
                return { success: false, error: banResult.description };
            }

            Logger.info('🚫 用户已被封禁:', { chatId, userId });
            return { success: true };

        } catch (error) {
            Logger.error('❌ 封禁用户异常:', { chatId, userId, error: error.message });
            return { success: false, error: error.message };
        }
    }

    // 获取群组信息
    async getChatInfo(chatId) {
        try {
            const response = await sendTelegramRequest(this.env, `https://api.telegram.org/bot${this.botToken}/getChat`, {
                chat_id: chatId
            });
            return response.ok ? response.result : null;
        } catch (error) {
            Logger.error('❌ 获取群组信息失败:', error);
            return null;
        }
    }

    // 生成验证码
    generateVerificationCode() {
        return Math.random().toString(36).substring(2, 8);
    }
}
// #endregion 🛡️ 验证管理器类 