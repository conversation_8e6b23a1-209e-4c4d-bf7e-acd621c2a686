// #region 📦 依赖导入
import Logger from '../../../utils/logger.js';

// #endregion 📦 依赖导入

// #region 🗃️ KV 键名常量
const KV_KEYS = {
	// 验证会话: verification:session:${userId}:${chatId}
	VERIFICATION_SESSION: (userId, chatId) => `verification:session:${userId}:${chatId}`,
	
	// 处理状态缓存: verification:processed:${userId}:${chatId}
	PROCESSED_STATUS: (userId, chatId) => `verification:processed:${userId}:${chatId}`,
	
	// 数学题答案: verification:math:${sessionId}
	MATH_ANSWER: (sessionId) => `verification:math:${sessionId}`
};
// #endregion 🗃️ KV 键名常量

// #region 🔧 会话管理器类
/**
 * 基于 KV 的验证会话管理器
 * 负责管理用户验证过程中的临时会话数据
 */
export class SessionManager {
    constructor(env) {
        this.kv = env.VERIFICATION_KV;
    }

    // 生成简单的sessionId
    generateSessionId() {
        return 'verify_' + Math.random().toString(36).substring(2, 10);
    }

    // 创建验证会话 - 超级简单
    async createSession(userID, chatID, mathAnswer, verificationMessageId = null, expiresInSeconds = 120, mathQuestion = null, userName = null) {
        const sessionId = this.generateSessionId();

        const sessionData = {
            userID,
            chatID,
            userName,                   // 用户名（用于超时处理）
            skipGroupTimeout: false,    // 群内按钮倒计时是否要被略过
            skipMathTimeout: false,     // 算术题倒计时是否要被略过
            mathAnswer,                 // 算术题答案 (A/B/C/D)
            mathQuestion,               // 完整的数学题对象
            verificationMessageId,      // 群内验证消息ID
            createdAt: Date.now()
        };

        await this.kv.put(sessionId, JSON.stringify(sessionData), {
            expirationTtl: expiresInSeconds
        });

        Logger.info('✅ 创建验证会话:', { sessionId, userID, chatID, mathAnswer, verificationMessageId });
        return sessionId;
    }

    // 获取会话 - 直接查询
    async getSession(sessionId) {
        try {
            const sessionJson = await this.kv.get(sessionId);
            if (!sessionJson) {
                return null;
            }
            return JSON.parse(sessionJson);
        } catch (error) {
            Logger.error('❌ 获取会话失败:', error);
            return null;
        }
    }

    // 标记群内按钮已点击 - 直接更新
    async markGroupButtonClicked(sessionId) {
        const session = await this.getSession(sessionId);
        if (!session) return false;

        session.skipGroupTimeout = true;
        await this.kv.put(sessionId, JSON.stringify(session), { expirationTtl: 60 });
        
        Logger.info('✅ 标记群内按钮已点击:', { sessionId, userID: session.userID });
        return true;
    }

    // 标记算术题已完成 - 直接更新
    async markMathCompleted(sessionId) {
        const session = await this.getSession(sessionId);
        if (!session) return false;

        session.skipMathTimeout = true;
        await this.kv.put(sessionId, JSON.stringify(session), { expirationTtl: 60 });
        
        Logger.info('✅ 标记算术题已完成:', { sessionId, userID: session.userID });
        return true;
    }

    // 检查答案 - 超级简单
    checkMathAnswer(session, userAnswer) {
        return session.mathAnswer === userAnswer;
    }

    // 删除会话
    async deleteSession(sessionId) {
        await this.kv.delete(sessionId);
        Logger.debug('🗑️ 删除验证会话:', { sessionId });
    }

    // 生成数学题 - 简化版
    generateMathQuestion() {
        const num1 = Math.floor(Math.random() * 20) + 1;
        const num2 = Math.floor(Math.random() * 20) + 1;
        const correctAnswer = num1 + num2;
        
        // 生成4个选项，正确答案随机位置
        const correctIndex = Math.floor(Math.random() * 4);
        const options = [];
        
        for (let i = 0; i < 4; i++) {
            if (i === correctIndex) {
                options.push(correctAnswer);
            } else {
                let wrongAnswer;
                do {
                    wrongAnswer = correctAnswer + Math.floor(Math.random() * 10) - 5;
                } while (wrongAnswer === correctAnswer || wrongAnswer < 1 || options.includes(wrongAnswer));
                options.push(wrongAnswer);
            }
        }
        
        return {
            num1,
            num2,
            operator: '+',
            question: `${num1} + ${num2} = ?`,
            options,
            correctIndex,
            correctAnswer: String.fromCharCode(65 + correctIndex) // A, B, C, D
        };
    }
}
// #endregion 🔧 会话管理器类 