// #region 📦 依赖导入
import Logger from '../../../utils/logger.js';
import { sendTelegramRequest, sendTelegramPhotoMultipart } from '../../../utils/telegramApi.js';
import { SessionManager } from '../modules/sessionManager.js';
import { VerificationManager } from '../modules/verificationManager.js';
import { executeWelcomeActions } from './welcomeActions.js';
import { handleVerificationFailure } from '../index.js';
import { generateMathVerificationImage } from '../../../utils/userVerificationUtils.js';
import { scheduleVerificationTimeout, cancelVerificationTimeout, markSkipTimeout } from '../utils/verificationTimer.js';
// #endregion 📦 依赖导入

// #region 🎯 验证动作执行器

/**
 * 执行验证相关动作
 * @param {Object} actionData 动作数据
 * @returns {Promise<Object>} 执行结果
 */
export async function executeVerificationActions({ type, member, chatId, env, ctx, callbackQuery, commandPayload, message, verificationCode }) {
    Logger.info('🎯 执行验证动作:', { type, userId: member?.id || callbackQuery?.from?.id || message?.from?.id });

    switch (type) {
        case 'start_verification':
            return await startVerificationFlow(member, chatId, env, ctx);
        case 'handle_verification_command':
            return await handleVerificationStartCommand(commandPayload, env);
        case 'handle_verification_start':
            return await handleVerificationStartFromMessage(message, verificationCode, env);
        case 'handle_math_answer':
            return await handleMathAnswerCallback(callbackQuery, env, ctx);
        default:
            throw new Error(`未知的验证动作类型: ${type}`);
    }
}

// #endregion 🎯 验证动作执行器

// #region 🚀 验证流程启动

/**
 * 启动验证流程
 * @param {Object} member 成员对象
 * @param {string} chatId 群组ID
 * @param {Object} env 环境变量
 * @param {Object} ctx 执行上下文
 * @returns {Promise<Object>} 执行结果
 */
async function startVerificationFlow(member, chatId, env, ctx) {
    const userId = member.id;
    const userName = member.first_name || '新成员';
    
    Logger.info('🚀 开始验证流程:', { userId, userName, chatId });

    try {
        const sessionManager = new SessionManager(env);
        const verificationManager = new VerificationManager(env);

        // 1. 生成数学题和答案
        const mathQuestion = sessionManager.generateMathQuestion();

        // 2. 先创建会话（存储完整的数学题对象和用户名）
        const sessionId = await sessionManager.createSession(
            userId,
            chatId,
            mathQuestion.correctAnswer,  // 存储答案字母A/B/C/D
            null, // verificationMessageId
            120,  // expiresInSeconds
            mathQuestion, // 存储完整的数学题对象
            userName // 存储用户名
        );

        // 3. 限制用户权限
        await verificationManager.restrictUserPermissions(chatId, userId);

        // 4. 生成验证码（用于私聊链接）
        const verificationCode = verificationManager.generateVerificationCode();

        // 5. 发送群内验证消息
        const chatInfo = await verificationManager.getChatInfo(chatId);
        const chatTitle = chatInfo?.title || '群组';
        
        const keyboard = [[{
            text: '🔐 点击验证',
            url: `https://t.me/${env.TELEGRAM_BOT_USERNAME}?start=verify_${verificationCode}_${sessionId}`
        }]];

        const verificationMessage = `🛡️ [${userName}](tg://user?id=${userId}) 需要完成真人验证\n⏰ 请在28秒内点击按钮开始验证，否则将被移出群组`;
        
        const messageResponse = await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
            chat_id: chatId,
            text: verificationMessage,
            reply_markup: { inline_keyboard: keyboard },
            parse_mode: 'Markdown'
        });

        // 保存验证消息ID到会话中
        if (messageResponse.ok && messageResponse.result?.message_id) {
            const session = await sessionManager.getSession(sessionId);
            if (session) {
                session.verificationMessageId = messageResponse.result.message_id;
                await sessionManager.kv.put(sessionId, JSON.stringify(session), { expirationTtl: 120 });
                Logger.debug('💾 验证消息ID已保存到会话:', { sessionId, messageId: messageResponse.result.message_id });
            }
        }

        // 6. 设置倒计时清理任务（群组超时）
        if (env.VERIFICATION_CLEANUP_QUEUE) {
            // 优先使用队列（付费计划）
            await env.VERIFICATION_CLEANUP_QUEUE.send({
                userId, chatId, sessionId, action: 'group_timeout'
            }, { delaySeconds: 28 });
        } else {
            // 使用 Durable Object 定时器（免费方案）
            await scheduleVerificationTimeout(env, sessionId, userId, chatId, 'group_timeout', 28);
        }

        return { 
            success: true, 
            verificationCode,
            messageId: messageResponse.result?.message_id 
        };

    } catch (error) {
        Logger.error('❌ 启动验证流程失败:', error);
        throw error;
    }
}

// #endregion 🚀 验证流程启动

// #region 🔗 验证开始处理

/**
 * 从消息中处理验证开始命令 (/start verify_xxx)
 * @param {Object} message 消息对象
 * @param {string} verificationCode 预解析的验证码
 * @param {Object} env 环境变量
 * @returns {Promise<Object>} 执行结果
 */
async function handleVerificationStartFromMessage(message, verificationCode, env) {
    const userId = message.from.id;
    
    Logger.info('🔗 从消息处理验证开始:', { userId, verificationCode: verificationCode.substring(0, 10) + '...' });

    try {
        // 解析验证码格式: verify_${verificationCode}_verify_${randomString}
        // 需要找到最后一个 "verify_" 来分割 verificationCode 和 sessionId
        const lastVerifyIndex = verificationCode.lastIndexOf('_verify_');
        if (lastVerifyIndex === -1) {
            return {
                success: false,
                response: {
                    method: 'sendMessage',
                    chat_id: message.chat.id,
                    text: '❌ 验证链接格式无效'
                }
            };
        }

        // 分离出真正的验证码和sessionId
        const actualVerificationCode = verificationCode.substring(0, lastVerifyIndex);
        const sessionId = verificationCode.substring(lastVerifyIndex + 1); // +1 跳过下划线

        Logger.debug('🔍 解析验证参数:', { actualVerificationCode, sessionId });

        // 调用原有的处理逻辑
        const result = await handleVerificationStartCommand({
            verificationCode: actualVerificationCode,
            sessionId,
            userId
        }, env);

        // 将结果转换为消息响应格式
        if (result.success) {
            return {
                success: true,
                response: {
                    method: 'sendMessage',
                    chat_id: message.chat.id,
                    text: result.message || '✅ 验证开始，请查看验证题'
                }
            };
        } else {
            return {
                success: false,
                response: {
                    method: 'sendMessage',
                    chat_id: message.chat.id,
                    text: result.message || '❌ 验证失败'
                }
            };
        }

    } catch (error) {
        Logger.error('❌ 处理验证命令失败:', error);
        return {
            success: false,
            response: {
                method: 'sendMessage',
                chat_id: message.chat.id,
                text: '❌ 处理验证请求时发生错误，请稍后重试'
            }
        };
    }
}

/**
 * 处理验证开始命令 (/start verify_xxx)
 * @param {Object} actionData 动作数据
 * @returns {Promise<Object>} 执行结果
 */
async function handleVerificationStartCommand({ verificationCode, sessionId, userId }, env) {
    Logger.info('🔗 处理验证开始命令:', { verificationCode: verificationCode.substring(0, 4) + '...', sessionId, userId });

    try {
        const sessionManager = new SessionManager(env);
        
        // 直接用sessionId获取会话
        const session = await sessionManager.getSession(sessionId);
        if (!session) {
            return {
                success: false,
                message: '❌ 验证链接无效或已过期\n请重新获取验证链接'
            };
        }

        // 检查用户ID是否匹配
        if (session.userID !== userId) {
            return {
                success: false,
                message: '❌ 验证链接与当前用户不匹配'
            };
        }

        // 标记群内按钮已点击（停止第一个倒计时）
        await sessionManager.markGroupButtonClicked(sessionId);

        // 🔧 主动取消群组超时DO定时器
        if (!env.VERIFICATION_CLEANUP_QUEUE) {
            // 只有使用DO定时器时才需要取消（队列模式不需要）
            await cancelVerificationTimeout(env, sessionId, 'group_timeout');
            Logger.debug('🔔 已主动取消群组超时定时器:', { sessionId });
        }

        // 删除群组中的验证消息
        if (session.verificationMessageId) {
            try {
                await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
                    chat_id: session.chatID,
                    message_id: session.verificationMessageId
                });
                Logger.debug('🗑️ 用户点击验证后，群组验证消息已删除:', { messageId: session.verificationMessageId });
            } catch (error) {
                Logger.debug('⚠️ 删除群组验证消息失败（可能已不存在）:', error.message);
            }
        }

        // 开始数学验证
        return await startMathVerification(session, sessionId, sessionManager, env);

    } catch (error) {
        Logger.error('❌ 处理验证命令失败:', error);
        return {
            success: false,
            message: '❌ 处理验证请求时发生错误，请稍后重试'
        };
    }
}

// #endregion 🔗 验证开始处理

// #region 🧮 数学题验证

/**
 * 开始数学题验证
 * @param {Object} session 会话对象
 * @param {string} sessionId 会话ID
 * @param {Object} sessionManager 会话管理器
 * @param {Object} env 环境变量
 * @returns {Promise<Object>} 执行结果
 */
async function startMathVerification(session, sessionId, sessionManager, env) {
    try {
        const verificationManager = new VerificationManager(env);
        const chatInfo = await verificationManager.getChatInfo(session.chatID);
        const chatTitle = chatInfo?.title || '群组';

        // 使用会话中存储的数学题（避免重新生成导致答案不匹配）
        const mathQuestion = session.mathQuestion;
        if (!mathQuestion) {
            throw new Error('会话中没有找到数学题数据');
        }
        const imageBuffer = await generateMathVerificationImage(mathQuestion);

        // 生成按钮 - 简单2x2布局，只显示字母选项
        const buttons = mathQuestion.options.map((_, index) => ({
            text: String.fromCharCode(65 + index), // 只显示 A、B、C、D
            callback_data: `math_answer:${sessionId}:${String.fromCharCode(65 + index)}`
        }));

        const keyboard = [
            [buttons[0], buttons[1]],
            [buttons[2], buttons[3]]
        ];

        const caption = `🛡️ 您正在通过 ${chatTitle} 的真人验证\n\n⏰ 请在28秒内选择正确答案：`;

        // 发送验证图片
        const response = await sendTelegramPhotoMultipart(
            env,
            session.userID,
            imageBuffer,
            caption,
            { inline_keyboard: keyboard }
        );

        if (!response.ok) {
            throw new Error('发送验证图片失败');
        }

        // 保存私聊验证消息ID到会话中
        if (response.result?.message_id) {
            const currentSession = await sessionManager.getSession(sessionId);
            if (currentSession) {
                currentSession.privateMessageId = response.result.message_id;
                await sessionManager.kv.put(sessionId, JSON.stringify(currentSession), { expirationTtl: 120 });
                Logger.debug('💾 私聊验证消息ID已保存到会话:', { sessionId, messageId: response.result.message_id });
            }
        }

        // 设置数学题超时清理任务
        if (env.VERIFICATION_CLEANUP_QUEUE) {
            // 优先使用队列（付费计划）
            await env.VERIFICATION_CLEANUP_QUEUE.send({
                userId: session.userID,
                chatId: session.chatID,
                sessionId: sessionId, // 使用传入的 sessionId 参数
                action: 'math_timeout'
            }, { delaySeconds: 28 });
        } else {
            // 使用 Durable Object 定时器（免费方案）
            await scheduleVerificationTimeout(env, sessionId, session.userID, session.chatID, 'math_timeout', 28);
        }

        return {
            success: true
        };

    } catch (error) {
        Logger.error('❌ 数学验证失败:', error);
        return {
            success: false,
            message: '❌ 发送验证题时发生错误，请稍后重试'
        };
    }
}

// #endregion 🧮 数学题验证

// #region 🔧 辅助函数

/**
 * 安全地回应回调查询（不阻塞主流程）
 * @param {Object} env 环境变量
 * @param {string} callbackQueryId 回调查询ID
 * @param {string} text 提示文本
 * @param {boolean} showAlert 是否显示警告
 */
async function safeAnswerCallbackQuery(env, callbackQueryId, text, showAlert = false) {
    try {
        await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
            callback_query_id: callbackQueryId,
            text: text,
            show_alert: showAlert
        });
    } catch (error) {
        Logger.debug('⚠️ 回调查询响应失败（不影响主流程）:', error.message);
    }
}

// #endregion 🔧 辅助函数

// #region 📊 数学题答案处理

/**
 * 处理数学题答案回调
 * @param {Object} actionData 动作数据
 * @returns {Promise<Object>} 执行结果
 */
async function handleMathAnswerCallback(callbackQuery, env, ctx) {
    const { data, from: user, message } = callbackQuery;
    const [, sessionId, userAnswer] = data.split(':'); // math_answer:sessionId:A

    Logger.info('🧮 处理数学答案:', { userId: user.id, sessionId, userAnswer });

    try {
        const sessionManager = new SessionManager(env);
        const session = await sessionManager.getSession(sessionId);

        if (!session) {
            await safeAnswerCallbackQuery(env, callbackQuery.id, '❌ 验证已过期');
            return { success: false, message: '验证会话已过期' };
        }

        if (session.userID !== user.id) {
            await safeAnswerCallbackQuery(env, callbackQuery.id, '❌ 用户不匹配');
            return { success: false, message: '用户验证不匹配' };
        }

        // 标记数学题已完成（停止第二个倒计时）
        await sessionManager.markMathCompleted(sessionId);

        // 检查答案 - 超级简单
        const isCorrect = sessionManager.checkMathAnswer(session, userAnswer);
        
        try {
            await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
                chat_id: user.id,
                message_id: message.message_id
            });
        } catch (e) {
            Logger.debug('⚠️ 删除验证消息失败:', e.message);
        }

        if (isCorrect) {
            // 验证成功
            await safeAnswerCallbackQuery(env, callbackQuery.id, '✅ 验证成功！');

            // 🔧 主动取消DO定时器，避免验证成功后被误踢
            if (!env.VERIFICATION_CLEANUP_QUEUE) {
                // 只有使用DO定时器时才需要取消（队列模式不需要）
                await cancelVerificationTimeout(env, sessionId, 'math_timeout');
                Logger.debug('🔔 已主动取消数学题超时定时器:', { sessionId });
            }

            // 删除会话（必须在取消定时器之后）
            await sessionManager.deleteSession(sessionId);

            const verificationManager = new VerificationManager(env);
            await verificationManager.markUserAsVerified(session.userID, session.userName || user.first_name || '用户', session.chatID);
            await verificationManager.restoreUserPermissions(session.chatID, session.userID);

            // 构造详细的成功消息
            const mathQuestion = session.mathQuestion;
            const correctAnswer = mathQuestion.options[mathQuestion.correctIndex];

            // 构造群组跳转链接
            const chatId = session.chatID;
            const groupLink = chatId.startsWith('-100')
                ? `https://t.me/c/${chatId.slice(4)}/9999999/` // 超级群组链接
                : `https://t.me/c/${chatId.slice(1)}/9999999/`; // 普通群组链接

            const successMessage = [
                '人！感谢你参与验证！',
                `🧮 ${mathQuestion.question} = ${correctAnswer}`,
                '🎉 欢迎加入群组！',
                `[👉点此跳转回到群组里👈](${groupLink})`
            ].join('\n');

            // 发送成功消息和欢迎（使用GIF动画）
            await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAnimation`, {
                chat_id: user.id,
                animation: 'CgACAgQAAxkBAAIHl2iJc2rjqTZfZosG8xwiQWsyQcz6AALwCAACaiRNULbZX5DMnFwzNgQ',
                caption: successMessage,
                parse_mode: 'Markdown'
            });
            
            const member = { id: session.userID, first_name: user.first_name || '用户' };
            await executeWelcomeActions({
                type: 'verification_success_welcome',
                member,
                chatId: session.chatID,
                env,
                ctx
            });

            return { success: true, result: 'verified' };
        } else {
            // 验证失败
            await safeAnswerCallbackQuery(env, callbackQuery.id, '❌ 答案错误');

            // 构造详细的失败消息
            const mathQuestion = session.mathQuestion;
            const correctAnswer = mathQuestion.options[mathQuestion.correctIndex];
            const failureMessage = [
                '❌ 答案错误',
                `🧮 ${mathQuestion.question} = ${correctAnswer}`,
                '请重新尝试验证流程'
            ].join('\n');

            await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                chat_id: user.id,
                text: failureMessage
            });

            await handleVerificationFailure(session.userID, session.chatID, user.first_name || '用户', env, ctx);
            return { success: true, result: 'failed' };
        }

    } catch (error) {
        Logger.error('❌ 处理数学答案失败:', error);
        await safeAnswerCallbackQuery(env, callbackQuery.id, '❌ 处理失败');
        return { success: false, error: error.message };
    }
}

// #endregion 📊 数学题答案处理