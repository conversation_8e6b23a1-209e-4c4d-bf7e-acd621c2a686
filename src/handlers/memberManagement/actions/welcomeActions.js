// #region 📦 依赖导入
import Logger from '../../../utils/logger.js';
import { sendRulesToNewMember } from '../../../utils/groupRulesUtils.js';
import { scheduleMessageDelete } from '../../../utils/messageScheduler.js';
// #endregion 📦 依赖导入

// #region 🎉 欢迎动作执行器

// 简化的欢迎动作分发器
export async function executeWelcomeActions({ type, member, chatId, env, ctx }) {
    Logger.info('🎉 执行欢迎动作:', { type, userId: member.id });

    switch (type) {
        case 'verified_user_welcome':
        case 'verification_success_welcome':
            // 统一处理欢迎消息和群规
            return await sendWelcomeAndRules(member, chatId, env, ctx);
        default:
            throw new Error(`未知的欢迎动作类型: ${type}`);
    }
}

// 发送欢迎消息和群规 - 超级简化
async function sendWelcomeAndRules(member, chatId, env, ctx) {
    const userId = member.id;
    const userName = member.first_name || '新成员';

    try {
        // 直接调用现有的群规工具函数
        const result = await sendRulesToNewMember(env, chatId, userId, userName);

        // 如果发送成功且有消息ID，设置30秒自动删除
        if (result.success && result.messageId) {
            await scheduleMessageDelete(
                env, 
                chatId, 
                result.messageId, 
                30, 
                `欢迎消息自动删除 - 用户${userName}`
            );
        }

        Logger.info('✅ 欢迎消息和群规发送完成:', { userId, chatId, result });

        return {
            success: true,
            sentWelcome: true,
            sentRules: result.success,
            messageId: result.messageId,
            autoDeleteScheduled: !!(result.success && result.messageId && ctx && ctx.waitUntil)
        };

    } catch (error) {
        Logger.error('❌ 发送欢迎消息失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// #endregion 🎉 欢迎动作执行器

// #region 📊 消息统计

/**
 * 获取欢迎消息统计信息
 * @param {string} chatId 群组ID
 * @param {Object} env 环境变量
 * @returns {Promise<Object>} 统计信息
 */
export async function getWelcomeStats(chatId, env) {
	try {
		// 简化版统计 - 直接使用现有的群规工具函数
		const { getGroupRules } = await import('../../../utils/groupRulesUtils.js');
		const rulesConfig = await getGroupRules(env, chatId);
		
		const hasRules = rulesConfig && rulesConfig.rule_urls && rulesConfig.rule_urls.length > 0;
		const accessibleRules = hasRules ? rulesConfig.rule_urls.filter(rule => rule.canAccess !== false) : [];
		
		return {
			hasGroupRules: hasRules,
			totalRules: hasRules ? rulesConfig.rule_urls.length : 0,
			accessibleRules: accessibleRules.length,
			welcomeMessagesSent: 0, // 这里可以从日志或数据库中获取统计
			rulesMessagesSent: 0    // 这里可以从日志或数据库中获取统计
		};
	} catch (error) {
		Logger.error('❌ 获取欢迎消息统计信息失败:', error);
		return {
			hasGroupRules: false,
			totalRules: 0,
			accessibleRules: 0,
			welcomeMessagesSent: 0,
			rulesMessagesSent: 0
		};
	}
}

// #endregion 📊 消息统计

// #region 🔧 工具方法

/**
 * 验证消息文本长度
 * @param {string} text 消息文本
 * @param {number} maxLength 最大长度
 * @returns {boolean} 是否在长度限制内
 */
export function validateMessageLength(text, maxLength = 4096) {
	return text.length <= maxLength;
}

/**
 * 截断过长的消息文本
 * @param {string} text 消息文本
 * @param {number} maxLength 最大长度
 * @returns {string} 截断后的文本
 */
export function truncateMessage(text, maxLength = 4096) {
	if (text.length <= maxLength) {
		return text;
	}
	
	const truncated = text.substring(0, maxLength - 10);
	return truncated + '...(截断)';
}

/**
 * 构建用户提及链接
 * @param {number} userId 用户ID
 * @param {string} userName 用户名
 * @returns {string} 格式化的用户链接
 */
export function buildUserMention(userId, userName) {
	return `[${userName}](tg://user?id=${userId})`;
}

// #endregion 🔧 工具方法 