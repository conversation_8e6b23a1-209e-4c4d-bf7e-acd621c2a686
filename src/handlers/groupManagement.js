import { sendTelegramRequest } from '../utils/telegramApi.js';
import Logger from '../utils/logger.js';

/**
 * 取消置顶Telegram官方消息
 * @param {Object} env 环境变量
 * @param {String} chatId 聊天ID
 * @param {Number} messageId 消息ID
 */
export async function unpinOfficialMessage(env, chatId, messageId) {
	try {
		// 等待1秒
		await new Promise((resolve) => setTimeout(resolve, 1000));

		// 取消置顶消息
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/unpinChatMessage`, {
			chat_id: chatId,
			message_id: messageId,
		});

		Logger.debug(`已取消置顶Telegram官方消息 ${messageId} 在群组 ${chatId}`);
	} catch (error) {
		Logger.error(`取消置顶消息时出错:`, error);
	}
}

/**
 * 删除从广告频道转发的消息
 * @param {Object} env 环境变量
 * @param {String} chatId 聊天ID
 * @param {Number} messageId 消息ID
 */
export async function deleteForwardedAdMessage(env, chatId, messageId) {
	try {
		// 等待1秒
		await new Promise((resolve) => setTimeout(resolve, 1000));

		// 删除消息
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
			chat_id: chatId,
			message_id: messageId,
		});

		Logger.info(`已删除从广告频道转发的消息 ${messageId} 在群组 ${chatId}`);
	} catch (error) {
		Logger.error(`删除转发广告消息时出错:`, error);
	}
} 