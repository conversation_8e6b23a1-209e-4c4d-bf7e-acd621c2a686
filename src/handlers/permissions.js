// #region 📦 依赖导入
import Logger from '../utils/logger.js';
import { BOT_ADMINS } from '../config/constants.js';
import { sendTelegramRequest } from '../utils/telegramApi.js';
// #endregion 📦 依赖导入

// #region 🗄️ 权限缓存
// 简单的内存缓存，避免重复API调用
const permissionCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

function getCacheKey(chatId, userId) {
  return `${chatId}:${userId}`;
}

function getCachedPermission(chatId, userId) {
  const key = getCacheKey(chatId, userId);
  const cached = permissionCache.get(key);
  
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    Logger.debug('使用缓存的权限信息', { chatId, userId, isAdmin: cached.isAdmin });
    return cached.isAdmin;
  }
  
  return null;
}

function setCachedPermission(chatId, userId, isAdmin) {
  const key = getCacheKey(chatId, userId);
  permissionCache.set(key, {
    isAdmin,
    timestamp: Date.now()
  });
  
  // 清理过期缓存
  if (permissionCache.size > 1000) { // 防止内存泄漏
    const now = Date.now();
    for (const [k, v] of permissionCache.entries()) {
      if (now - v.timestamp > CACHE_TTL) {
        permissionCache.delete(k);
      }
    }
  }
}
// #endregion 🗄️ 权限缓存

// #region 🔍 信息提取函数
/**
 * 从上下文中提取消息和用户信息
 * @param {Object} ctx 上下文对象
 * @returns {Object} 包含 message 和 user 的对象
 */
function extractMessageInfo(ctx) {
  const message = ctx.message;
  const user = message?.from;
  
  return { message, user };
}

/**
 * 验证必要参数
 * @param {Object} message 消息对象
 * @param {Object} user 用户对象
 * @returns {boolean} 参数是否有效
 */
function validateParams(message, user) {
  if (!message || !user) {
    Logger.debug('消息或用户信息缺失', { hasMessage: !!message, hasUser: !!user });
    return false;
  }
  return true;
}
// #endregion 🔍 信息提取函数

// #region 🏷️ 基础权限检查

/**
 * 检查是否为匿名管理员
 * @param {Object} ctx Telegram 上下文对象
 * @returns {boolean} 是否为匿名管理员
 */
export function isAnonymous(ctx) {
  const { message } = extractMessageInfo(ctx);
  
  if (!message) {
    return false;
  }
  
  // 检查是否是匿名管理员发送的消息
  // 匿名管理员的消息通常有以下特征：
  // 1. from.username === 'GroupAnonymousBot' 
  // 2. sender_chat 存在且等于当前聊天
  // 3. 有 author_signature 字段（管理员自定义头衔）
  const isGroupAnonymousBot = message.from?.username === 'GroupAnonymousBot';
  const hasSenderChat = !!message.sender_chat;
  const hasAuthorSignature = !!message.author_signature;
  
  const isAnonymousAdmin = isGroupAnonymousBot && (hasSenderChat || hasAuthorSignature);
  
  if (isAnonymousAdmin) {
    Logger.debug('检测到匿名管理员', {
      isGroupAnonymousBot,
      hasSenderChat,
      hasAuthorSignature,
      senderChatId: message.sender_chat?.id,
      authorSignature: message.author_signature
    });
  }
  
  return isAnonymousAdmin;
}

/**
 * 检查是否为Bot管理员
 * @param {Object} ctx Telegram 上下文对象
 * @returns {boolean} 是否为Bot管理员
 */
export function isBotAdmin(ctx) {
  const { user } = extractMessageInfo(ctx);
  
  if (!user) {
    return false;
  }
  
  const isAdmin = BOT_ADMINS.includes(user.id);
  
  if (isAdmin) {
    Logger.debug('检测到Bot管理员', { userId: user.id, username: user.username });
  }
  
  return isAdmin;
}

/**
 * 从消息原始信息检查是否可能是管理员
 * @param {Object} message 消息对象
 * @returns {boolean} 是否可能是管理员
 */
function checkAdminFromMessageInfo(message) {
  // 检查是否有管理员相关的标识
  const indicators = {
    hasAuthorSignature: !!message.author_signature, // 频道管理员或有自定义头衔的群组管理员
    hasSenderChat: !!message.sender_chat, // 代表聊天发送的消息（通常是管理员）
    isGroupAnonymousBot: message.from?.username === 'GroupAnonymousBot', // 匿名管理员
    hasSpecialPermissions: false // 可以扩展其他特殊权限标识
  };
  
  // 如果有任何管理员标识，认为可能是管理员
  const possibleAdmin = Object.values(indicators).some(Boolean);
  
  if (possibleAdmin) {
    Logger.debug('从消息信息检测到可能的管理员标识', indicators);
  }
  
  return possibleAdmin;
}
// #endregion 🏷️ 基础权限检查

// #region 🔐 群组管理员检查

/**
 * 检查用户是否为群组管理员（轻量级方法，减少API调用）
 * @param {Object} ctx Telegram 上下文对象
 * @returns {boolean} 是否为群组管理员
 */
export async function isGroupAdmin(ctx) {
  // #region 📝 信息提取
  const { message, user } = extractMessageInfo(ctx);
  
  if (!validateParams(message, user)) {
    return false;
  }
  
  // 检查是否在群组中（群组ID通常是负数）
  const chatId = message.chat.id;
  if (chatId > 0) {
    // 这是私聊，不是群组，直接返回false
    Logger.debug('私聊中不检查群组管理员状态', { chatId, userId: user.id });
    return false;
  }
  
  // 匿名管理员默认有管理权限
  if (isAnonymous(ctx)) {
    Logger.debug('检测到匿名管理员，授予管理权限');
    return true;
  }
  // #endregion 📝 信息提取
  
  // #region 🗄️ 缓存检查
  const cachedResult = getCachedPermission(chatId, user.id);
  if (cachedResult !== null) {
    return cachedResult;
  }
  // #endregion 🗄️ 缓存检查
  
  // #region 📊 消息信息分析
  // 先从消息原始信息判断
  const possibleAdminFromMessage = checkAdminFromMessageInfo(message);
  if (possibleAdminFromMessage) {
    Logger.debug('从消息信息推断为管理员，跳过API检查', {
      chatId,
      userId: user.id,
      authorSignature: message.author_signature,
      senderChat: message.sender_chat?.id
    });
    
    // 缓存结果并返回
    setCachedPermission(chatId, user.id, true);
    return true;
  }
  // #endregion 📊 消息信息分析
  
  // #region 🚫 API检查降级策略
  // 如果消息信息无法确定，且没有env环境（开发模式），直接返回false
  if (!ctx.env || !ctx.env.TELEGRAM_BOT_TOKEN) {
    Logger.warn('无法进行API权限检查：缺少环境配置', {
      chatId,
      userId: user.id,
      hasEnv: !!ctx.env,
      hasToken: !!(ctx.env?.TELEGRAM_BOT_TOKEN)
    });
    
    // 缓存结果避免重复警告
    setCachedPermission(chatId, user.id, false);
    return false;
  }
  
  // 最后才进行API调用检查
  try {
    Logger.debug('进行API权限检查（最后手段）', { chatId, userId: user.id });
    
    const member = await sendTelegramRequest(
      ctx.env,
      `https://api.telegram.org/bot${ctx.env.TELEGRAM_BOT_TOKEN}/getChatMember`,
      {
        chat_id: chatId,
        user_id: user.id
      }
    );
    
    if (member.ok) {
      const isAdmin = ['creator', 'administrator'].includes(member.result.status);
      Logger.debug('API权限检查完成', {
        chatId,
        userId: user.id,
        status: member.result.status,
        isAdmin
      });
      
      // 缓存结果
      setCachedPermission(chatId, user.id, isAdmin);
      return isAdmin;
    } else {
      Logger.warn('API权限检查失败', {
        chatId,
        userId: user.id,
        error: member.description
      });
      
      // 缓存失败结果，避免重复调用
      setCachedPermission(chatId, user.id, false);
      return false;
    }
  } catch (error) {
    Logger.warn('API权限检查异常', {
      chatId,
      userId: user.id,
      error: error.message
    });
    
    // 缓存失败结果
    setCachedPermission(chatId, user.id, false);
    return false;
  }
  // #endregion 🚫 API检查降级策略
}
// #endregion 🔐 群组管理员检查

// #region 🎯 综合权限检查

/**
 * 检查用户权限（综合检查）
 * @param {Object} ctx Telegram 上下文对象
 * @returns {Object} 权限检查结果
 */
export async function checkPermission(ctx) {
  const { message, user } = extractMessageInfo(ctx);
  
  // 基础验证
  if (!validateParams(message, user)) {
    return {
      hasPermission: false,
      reason: '缺少必要的消息或用户信息',
      details: {
        isBotAdmin: false,
        isGroupAdmin: false,
        isAnonymous: false,
        error: '无效的上下文信息'
      }
    };
  }
  
  // 详细权限检查
  const isBotAdminResult = isBotAdmin(ctx);
  const isAnonymousResult = isAnonymous(ctx);
  const isGroupAdminResult = await isGroupAdmin(ctx);
  
  const hasPermission = isBotAdminResult || isAnonymousResult || isGroupAdminResult;
  
  // 确定主要权限来源
  let reason = '权限不足';
  if (hasPermission) {
    if (isBotAdminResult) {
      reason = 'Bot管理员权限';
    } else if (isAnonymousResult) {
      reason = '匿名管理员权限';
    } else if (isGroupAdminResult) {
      reason = '群组管理员权限';
    }
  }
  
  Logger.debug('权限检查完成', {
    userId: user.id,
    chatId: message.chat.id,
    hasPermission,
    reason,
    isBotAdmin: isBotAdminResult,
    isGroupAdmin: isGroupAdminResult,
    isAnonymous: isAnonymousResult
  });
  
  return {
    hasPermission,
    reason,
    details: {
      isBotAdmin: isBotAdminResult,
      isGroupAdmin: isGroupAdminResult,
      isAnonymous: isAnonymousResult,
      error: null
    }
  };
}
// #endregion 🎯 综合权限检查 