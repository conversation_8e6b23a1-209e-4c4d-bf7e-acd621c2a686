/**
 * 回调查询处理模块
 * 处理Telegram Bot的callback_query事件，主要包括投稿相关的回调操作
 */

import { sendTelegramRequest } from '../utils/telegramApi.js';
import { DefaultGroupsBind } from '../config/constants.js';
import { processGradeText, getGradeConfig, removeGradePrefix } from '../utils/gradeUtils.js';
import Logger from '../utils/logger.js';
// 导入媒体组处理工具
import { 
	getMediaGroupMessages, 
	getMediaGroupForwardedRecords,
	batchDeleteMessages, 
	batchEditMediaMessages, 
	sendMediaGroupToChannel,
	editSingleMediaMessage,
	createCaptionWithSource
} from '../utils/mediaGroupUtils.js';
import { isBilifeedbotContent, simplifyBilifeedbotContent } from '../utils/stringUtils.js';
// 导入命令回调处理器
import { handleCommandCallbacks } from './commandCallbacks.js';

/**
 * 处理回调查询（按钮点击）
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleCallbackQuery(callbackQuery, env, ctx) {
	try {
		const startTime = Date.now();
		const { id, data, message } = callbackQuery;
		Logger.debug('开始处理回调查询:', data);

			// 首先检查是否是命令相关的回调
	const isCommandCallback = await handleCommandCallbacks(callbackQuery, env, ctx);
	if (isCommandCallback) {
		return new Response('OK', { status: 200 });
	}

	// 检查是否是举报处理回调
	if (data && data.startsWith('report_handle:')) {
		const { handleReportCallback } = await import('./command/report.js');
		const isReportCallback = await handleReportCallback(callbackQuery, env);
		if (isReportCallback) {
			return new Response('OK', { status: 200 });
		}
	}

	// 检查是否是数学题答案回调
	if (data && data.startsWith('math_answer:')) {
		const { executeVerificationActions } = await import('./memberManagement/actions/verificationActions.js');
		const result = await executeVerificationActions({
			type: 'handle_math_answer',
			callbackQuery,
			env,
			ctx
		});
		if (result.handled) {
			return new Response('OK', { status: 200 });
		}
	}

		// 使用 waitUntil 异步处理耗时操作
		if (data && data.startsWith('sc:')) {
			ctx.waitUntil(handleSubmitToChannel(callbackQuery, env));
			return new Response('OK', { status: 200 });
		} else if (data && data.startsWith('co:')) {
			ctx.waitUntil(handleContributeOption(callbackQuery, env));
			return new Response('OK', { status: 200 });
		} else if (data === 'cc') {
			ctx.waitUntil(handleContributeCancel(callbackQuery, env));
			return new Response('OK', { status: 200 });
		}

		Logger.debug(`回调查询处理完成，总耗时: ${Date.now() - startTime}ms`);
		return new Response('OK', { status: 200 });
	} catch (error) {
		Logger.debug('处理回调查询时出错:', error);

		// 发送错误消息
		try {
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: callbackQuery.id,
				text: '处理请求时出错，请稍后再试',
				show_alert: false,
			});
		} catch (e) {
			Logger.error('回复回调查询时出错:', e);
		}

		// 即使处理回调查询失败也要返回200，避免Telegram重试
		return new Response('OK - Callback error handled', { status: 200 });
	}
}

/**
 * 处理投稿到频道的操作
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
async function handleSubmitToChannel(callbackQuery, env) {
	const { id, data, message } = callbackQuery;
	Logger.debug('异步处理投稿到频道...');

	const parts = data.split(':');
	if (parts.length !== 4) {
		Logger.warn('CBData格式错误:', data);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '数据格式错误',
			show_alert: false,
		});
		return;
	}

	const simplifiedGroupId = parts[1]; // 精简后的群组ID (不含-100前缀)
	const originalMessageId = parts[2]; // 原始消息ID
	const forwardedMessageId = parts[3]; // 转发到投稿群的消息ID

	// 还原完整的群组ID
	const sourceGroupId = '-100' + simplifiedGroupId;
	
	// 从群组配置中获取对应的频道ID
	const groupConfig = DefaultGroupsBind.find((group) => group.id === sourceGroupId);
	if (!groupConfig) {
		Logger.warn('未找到群组配置:', sourceGroupId);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '未找到群组配置',
			show_alert: false,
		});
		return;
	}

	const targetChannelId = groupConfig.defaultChannel;
	const groupName = groupConfig.name;

	// 首先获取要投稿的目标消息（forwardedMessageId对应的消息）
	let targetMessage = null;
	try {
		// 尝试通过回复关系找到目标消息
		if (message.reply_to_message) {
			targetMessage = message.reply_to_message;
			Logger.debug('找到回复的目标消息:', targetMessage.message_id);
		} else {
			// 如果没有回复关系，说明可能是内联按钮
			Logger.warn('按钮消息没有reply_to_message，使用按钮消息作为目标');
			targetMessage = message;
		}
	} catch (error) {
		Logger.error('获取目标消息时出错:', error);
		targetMessage = message; // 降级使用按钮消息
	}

	// 处理投稿
	let result = null;
	let responseText = '';

	try {
		// 检查是否是媒体组的一部分
		if (targetMessage.media_group_id) {
			Logger.debug('处理媒体组投稿，media_group_id:', targetMessage.media_group_id);
			
			// 获取媒体组记录
			let mediaGroupRecords = await getMediaGroupForwardedRecords(env, targetMessage.media_group_id);
			if (mediaGroupRecords.length === 0) {
				mediaGroupRecords = await getMediaGroupForwardedRecords(env, targetMessage.media_group_id);
			}
			Logger.debug('找到媒体组投稿记录数量:', mediaGroupRecords.length);
			
			if (mediaGroupRecords.length > 0) {
				// 检查目标消息是否有NSFW分级或遮罩
				const hasNSFWGrade = targetMessage.caption && (
					targetMessage.caption.includes('🔞NSFW⚠️') || 
					targetMessage.caption.includes('🔞NSFW⚠') ||
					targetMessage.caption.includes('三重警告')
				);
				const shouldApplySpoiler = targetMessage.has_media_spoiler || hasNSFWGrade;
				
				Logger.debug('媒体组NSFW检查:', { hasNSFWGrade, shouldApplySpoiler });
				
				// 构建媒体组消息，使用原始消息数据但应用当前目标消息的分级信息
				const mediaMessages = mediaGroupRecords.map(record => {
					try {
						const originalMessage = typeof record.raw_json === 'string' 
							? JSON.parse(record.raw_json) 
							: record.raw_json;
						
						// 如果这是当前目标消息，使用当前的分级处理后的内容
						if (record.sub_message_id === targetMessage.message_id) {
							Logger.debug('使用当前分级处理后的目标消息内容');
							return {
								...originalMessage,
								caption: targetMessage.caption,
								caption_entities: targetMessage.caption_entities,
								has_media_spoiler: targetMessage.has_media_spoiler
							};
						}
						
						// 其他消息：如果目标消息是NSFW分级，也给它们加上遮罩
						const modifiedMessage = { ...originalMessage };
						if (shouldApplySpoiler) {
							modifiedMessage.has_media_spoiler = true;
							Logger.debug('给媒体组其他消息添加遮罩:', record.sub_message_id);
						}
						
						return modifiedMessage;
					} catch (e) {
						Logger.error('解析媒体组消息JSON失败:', e);
						return null;
					}
				}).filter(msg => msg !== null);
				
				Logger.debug('构建的媒体消息数量:', mediaMessages.length);
				
				if (mediaMessages.length > 0) {
					result = await sendMediaGroupToChannel(env, targetChannelId, mediaMessages);
				} else {
					Logger.warn('无法构建媒体组消息，降级为单条消息处理');
					result = await sendMediaGroupToChannel(env, targetChannelId, [targetMessage]);
				}
			} else {
				Logger.warn('未找到媒体组投稿记录，降级为单条消息处理');
				result = await sendMediaGroupToChannel(env, targetChannelId, [targetMessage]);
			}
		} else {
			// 单个媒体消息：直接使用当前消息内容（已经包含分级处理）
			result = await sendMediaGroupToChannel(env, targetChannelId, [targetMessage]);
		}

		// 处理投稿结果
		if (result && result.ok) {
			responseText = `✅ 已成功投稿到${groupName}频道`;
			Logger.success(`投稿成功`);

			// 给原始消息添加反应表情（使用CBData中的originalMessageId）
			try {
				Logger.debug(`添加反应表情到原始消息`);
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/setMessageReaction`, {
					chat_id: sourceGroupId,
					message_id: parseInt(originalMessageId),
					reaction: [{ type: 'emoji', emoji: '🎉' }],
					is_big: false,
				});
			} catch (error) {
				Logger.log(`添加反应表情到原始消息失败:`, error);
			}
		} else {
			responseText = `❌ 投稿失败: ${result ? result.description : '未知错误'}`;
			Logger.error(`投稿失败:`, result);
		}

		// 更新回调查询响应
		Logger.debug(`发送最终回调查询响应`);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: responseText,
			show_alert: false,
		});

		// 发送最终回调查询响应之后如果投稿成功则删除回调查询相关的消息和数据库记录
		if (result && result.ok) {
			// 检查消息是否超过48小时
			const { checkSubmissionMessageExpired } = await import('../utils/messageTimeChecker.js');
			const isExpired = await checkSubmissionMessageExpired(env, message);
			
			if (isExpired) {
				Logger.info('投稿成功但消息超过48小时，跳过删除操作');
				// 投稿成功但消息过期，不进行删除操作
				return;
			}
			
			// 使用统一的删除函数，删除消息和数据库记录
			try {
				const { deleteSubmissionData } = await import('../utils/submissionDeletion.js');
				await deleteSubmissionData(env, parseInt(originalMessageId), sourceGroupId, {
					buttonMessage: message
				});
			} catch (error) {
				Logger.error('删除投稿成功后的数据失败:', error);
				// 降级：仅删除消息
				await deleteCallbackMessages(env, message);
			}
		}
	} catch (error) {
		Logger.log(`处理投稿过程中出错:`, error);

		// 尝试发送错误消息
		try {
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: `❌ 处理投稿时出错: ${error.message}`,
				show_alert: false,
			});
		} catch (e) {
			Logger.log(`发送错误消息失败:`, e);
		}
	}
}

/**
 * 处理内容分级选项
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
async function handleContributeOption(callbackQuery, env) {
	const { id, data, message } = callbackQuery;
	Logger.debug(`异步处理内容分级选项...`);

	const parts = data.split(':');
	if (parts.length !== 3) {
		Logger.warn('CBData格式错误:', data);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '数据格式错误',
			show_alert: false,
		});
		return;
	}

	const option = parts[1]; // 选项类型（分级或removeCaption）
	const targetMessageId = parseInt(parts[2]); // 要修改的消息ID

	// 获取当前操作的群组和话题信息
	const currentChatId = message.chat.id.toString();
	const currentTopicId = message.message_thread_id;

	Logger.debug('处理投稿群选项操作:', { option, targetMessageId, currentChatId, currentTopicId });

	try {
		// 获取目标消息（按钮消息应该回复到目标媒体消息）
		let targetMessage = null;
		if (message.reply_to_message) {
			targetMessage = message.reply_to_message;
			Logger.debug('找到要编辑的目标消息:', targetMessage.message_id);
		} else {
			Logger.error('按钮消息没有回复到目标消息');
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: '无法找到目标消息',
				show_alert: false,
			});
			return;
		}

		// 处理不同类型的选项
		let newCaption = '';
		let newCaptionEntities = [];
		let needsSpoiler = false;

		if (option === 'removeCaption') {
			// 处理去除说明选项
			Logger.debug('处理去除说明选项');
			
			// 获取当前按钮状态，检查是否已经选中去除说明
			const currentState = getCurrentContributeButtonState(message.reply_markup);
			const isCurrentlySelected = currentState.removeCaption;
			
			// 切换去除说明状态
			const newRemoveCaptionState = !isCurrentlySelected;
			Logger.debug('去除说明状态切换:', { from: isCurrentlySelected, to: newRemoveCaptionState });
			
			// 从数据库获取原始消息数据
			try {
				const stmt = env.DB.prepare(`
					SELECT raw_json FROM tg_log_forwarded_submissions 
					WHERE sub_message_id = ? 
					LIMIT 1
				`).bind(targetMessage.message_id);
				
				const result = await stmt.first();
				if (result && result.raw_json) {
					const originalMessage = typeof result.raw_json === 'string' 
						? JSON.parse(result.raw_json) 
						: result.raw_json;
					
					Logger.debug('找到原始消息数据，应用去除说明处理');
					
					// 根据新状态决定是否保留原始说明
					let originalText = newRemoveCaptionState ? '' : (originalMessage.caption || '');
					let originalEntities = newRemoveCaptionState ? [] : (originalMessage.caption_entities || []);
					
					// 检测是否是bilifeedbot转换的视频并精简
					if (!newRemoveCaptionState && originalText && isBilifeedbotContent(originalText)) {
						Logger.debug('在handleContributeOption中检测到bilifeedbot转换视频，精简说明文字');
						const simplified = simplifyBilifeedbotContent(originalText, originalEntities);
						originalText = simplified.caption;
						originalEntities = simplified.entities;
					}
					
					// 应用当前选中的分级（如果有）
					const currentGrade = currentState.gradeOption || 'normal';
					const gradeConfig = getGradeConfig(currentGrade);
					needsSpoiler = gradeConfig.needsSpoiler;
					
					// 使用createCaptionWithSource重新生成说明文字
					const captionData = createCaptionWithSource(originalMessage, originalText);
					
					// 如果有分级且不是normal，应用分级前缀
					if (currentGrade && currentGrade !== 'normal') {
						const gradeResult = processGradeText({
							originalText: captionData.caption,
							originalEntities: captionData.entities,
							grade: currentGrade,
							viaText: '',
							viaEntities: []
						});
						newCaption = gradeResult.text;
						newCaptionEntities = gradeResult.entities;
						needsSpoiler = gradeResult.needsSpoiler;
					} else {
						newCaption = captionData.caption;
						newCaptionEntities = captionData.entities;
					}
					
					Logger.debug('应用去除说明后的说明文字:', newCaption);
				} else {
					Logger.warn('未找到原始消息数据，无法处理去除说明');
					await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
						callback_query_id: id,
						text: '无法获取原始消息数据',
						show_alert: false,
					});
					return;
				}
			} catch (error) {
				Logger.error('获取原始消息数据失败:', error);
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
					callback_query_id: id,
					text: '处理失败',
					show_alert: false,
				});
				return;
			}
		} else if (option === 'clear') {
			// 清空说明：从数据库获取原始消息数据，重新构建via xxx from xxx格式
			try {
				// 查询数据库获取原始消息数据
				const stmt = env.DB.prepare(`
					SELECT raw_json FROM tg_log_forwarded_submissions 
					WHERE sub_message_id = ? 
					LIMIT 1
				`).bind(targetMessage.message_id);
				
				const result = await stmt.first();
				if (result && result.raw_json) {
					const originalMessage = typeof result.raw_json === 'string' 
						? JSON.parse(result.raw_json) 
						: result.raw_json;
					
					Logger.debug('找到原始消息数据，重新构建说明文字');
					
					// 处理原始说明文字，检测是否是bilifeedbot内容
					let originalCaption = originalMessage.caption || '';
					let originalCaptionEntities = originalMessage.caption_entities || [];
					
					// 检测是否是bilifeedbot转换的视频并精简
					if (originalCaption && isBilifeedbotContent(originalCaption)) {
						Logger.debug('在clear选项中检测到bilifeedbot转换视频，精简说明文字');
						const simplified = simplifyBilifeedbotContent(originalCaption, originalCaptionEntities);
						originalCaption = simplified.caption;
						originalCaptionEntities = simplified.entities;
					}
					
					// 使用createCaptionWithSource重新生成原始格式的说明文字
					const captionData = createCaptionWithSource(originalMessage, originalCaption);
					newCaption = captionData.caption;
					newCaptionEntities = captionData.entities;
					
					Logger.debug('重新构建的说明文字:', newCaption);
				} else {
					Logger.warn('未找到原始消息数据，保持当前说明文字');
					newCaption = targetMessage.caption || '';
					newCaptionEntities = targetMessage.caption_entities || [];
				}
			} catch (error) {
				Logger.error('获取原始消息数据失败:', error);
				newCaption = targetMessage.caption || '';
				newCaptionEntities = targetMessage.caption_entities || [];
			}
		} else {
			// 处理分级选项
			const gradeConfig = getGradeConfig(option);
			needsSpoiler = gradeConfig.needsSpoiler;
			
			// 使用通用分级处理函数
			const { cleanText } = removeGradePrefix(targetMessage.caption || '');
			let processedText = cleanText;
			let processedEntities = targetMessage.caption_entities || [];
			
			// 检测是否是bilifeedbot转换的视频并精简
			if (processedText && isBilifeedbotContent(processedText)) {
				Logger.debug('在分级选项处理中检测到bilifeedbot转换视频，精简说明文字');
				const simplified = simplifyBilifeedbotContent(processedText, processedEntities);
				processedText = simplified.caption;
				processedEntities = simplified.entities;
			}
			
			const result = processGradeText({
				originalText: processedText,
				originalEntities: processedEntities,
				grade: option,
				viaText: '', // 投稿群内处理不需要via信息
				viaEntities: []
			});
			
			newCaption = result.text;
			newCaptionEntities = result.entities;
			needsSpoiler = result.needsSpoiler;
		}

		// 更新目标消息
		let editResult = null;

		// 检查是否是媒体组的一部分
		if (targetMessage.media_group_id) {
			Logger.debug('处理媒体组分级，media_group_id:', targetMessage.media_group_id);

			// 获取媒体组中的所有消息 - 优先从转发提交记录获取
			try {
				let mediaGroupMessages = await getMediaGroupForwardedRecords(env, targetMessage.media_group_id);
				if (mediaGroupMessages.length === 0) {
					// 降级：从原始媒体组记录获取
					const rawMessages = await getMediaGroupMessages(env, targetMessage.media_group_id);
					mediaGroupMessages = rawMessages;
				}
				Logger.debug('找到媒体组消息数量:', mediaGroupMessages.length);

				if (mediaGroupMessages.length === 0) {
					Logger.warn('媒体组消息为空，降级为单消息处理');
					editResult = await editSingleMediaMessage(env, currentChatId, targetMessage, newCaption, newCaptionEntities, needsSpoiler);
				} else {
					// 使用批量编辑功能处理整个媒体组
					// 确保获取正确的消息数据格式，并使用转发后的消息ID
					const messageData = mediaGroupMessages.map(record => {
						if (record.raw_json) {
							// 来自数据库记录，需要解析raw_json并替换为转发后的消息ID
							const parsedMessage = typeof record.raw_json === 'string' 
								? JSON.parse(record.raw_json) 
								: record.raw_json;
							// 重要：使用转发后的消息ID，而不是原始消息ID
							parsedMessage.message_id = record.sub_message_id;
							return parsedMessage;
						} else {
							// 直接的消息对象，同样需要确保使用正确的消息ID
							if (record.sub_message_id) {
								record.message_id = record.sub_message_id;
							}
							return record;
						}
					});
					
					const editResults = await batchEditMediaMessages(
						env, 
						currentChatId, 
						messageData, 
						newCaption, 
						newCaptionEntities, 
						needsSpoiler
					);
					
					// 检查编辑结果
					const successCount = editResults.filter(result => result.ok).length;
					Logger.debug(`媒体组编辑完成: ${successCount}/${editResults.length} 成功`);
					
					// 使用第一个结果作为主要结果
					editResult = editResults.length > 0 ? editResults[0] : { ok: false, description: '没有编辑结果' };
				}
			} catch (error) {
				Logger.error('处理媒体组时出错:', error);
				// 降级处理：只编辑目标消息
				editResult = await editSingleMediaMessage(env, currentChatId, targetMessage, newCaption, newCaptionEntities, needsSpoiler);
			}
		} else {
			// 单个媒体消息：正常编辑
			editResult = await editSingleMediaMessage(env, currentChatId, targetMessage, newCaption, newCaptionEntities, needsSpoiler);
		}

		// 不删除操作按钮消息，保留以便继续操作

		// 获取选项显示名称
		let optionText = '';
		if (option === 'removeCaption') {
			optionText = '去除说明';
		} else {
			optionText = getGradeConfig(option).displayName;
		}

		// 回复回调查询
		if (editResult && editResult.ok) {
			// 更新按钮状态：添加/移除✅标记
			try {
				const currentKeyboard = message.reply_markup?.inline_keyboard || [];
				const updatedKeyboard = currentKeyboard.map(row => {
					return row.map(button => {
						// 检查是否是投稿群选项按钮
						if (button.callback_data && button.callback_data.startsWith('co:')) {
							const buttonOption = button.callback_data.split(':')[1];
							
							// 移除已有的✅标记
							let cleanText = button.text.replace('✅', '').trim();
							
							// 特殊处理去除说明按钮：独立切换状态
							if (buttonOption === 'removeCaption') {
								if (option === 'removeCaption') {
									// 如果当前点击的是去除说明按钮，切换其状态
									if (button.text.startsWith('✅')) {
										// 如果已经选中，则取消选中
										cleanText = cleanText;
									} else {
										// 如果未选中，则选中
										cleanText = '✅' + cleanText;
									}
								} else {
									// 如果点击的是其他按钮，保持去除说明按钮的当前状态
									if (button.text.startsWith('✅')) {
										cleanText = '✅' + cleanText;
									}
								}
							} else {
								// 分级按钮逻辑
								if (option !== 'removeCaption') {
									// 只有点击分级按钮时才更新分级按钮状态
									if (buttonOption === option) {
										// 选中当前点击的分级按钮
										cleanText = '✅' + cleanText;
									}
									// 其他分级按钮保持未选中状态
								} else {
									// 如果点击的是去除说明按钮，保持分级按钮的当前状态
									if (button.text.startsWith('✅')) {
										cleanText = '✅' + cleanText;
									}
								}
							}
							
							return {
								...button,
								text: cleanText
							};
						}
						return button;
					});
				});
				
				// 更新按钮显示
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageReplyMarkup`, {
					chat_id: message.chat.id,
					message_id: message.message_id,
					reply_markup: {
						inline_keyboard: updatedKeyboard
					}
				});
				
				Logger.debug('成功更新按钮状态');
			} catch (buttonError) {
				Logger.error('更新按钮状态失败:', buttonError);
				// 不影响主要功能，继续执行
			}
			
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: `✅ 已应用${optionText}`,
				show_alert: false,
			});
		} else {
			Logger.error('编辑目标消息失败:', editResult);
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: `❌ 编辑失败: ${editResult ? editResult.description : '未知错误'}`,
				show_alert: false,
			});
		}
	} catch (error) {
		Logger.error('处理分级操作时出错:', error);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: `❌ 操作失败: ${error.message}`,
			show_alert: false,
		});
	}
}

/**
 * 处理取消操作
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
async function handleContributeCancel(callbackQuery, env) {
	const { id, message } = callbackQuery;

	try {
		// 检查消息是否超过48小时
		const { checkSubmissionMessageExpired } = await import('../utils/messageTimeChecker.js');
		const isExpired = await checkSubmissionMessageExpired(env, message);
		
		if (isExpired) {
			// 消息超过48小时，弹窗提示并直接返回
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: '❌ 消息太旧，无法删除',
				show_alert: false,
			});
			Logger.info('取消操作被阻止：消息超过48小时无法删除');
			return;
		}

		// 消息未超时，正常处理删除
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '操作已取消，正在删除相关消息...',
			show_alert: false,
		});

		// 使用统一的删除函数，删除消息和数据库记录
		try {
			const { deleteSubmissionByButtonMessage } = await import('../utils/submissionDeletion.js');
			const deleteSuccess = await deleteSubmissionByButtonMessage(env, message);
			
			if (!deleteSuccess) {
				Logger.warn('统一删除失败，尝试降级删除');
				// 降级：仅删除消息
				await deleteCallbackMessages(env, message);
			}
		} catch (error) {
			Logger.error('统一删除投稿数据失败:', error);
			// 降级：仅删除消息
			await deleteCallbackMessages(env, message);
		}
	} catch (error) {
		Logger.error('处理取消操作时出错:', error);
		try {
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: '取消操作失败',
				show_alert: false,
			});
		} catch (e) {
			Logger.error('回复取消失败回调时出错:', e);
		}
	}
}

/**
 * 获取投稿群内按钮的当前状态
 * @param {Object} replyMarkup 按钮配置
 * @returns {Object} 当前状态 {gradeOption: string|null, removeCaption: boolean}
 */
function getCurrentContributeButtonState(replyMarkup) {
	if (!replyMarkup?.inline_keyboard) {
		return { gradeOption: null, removeCaption: false };
	}
	
	let gradeOption = null;
	let removeCaption = false;
	
	// 查找带有✅标记的按钮
	for (const row of replyMarkup.inline_keyboard) {
		for (const button of row) {
			if (button.callback_data && button.callback_data.startsWith('co:') && button.text.startsWith('✅')) {
				const option = button.callback_data.split(':')[1];
				if (option === 'removeCaption') {
					removeCaption = true;
				} else {
					gradeOption = option;
				}
			}
		}
	}
	
	return { gradeOption, removeCaption };
}

/**
 * 删除回调查询相关的消息（目标消息和按钮消息）
 * @param {Object} env 环境变量
 * @param {Object} message 按钮消息对象
 * @returns {Promise<boolean>} 删除是否成功
 */
async function deleteCallbackMessages(env, message) {
	try {
		// 收集要删除的消息ID
		const messagesToDelete = [];

		// 添加操作按钮消息
		messagesToDelete.push(message.message_id);

		// 检查被回复的目标消息
		if (message.reply_to_message) {
			const targetMessage = message.reply_to_message;
			
			// 如果是媒体组，需要删除整个媒体组
			if (targetMessage.media_group_id) {
				Logger.debug('处理媒体组删除，media_group_id:', targetMessage.media_group_id);
				
				// 优先从转发提交记录数据库获取媒体组中的所有消息ID
				let mediaGroupRecords = await getMediaGroupForwardedRecords(env, targetMessage.media_group_id);
				if (mediaGroupRecords.length === 0) {
					// 降级：从投稿记录数据库获取
					mediaGroupRecords = await getMediaGroupForwardedRecords(env, targetMessage.media_group_id);
				}
				Logger.debug('找到媒体组记录数量:', mediaGroupRecords.length);
				
				if (mediaGroupRecords.length > 0) {
					// 添加媒体组中所有消息的sub_message_id到删除列表
					for (const record of mediaGroupRecords) {
						if (record.sub_message_id) {
							messagesToDelete.push(record.sub_message_id);
						}
					}
					Logger.debug('媒体组消息ID列表:', messagesToDelete);
				} else {
					// 降级处理：只删除目标消息
					messagesToDelete.push(targetMessage.message_id);
				}
			} else {
				// 单条消息，直接添加
				messagesToDelete.push(targetMessage.message_id);
			}
		}

		// 使用通用批量删除函数
		const deleteResult = await batchDeleteMessages(env, message.chat.id, messagesToDelete);
		Logger.debug('批量删除结果:', deleteResult);
		
		return deleteResult;
	} catch (error) {
		Logger.error('删除回调消息时出错:', error);
		return false;
	}
}

// 重复的函数已移动到 mediaGroupUtils.js 中
