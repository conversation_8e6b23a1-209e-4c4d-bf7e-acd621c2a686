// #region 📦 依赖导入
import Logger from '../utils/logger.js';
import { checkPermission } from './permissions.js';
import { sendTelegramRequest } from '../utils/telegramApi.js';
import { safeString } from '../utils/stringUtils.js';
// #endregion 📦 依赖导入

// #region 🔧 配置常量
// 群组类型配置
const GROUP_CONFIGS = {
  // 新群组：检测侮辱性内容
  newGroups: [-1001360670669, -1001107742293, -1001730968687],
  // 特殊群组：沙雕英雄群
  specialGroup: -1001143091022,
  // 管理群ID
  adminChatId: -1002008890111,
  // MitsukiJoe用户ID
  mitsukiJoeId: 96728357
};

// AI API 配置
const AI_CONFIG = {
  monicaTimeout: 15000,    // Monica API 超时时间（15秒）
  geminiTimeout: 10000,    // Gemini API 超时时间（10秒）
  maxRetries: 1            // 最大重试次数
};
// #endregion 🔧 配置常量

// #region 🤖 AI API 调用函数

/**
 * 调用 Monica API 进行内容检测
 * @param {string} prompt 检测提示词
 * @param {Object} env 环境变量
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<string|null>} AI响应结果或null
 */
async function callMonica(prompt, env, timeout = AI_CONFIG.monicaTimeout) {
  if (!env.MONICA_API_KEY) {
    Logger.warn('Monica API Key 未配置，跳过Monica检测');
    return null;
  }

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    Logger.debug('开始调用 Monica API');
    
    const response = await fetch('https://openapi.monica.im/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${env.MONICA_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{
          role: 'user',
          content: [{
            type: 'text',
            text: prompt
          }]
        }]
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Monica API 响应错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.choices && data.choices[0] && data.choices[0].message) {
      const result = data.choices[0].message.content;
      Logger.success('Monica API 调用成功', { responseLength: result.length });
      return result;
    } else {
      throw new Error('Monica API 响应格式无效');
    }

  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      Logger.warn(`Monica API 调用超时 (${timeout}ms)`);
    } else {
      Logger.error('Monica API 调用失败', error);
    }
    
    return null;
  }
}

/**
 * 调用 Gemini API 进行内容检测
 * @param {string} prompt 检测提示词
 * @param {Object} env 环境变量
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<string|null>} AI响应结果或null
 */
async function callGemini(prompt, env, timeout = AI_CONFIG.geminiTimeout) {
  if (!env.GEMINI_API_KEY) {
    Logger.warn('Gemini API Key 未配置，跳过Gemini检测');
    return null;
  }

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    Logger.debug('开始调用 Gemini API');
    
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          safetySettings: [
            { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
            { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
            { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
            { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' }
          ],
          contents: [{
            role: 'user',
            parts: [{ text: prompt }]
          }]
        }),
        signal: controller.signal
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Gemini API 响应错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const result = data.candidates[0].content.parts[0].text;
      Logger.success('Gemini API 调用成功', { responseLength: result.length });
      return result;
    } else {
      throw new Error('Gemini API 响应格式无效');
    }

  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      Logger.warn(`Gemini API 调用超时 (${timeout}ms)`);
    } else {
      Logger.error('Gemini API 调用失败', error);
    }
    
    return null;
  }
}

/**
 * 智能AI调用：Monica优先，失败后尝试Gemini
 * @param {string} prompt 检测提示词
 * @param {Object} env 环境变量
 * @returns {Promise<string|null>} AI响应结果或null
 */
async function callAIWithFallback(prompt, env) {
  Logger.debug('开始AI检测，Monica优先策略');
  
  // 优先尝试Monica
  let result = await callMonica(prompt, env);
  
  if (result) {
    Logger.debug('Monica API 调用成功，返回结果');
    return result;
  }
  
  // Monica失败，尝试Gemini
  Logger.debug('Monica API 失败，尝试Gemini API');
  result = await callGemini(prompt, env);
  
  if (result) {
    Logger.debug('Gemini API 调用成功，返回结果');
    return result;
  }
  
  Logger.warn('所有AI API都调用失败');
  return null;
}
// #endregion 🤖 AI API 调用函数

// #region 📝 提示词生成

/**
 * 生成AI检测提示词
 * @param {Object} message 消息对象
 * @param {number} chatId 聊天ID
 * @returns {string} 生成的提示词
 */
function generatePrompt(message, chatId) {
  const { from, forward_origin = {}, text, caption } = message;
  const { type, sender_user = {}, chat = {} } = forward_origin;
  
  // 获取文本内容
  const textContent = text || caption || '';
  
  // 判断群组类型
  const isNewGroup = GROUP_CONFIGS.newGroups.includes(chatId);
  
  // 构建转发信息
  const forwardedFromUser = type === 'user' ? ` 转发自用户 ${sender_user.first_name}` : '';
  const forwardedFromChat = type === 'channel' ? ` 转发自聊天 ${chat.title}` : '';
  
  // 构建基础信息
  let prompt = `用户 ${from?.first_name || '未知用户'} `;
  prompt += `${forwardedFromUser || ''}`;
  prompt += `${forwardedFromChat || ''}`;
  prompt += `\n的信息: ${textContent}`;
  
  if (isNewGroup) {
    // 新群组：检测侮辱性内容
    prompt += '\n判断以上的用户和TA发送的内容是否包含针对性的辱骂内容，只要直接关联的相关性，不要"可能"相关的那种';
    prompt += '\n相关度用0~10数字表示,只回答相关性数字!!!只有相关度大于0的情况下再给出理由';
  } else {
    // 原有群组：检测广告内容
    prompt += '\n判断以上的用户和TA发送的内容在聊天群内出现是否属于[';
    prompt += '虚拟币或者假币广告/法轮功/博彩广告/诱导他人看自己头像或者名字加联系方式进行的特殊诈骗/';
    prompt += '全新引流方式【TG频道评论】全行业都可以引/这是我的XXweb3钱包助记词退圈了送给有缘人';
    prompt += ']相关内容';
    prompt += '\n不进行什么"在绝大多数平台"之类的检测，不检测性质上属于别的什么';
    prompt += '\n不管危害性等不相关的事情！';
    prompt += '\n相关度用0~10数字表示,只回答相关性数字!!!只有相关度大于0的情况下再给出理由';
  }
  
  return prompt;
}
// #endregion 📝 提示词生成

// #region 🎬 处理动作函数

/**
 * 转发到管理群进行人工审核
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {string} analysisResult AI分析结果
 * @param {string} contentType 内容类型（广告/侮辱性内容）
 */
async function forwardToAdminGroup(message, env, analysisResult, contentType) {
  try {
    const chatId = message.chat.id;
    const userId = message.from.id;
    const messageId = message.message_id;
    const adminChatId = GROUP_CONFIGS.adminChatId;

    // 转发消息到管理群
    const forwardResult = await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`,
      {
        chat_id: adminChatId,
        from_chat_id: chatId,
        message_id: messageId
      }
    );

    if (!forwardResult.ok) {
      throw new Error('转发消息失败');
    }

    // 生成跳转链接
    const messageLink = `https://t.me/c/${String(chatId).replace('-100', '')}/${messageId}`;

    // 发送带按钮的操作消息
    await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
      {
        chat_id: adminChatId,
        text: `检测到该信息可能是${contentType}，请管理员进行甄别。\n[点击跳转到原信息](${messageLink})\n${analysisResult}`,
        parse_mode: 'Markdown',
        reply_to_message_id: forwardResult.result.message_id,
        reply_markup: {
          inline_keyboard: [[
            {
              text: `😡🔪 ${contentType}！杀`,
              callback_data: `AIantiAD:ban_${chatId}_${userId}_${messageId}`
            },
            {
              text: `🙂☕️ 不是${contentType}`,
              callback_data: `AIantiAD:ignore_${chatId}_${messageId}`
            }
          ]]
        }
      }
    );

    Logger.info('已转发消息到管理群进行人工审核', {
      chatId,
      messageId,
      forwardedMessageId: forwardResult.result.message_id
    });

  } catch (error) {
    Logger.error('转发到管理群失败', error);
  }
}

/**
 * 转发给MitsukiJoe进行审核
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {string} analysisResult AI分析结果
 * @param {string} contentType 内容类型
 */
async function forwardToMitsukiJoe(message, env, analysisResult, contentType) {
  try {
    const chatId = message.chat.id;
    const userId = message.from.id;
    const messageId = message.message_id;
    const mitsukiJoeId = GROUP_CONFIGS.mitsukiJoeId;

    // 转发消息给MitsukiJoe
    const forwardResult = await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`,
      {
        chat_id: mitsukiJoeId,
        from_chat_id: chatId,
        message_id: messageId
      }
    );

    if (!forwardResult.ok) {
      throw new Error('转发消息失败');
    }

    // 生成跳转链接
    const messageLink = `https://t.me/c/${String(chatId).replace('-100', '')}/${messageId}`;

    // 发送带按钮的操作消息给MitsukiJoe
    await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
      {
        chat_id: mitsukiJoeId,
        text: `检测到该信息可能是${contentType}，请进行甄别。\n[点击跳转到原信息](${messageLink})\n${analysisResult}`,
        parse_mode: 'Markdown',
        reply_to_message_id: forwardResult.result.message_id,
        reply_markup: {
          inline_keyboard: [[
            {
              text: `😡🔪 ${contentType}！杀`,
              callback_data: `AIantiAD:ban_${chatId}_${userId}_${messageId}`
            },
            {
              text: `🙂☕️ 不是${contentType}`,
              callback_data: `AIantiAD:ignore_${chatId}_${messageId}`
            }
          ]]
        }
      }
    );

    Logger.info('已转发消息给MitsukiJoe进行审核', {
      chatId,
      messageId,
      forwardedMessageId: forwardResult.result.message_id
    });

  } catch (error) {
    Logger.error('转发给MitsukiJoe失败', error);
  }
}

/**
 * 直接通知MitsukiJoe并处理侮辱性内容
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 * @param {string} analysisResult AI分析结果
 */
async function notifyMitsukiJoeAndBan(message, env, analysisResult) {
  try {
    const chatId = message.chat.id;
    const userId = message.from.id;
    const messageId = message.message_id;
    const mitsukiJoeId = GROUP_CONFIGS.mitsukiJoeId;

    // 生成跳转链接
    const messageLink = `https://t.me/c/${String(chatId).replace('-100', '')}/${messageId}`;

    // 转发消息给MitsukiJoe
    await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`,
      {
        chat_id: mitsukiJoeId,
        from_chat_id: chatId,
        message_id: messageId
      }
    );

    // 发送链接通知
    await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
      {
        chat_id: mitsukiJoeId,
        text: `检测到侮辱性内容，消息链接: ${messageLink}\n${analysisResult}`,
        parse_mode: 'Markdown'
      }
    );

    // 删除消息并封禁用户
    await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
      {
        chat_id: chatId,
        message_id: messageId
      }
    );

    await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/banChatMember`,
      {
        chat_id: chatId,
        user_id: userId
      }
    );

    Logger.warn('检测到严重侮辱性内容，已删除消息并封禁用户', {
      chatId,
      userId,
      messageId
    });

  } catch (error) {
    Logger.error('处理侮辱性内容失败', error);
  }
}
// #endregion 🎬 处理动作函数

// #region 🔍 主检测函数

/**
 * 解析AI响应结果
 * @param {string} aiResponse AI响应文本
 * @returns {Object|null} 解析结果 {level: number, reason: string} 或 null
 */
function parseAIResponse(aiResponse) {
  if (!aiResponse || typeof aiResponse !== 'string') {
    return null;
  }

  // 匹配数字开头的响应格式
  const match = aiResponse.match(/^(\d+)\s*(.*)/s);
  if (match) {
    return {
      level: parseInt(match[1], 10),
      reason: match[2].trim()
    };
  }

  return null;
}

/**
 * AI反广告检测主函数
 * @param {Object} message 消息对象
 * @param {Object} env 环境变量
 */
export async function aiAntiAdCheck(message, env) {
  try {
    Logger.debug('🤖 开始AI反广告检测', {
      chatId: message.chat.id,
      messageId: message.message_id,
      userId: message.from?.id
    });

    // 检查是否有文本内容
    const textContent = message.text || message.caption;
    if (!textContent || !textContent.trim()) {
      Logger.debug('消息无文本内容，跳过AI检测');
      return;
    }

    // 检查权限（管理员跳过检测）
    const permissionResult = await checkPermission({ message, env });
    if (permissionResult.hasPermission) {
      Logger.debug('用户有管理权限，跳过AI检测');
      return;
    }

    const chatId = message.chat.id;
    
    // 检查是否在支持的群组中
    const isNewGroup = GROUP_CONFIGS.newGroups.includes(chatId);
    const isSpecialGroup = chatId === GROUP_CONFIGS.specialGroup;
    const isOldGroup = !isNewGroup && !isSpecialGroup;

    if (!isNewGroup && !isSpecialGroup && !isOldGroup) {
      Logger.debug('群组不在AI检测范围内，跳过检测');
      return;
    }

    // 生成提示词
    const prompt = generatePrompt(message, chatId);
    Logger.debug('生成AI检测提示词', { promptLength: prompt.length });

    // 调用AI API
    const aiResponse = await callAIWithFallback(prompt, env);
    if (!aiResponse) {
      Logger.warn('所有AI API调用失败，跳过处理');
      return;
    }

    // 解析AI响应
    const parsed = parseAIResponse(aiResponse);
    if (!parsed) {
      Logger.warn('AI响应解析失败', { aiResponse: aiResponse.substring(0, 100) });
      return;
    }

    const { level: relevanceLevel, reason } = parsed;
    
    // 构建分析结果文本
    const contentType = isNewGroup ? '侮辱性内容' : '广告';
    const analysisResult = `该信息是${contentType}的可疑度: ${relevanceLevel} by AI\n${reason}`;

    Logger.info('AI检测完成', {
      chatId,
      messageId: message.message_id,
      relevanceLevel,
      contentType,
      reason: reason.substring(0, 50)
    });

    // 根据群组类型和相关度决定处理方式
    if (isNewGroup) {
      // 新群组：处理侮辱性内容
      if (relevanceLevel >= 10) {
        await notifyMitsukiJoeAndBan(message, env, analysisResult);
      }
    } else if (isSpecialGroup) {
      // 特殊群组：沙雕英雄群
      if (relevanceLevel >= 8) {
        await forwardToMitsukiJoe(message, env, analysisResult, contentType);
      }
    } else {
      // 原有群组：处理广告内容
      if (relevanceLevel >= 5) {
        await forwardToAdminGroup(message, env, analysisResult, contentType);
      }
    }

    Logger.debug('🤖 AI反广告检测完成');

  } catch (error) {
    Logger.error('AI反广告检测过程中发生错误', error);
    // 不抛出异常，避免影响主流程
  }
}
// #endregion 🔍 主检测函数

// #region 🎛️ 回调查询处理

/**
 * 处理AI反广告相关的回调查询（按钮点击）
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
export async function handleAIAntiAdCallback(callbackQuery, env) {
  try {
    const { data, from, message } = callbackQuery;
    const adminChatId = GROUP_CONFIGS.adminChatId;

    Logger.debug('处理AI反广告回调查询', { data, operatorId: from.id });

    if (data.startsWith('AIantiAD:ban_')) {
      // 处理封禁操作
      const [, chatId, userId, messageId] = data.split('_');

      // 删除原群中的消息
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
        {
          chat_id: chatId,
          message_id: messageId
        }
      );

      // 封禁用户
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/banChatMember`,
        {
          chat_id: chatId,
          user_id: userId
        }
      );

      // 删除管理群中的转发消息和操作消息
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
        {
          chat_id: message.chat.id,
          message_id: message.message_id
        }
      );

      if (message.reply_to_message) {
        await sendTelegramRequest(
          env,
          `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
          {
            chat_id: adminChatId,
            message_id: message.reply_to_message.message_id
          }
        );
      }

      // 发送操作通知
      const messageLink = `https://t.me/c/${chatId.replace('-100', '')}/${messageId}`;
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
        {
          chat_id: adminChatId,
          text: `[${from.first_name}](tg://user?id=${from.id}) 封禁了 [TA](tg://user?id=${userId}) 的 [一条信息](${messageLink})`,
          parse_mode: 'Markdown'
        }
      );

      // 响应回调查询
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`,
        {
          callback_query_id: callbackQuery.id,
          text: '用户已被封禁，消息已删除。'
        }
      );

      Logger.info('AI反广告：执行封禁操作成功', {
        operatorId: from.id,
        targetUserId: userId,
        chatId
      });

    } else if (data.startsWith('AIantiAD:ignore_')) {
      // 处理忽略操作
      const [, chatId, messageId] = data.split('_');

      // 删除管理群中的转发消息和操作消息
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
        {
          chat_id: message.chat.id,
          message_id: message.message_id
        }
      );

      if (message.reply_to_message) {
        await sendTelegramRequest(
          env,
          `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
          {
            chat_id: adminChatId,
            message_id: message.reply_to_message.message_id
          }
        );
      }

      // 发送操作通知
      const messageLink = `https://t.me/c/${chatId.replace('-100', '')}/${messageId}`;
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
        {
          chat_id: adminChatId,
          text: `[${from.first_name}](tg://user?id=${from.id}) 放行了 [一条信息](${messageLink})`,
          parse_mode: 'Markdown'
        }
      );

      // 响应回调查询
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`,
        {
          callback_query_id: callbackQuery.id,
          text: '操作已取消，无进一步处理。'
        }
      );

      Logger.info('AI反广告：执行忽略操作成功', {
        operatorId: from.id,
        chatId
      });
    }

  } catch (error) {
    Logger.error('处理AI反广告回调查询失败', error);
    
    // 尝试响应回调查询避免用户看到错误
    try {
      await sendTelegramRequest(
        env,
        `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`,
        {
          callback_query_id: callbackQuery.id,
          text: '操作失败，请稍后再试！'
        }
      );
    } catch (responseError) {
      Logger.error('响应回调查询失败', responseError);
    }
  }
}
// #endregion 🎛️ 回调查询处理 