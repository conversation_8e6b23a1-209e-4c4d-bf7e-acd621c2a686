/**
 * 媒体转发处理模块
 * 处理媒体消息的转发，包括单个媒体和媒体组
 */

import { sendTelegramRequest } from '../utils/telegramApi.js';
import { DefaultGroupsBind } from '../config/constants.js';
import Logger from '../utils/logger.js';
// 导入投稿处理模块 (已废弃logSubMessage，使用forwardHandler中的函数)
import { transcodeEmoji } from '../utils/transcodeEmoji.js';
import { TARGET_GROUP_ID, TOPIC_MAPPING, MEDIA_GROUP_DELAY, TELEGRAM_OFFICIAL_ID, BOT_ADMINS } from '../config/constants.js';
// 导入新的转发处理模块
import { 
	hasMedia, 
	hasSubmissionTag, 
	getTargetTopicId, 
	logForwardedSubmission 
} from './forwardHandler.js';
// 导入安全字符串处理工具
import { 
	safeUserDisplayNameWithLimit,
	safeChannelDisplayName, 
	safeCaptionText, 
	safeEntities,
	isBilifeedbotContent,
	simplifyBilifeedbotContent
} from '../utils/stringUtils.js';
// 导入通用分级处理工具
import { processGradeText, getGradeConfig } from '../utils/gradeUtils.js';
// 导入统一按钮工具
import { createSubmitToChannelButtons } from '../utils/buttonUtils.js';



/**
 * 获取用户显示名称 - 使用安全字符串处理
 * @param {Object} user 用户对象
 * @returns {String} 用户显示名称
 */
function getUserDisplayName(user) {
	return safeUserDisplayNameWithLimit(user, 7);
}

/**
 * 获取频道显示名称 - 使用安全字符串处理
 * @param {Object} chat 频道对象
 * @param {String} signature 签名（可选）
 * @returns {String} 频道显示名称
 */
function getChannelDisplayName(chat, signature) {
	return safeChannelDisplayName(chat, signature, 10);
}

/**
 * 创建带有原始消息链接的说明文字，使用via/from格式
 * @param {Object} message 原始消息对象
 * @param {String} originalCaption 原始说明文字
 * @param {String} grade 内容分级
 * @returns {Object} 包含说明文字和实体的对象
 */
function createCaptionWithSource(message, originalCaption = '', grade = 'normal') {
	const chatId = message.chat.id.toString();
	const messageId = message.message_id;
	const user = message.from;
	const userDisplayName = getUserDisplayName(user);

	// 创建原始消息链接
	const sourceUrl = `https://t.me/c/${chatId.replace('-100', '')}/` + messageId;

	// 处理原始说明文字（去除#投稿标签）
	let cleanOriginalCaption = '';
	let cleanOriginalEntities = [];
	
	if (originalCaption && originalCaption.trim() !== '') {
		// 使用安全字符串处理原始caption
		cleanOriginalCaption = safeCaptionText(originalCaption);
		cleanOriginalEntities = message.caption_entities || [];
		
		// 检测是否是bilifeedbot转换的视频
		if (isBilifeedbotContent(cleanOriginalCaption)) {
			Logger.debug('检测到bilifeedbot转换视频，精简说明文字');
			const simplified = simplifyBilifeedbotContent(cleanOriginalCaption, cleanOriginalEntities);
			cleanOriginalCaption = simplified.caption;
			cleanOriginalEntities = simplified.entities;
		}
		
		// 去除#投稿标签
		if (cleanOriginalCaption.includes('#投稿')) {
			cleanOriginalCaption = cleanOriginalCaption.replace('#投稿', '').trim();
		}

		// 如果原始消息有实体，使用安全处理方式
		if (cleanOriginalEntities && cleanOriginalEntities.length > 0 && cleanOriginalCaption) {
			// 由于emoji处理可能改变字符长度，使用安全实体处理
			const processedEntities = safeEntities(
				cleanOriginalEntities, 
				originalCaption, 
				cleanOriginalCaption
			);
			
			// 如果实体处理成功，过滤掉#投稿标签相关的实体
			if (processedEntities.length > 0) {
				cleanOriginalEntities = processedEntities
					.map((entity) => {
						// 跳过可能的#投稿标签实体
						if (originalCaption.indexOf('#投稿') >= 0 &&
							entity.offset <= originalCaption.indexOf('#投稿') &&
							entity.offset + entity.length >= originalCaption.indexOf('#投稿') + 3
						) {
							return null;
						}

						// 调整偏移量（减去#投稿标签的长度）
						return {
							...entity,
							offset: Math.max(0, entity.offset - (originalCaption.indexOf('#投稿') > 0 ? 3 : 0))
						};
					})
					.filter((entity) => entity !== null);
			}
		}
	}

	// 构建via信息
	let viaText = '';
	let viaEntities = [];
	
	// 检查是否是机器人管理员
	const isAdmin = user && BOT_ADMINS.includes(user.id);

	// 如果不是管理员，添加via用户名
	if (!isAdmin && userDisplayName) {
		viaText += `via ${userDisplayName}`;

		// 添加用户链接实体
		viaEntities.push({
			type: 'text_link',
			offset: 4, // "via "的长度
			length: userDisplayName.length,
			url: sourceUrl,
		});
	}

	// 添加from频道（如果有）
	if (message.forward_from_chat) {
		// 检查是否需要跳过某些频道（可以添加黑名单逻辑）
		const channelName = getChannelDisplayName(message.forward_from_chat, message.forward_signature);

		// 创建频道链接 - 优先使用username，没有则使用chat_id
		let channelUrl;
		if (message.forward_from_chat.username) {
			// 使用username形式的链接（公开可访问）
			channelUrl = `https://t.me/${message.forward_from_chat.username}/${message.forward_from_message_id}`;
		} else {
			// 使用chat_id形式的链接（需要成员身份）
			channelUrl = `https://t.me/c/${message.forward_from_chat.id.toString().replace('-100', '')}/${message.forward_from_message_id}`;
		}

		// 对于管理员：如果有via xxx from xxx格式，变成只有from xxx
		// 对于普通用户：正常添加空格
		if (!isAdmin && userDisplayName) {
			viaText += ' ';
		}

		const fromOffset = viaText.length;
		viaText += `from ${channelName}`;

		// 添加频道链接实体
		viaEntities.push({
			type: 'text_link',
			offset: fromOffset + 5, // "from "的长度
			length: channelName.length,
			url: channelUrl,
		});
	}

	// 使用通用分级处理函数
	const result = processGradeText({
		originalText: cleanOriginalCaption,
		originalEntities: cleanOriginalEntities,
		grade: grade,
		viaText: viaText,
		viaEntities: viaEntities,
		separator: '\n' // 使用单换行符分隔
	});

	return {
		caption: result.text,
		entities: result.entities,
	};
}

/**
 * 检查消息是否带有遮罩(spoiler)
 * @param {Object} message Telegram消息对象
 * @returns {Boolean} 是否带有遮罩
 */
function hasSpoiler(message) {
	// 检查照片或视频是否有遮罩标记
	if (message.photo && message.has_media_spoiler) {
		return true;
	}
	if (message.video && message.has_media_spoiler) {
		return true;
	}
	if (message.animation && message.has_media_spoiler) {
		return true;
	}
	return false;
}

/**
 * 获取群组对应的默认频道
 * @param {String} chatId 群组ID
 * @returns {String|null} 默认频道ID或null
 */
function getDefaultChannel(chatId) {
	const groupConfig = DefaultGroupsBind.find((group) => group.id === chatId);
	return groupConfig ? groupConfig.defaultChannel : null;
}

/**
 * 创建投稿按钮（已迁移到 buttonUtils.js）
 * @param {String} chatId 来源群组ID
 * @param {String} originalMessageId 原始消息ID
 * @param {String} forwardedMessageId 转发到投稿群的消息ID
 * @param {String} topicId 话题ID
 * @param {Object} env 环境变量
 * @param {Object} originalMessage 原始消息对象（可选）
 * @returns {Promise<Object|null>} 按钮配置或null
 */
async function createSubmitButtons(chatId, originalMessageId, forwardedMessageId, topicId, env, originalMessage = null) {
	return await createSubmitToChannelButtons(chatId, originalMessageId, forwardedMessageId, topicId, DefaultGroupsBind, env, originalMessage);
}

/**
 * 发送单个媒体消息
 * @param {Object} ctx 上下文对象
 * @param {String} topicId 目标话题ID
 * @param {String} grade 内容分级
 * @returns {Promise<Object|null>} 发送结果或null
 */
async function sendSingleMedia(ctx, topicId, grade = 'normal') {
	const { message, env } = ctx;
	const chatId = message.chat.id.toString();

	try {
		let apiUrl = '';
		let params = {
			chat_id: TARGET_GROUP_ID,
			message_thread_id: topicId,
			// 其他参数将根据媒体类型添加
		};

		// 准备说明文字和实体
		const captionData = createCaptionWithSource(message, message.caption || '', grade);
		params.caption = captionData.caption;
		params.caption_entities = captionData.entities;

		// 检查是否有遮罩，如果有则添加遮罩参数
		if (hasSpoiler(message)) {
			params.has_spoiler = true;
		}

		// 根据媒体类型选择API端点和参数
		if (message.photo) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendPhoto`;
			params.photo = message.photo[message.photo.length - 1].file_id;
		} else if (message.video) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVideo`;
			params.video = message.video.file_id;
		} else if (message.document) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendDocument`;
			params.document = message.document.file_id;
		} else if (message.audio) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAudio`;
			params.audio = message.audio.file_id;
		} else if (message.voice) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVoice`;
			params.voice = message.voice.file_id;
		} else if (message.video_note) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVideoNote`;
			params.video_note = message.video_note.file_id;
			// VideoNote不支持说明文字和按钮
			delete params.caption;
			delete params.caption_entities;
			delete params.reply_markup;
		} else if (message.animation) {
			apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAnimation`;
			params.animation = message.animation.file_id;
		} else {
			Logger.warn('不支持的媒体类型');
			return null;
		}

		// 使用新的 API 工具发送请求
		const result = await sendTelegramRequest(env, apiUrl, params);
		Logger.info('发送单个媒体结果:', result);

		if (!result.ok) {
			Logger.error('发送单个媒体失败:', result.description);
			return null;
		}

		// 记录转发消息到数据库
		try {
			await logForwardedSubmission(env, message, result.result, topicId);
		} catch (error) {
			Logger.error('记录转发消息到数据库失败:', error);
		}

		// 如果发送成功，发送一条带有投稿按钮的消息
		if (result.result && result.result.message_id) {
			const forwardedMessageId = result.result.message_id;
			
			// 创建投稿按钮，传递原始消息对象用于判断是否显示去除说明按钮
			const replyMarkup = await createSubmitButtons(chatId, message.message_id, forwardedMessageId, topicId, env, message);
			if (replyMarkup) {
				const buttonMessage = {
					chat_id: TARGET_GROUP_ID,
					message_thread_id: topicId,
					text: '↑操作上方媒体↑',
					reply_to_message_id: forwardedMessageId, // 回复到媒体消息
					reply_markup: replyMarkup,
				};

				// 发送按钮消息
				const buttonResult = await sendTelegramRequest(
					env,
					`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
					buttonMessage
				);

				if (!buttonResult.ok) {
					Logger.error('发送投稿按钮失败:', buttonResult.description);
				}
			}
		}

		return result;
	} catch (error) {
		Logger.error('发送单个媒体时出错:', error);
		return null;
	}
}

/**
 * 获取媒体组中的所有媒体
 * @param {Object} ctx 上下文对象
 * @param {String} mediaGroupId 媒体组ID
 * @returns {Promise<Array>} 媒体组中的所有消息
 */
async function getMediaGroupMessages(ctx, mediaGroupId) {
	try {
		const { env } = ctx;

		// 从数据库查询媒体组中的所有消息
		const stmt = env.DB.prepare(
			`
      SELECT * FROM tg_log_media_group 
      WHERE media_group_id = ? 
      ORDER BY msg_id ASC
    `
		).bind(mediaGroupId);

		const result = await stmt.all();

		if (!result.results || result.results.length === 0) {
			Logger.warn('未找到媒体组消息');
			return [];
		}

		// 将原始JSON字符串转换为对象
		return result.results
			.map((item) => {
				try {
					const rawMessage = JSON.parse(item.raw_json);
					return rawMessage;
				} catch (e) {
					Logger.error('解析消息JSON失败:', e);
					return null;
				}
			})
			.filter((msg) => msg !== null); // 过滤掉解析失败的消息
	} catch (error) {
		Logger.error('获取媒体组消息时出错:', error);
		return [];
	}
}

/**
 * 判断媒体是否为文件格式
 * @param {Object} message 消息对象
 * @returns {Boolean} 是否为文件格式
 */
function isFileMedia(message) {
	return !!message.document;
}

/**
 * 发送媒体组
 * @param {Object} ctx 上下文对象
 * @param {Array} mediaGroupMessages 媒体组中的所有消息
 * @param {String} topicId 目标话题ID
 * @param {String} grade 内容分级
 * @returns {Promise<Object|null>} 发送结果或null
 */
async function sendMediaGroup(ctx, mediaGroupMessages, topicId, grade = 'normal') {
	const { env } = ctx;
	const originalMessage = ctx.message;
	const chatId = originalMessage.chat.id.toString();

	try {
		// 准备媒体组数据
		const media = [];
		let hasFileMedia = false;
		let firstMediaWithCaption = null;
		let firstMedia = null;
		let lastFileMedia = null;

		// 第一遍扫描，确定媒体类型和找出有说明文字的媒体
		for (const message of mediaGroupMessages) {
			if (message.caption) {
				if (!firstMediaWithCaption) {
					firstMediaWithCaption = message;
				}
			}

			if (isFileMedia(message)) {
				hasFileMedia = true;
				lastFileMedia = message;
			}

			if (!firstMedia) {
				firstMedia = message;
			}
		}

		// 确定应该将源链接添加到哪个媒体
		let targetMessageForSource = firstMediaWithCaption || (hasFileMedia ? lastFileMedia : firstMedia);

		// 第二遍扫描，构建媒体数组
		for (const message of mediaGroupMessages) {
			let mediaItem = {
				type: null,
				media: null,
			};

			// 设置媒体类型和ID
			if (message.photo) {
				mediaItem.type = 'photo';
				mediaItem.media = message.photo[message.photo.length - 1].file_id;
				// 检查是否有遮罩
				if (message.has_media_spoiler) {
					mediaItem.has_spoiler = true;
				}
			} else if (message.video) {
				mediaItem.type = 'video';
				mediaItem.media = message.video.file_id;
				// 检查是否有遮罩
				if (message.has_media_spoiler) {
					mediaItem.has_spoiler = true;
				}
			} else if (message.document) {
				mediaItem.type = 'document';
				mediaItem.media = message.document.file_id;
			} else if (message.audio) {
				mediaItem.type = 'audio';
				mediaItem.media = message.audio.file_id;
			} else if (message.animation) {
				mediaItem.type = 'animation';
				mediaItem.media = message.animation.file_id;
				// 检查是否有遮罩
				if (message.has_media_spoiler) {
					mediaItem.has_spoiler = true;
				}
			} else {
				// 不支持的媒体类型，跳过
				Logger.warn('不支持的媒体类型');
				continue;
			}

			// 如果是目标消息，添加源链接和用户名
			if (message.message_id === targetMessageForSource.message_id) {
				// 创建带有分级和via/from格式的说明文字
				const captionData = createCaptionWithSource(message, message.caption || '', grade);
				mediaItem.caption = captionData.caption;
				mediaItem.caption_entities = captionData.entities;
			} else if (message.caption) {
				// 保留原有说明文字，确保是字符串
				mediaItem.caption = String(message.caption);
				if (message.caption_entities) {
					mediaItem.caption_entities = message.caption_entities;
				}
			}

			media.push(mediaItem);
		}

		// 发送媒体组
		const apiUrl = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMediaGroup`;
		const params = {
			chat_id: TARGET_GROUP_ID,
			message_thread_id: topicId,
			media: media,
		};

		// 记录详细的请求内容，用于调试
		Logger.info('发送媒体组请求参数:', params);

		// 使用新的 API 工具发送请求
		const result = await sendTelegramRequest(env, apiUrl, params);
		Logger.info('发送媒体组结果:', result);

		if (!result.ok) {
			Logger.error('发送媒体组失败:', result.description);
			return null;
		}

		// 记录媒体组中每条消息到数据库
		try {
			if (result.result && result.result.length > 0) {
				for (let i = 0; i < mediaGroupMessages.length && i < result.result.length; i++) {
					const originalMessage = mediaGroupMessages[i];
					const sentMessage = result.result[i];
					await logForwardedSubmission(env, originalMessage, sentMessage, topicId);
				}
			}
		} catch (error) {
			Logger.error('记录媒体组消息到数据库失败:', error);
		}

		// 媒体组发送成功后，发送一条带有投稿按钮的消息
		// 注意：媒体组API不支持直接添加按钮，所以我们需要发送一条单独的消息
		if (result.result && result.result.length > 0) {
			// 获取媒体组中第一条消息的ID
			const firstMediaMessageId = result.result[0].message_id;
			const originalFirstMessageId = mediaGroupMessages[0].message_id;
			
			// 传递第一条原始消息对象用于判断是否显示去除说明按钮
			const replyMarkup = await createSubmitButtons(chatId, originalFirstMessageId, firstMediaMessageId, topicId, env, mediaGroupMessages[0]);
			if (replyMarkup) {
				const buttonMessage = {
					chat_id: TARGET_GROUP_ID,
					message_thread_id: topicId,
					text: '↑操作上方媒体↑',
					reply_to_message_id: firstMediaMessageId, // 回复到媒体组第一条消息
					reply_markup: replyMarkup,
				};

				// 发送按钮消息
				const buttonResult = await sendTelegramRequest(
					env,
					`https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`,
					buttonMessage
				);

				if (!buttonResult.ok) {
					Logger.error('发送投稿按钮失败:', buttonResult.description);
				}
			}
		}

		return result;
	} catch (error) {
		Logger.error('发送媒体组时出错:', error);
		return null;
	}
}

/**
 * 等待一段时间
 * @param {Number} ms 毫秒数
 * @returns {Promise} Promise对象
 */
function sleep(ms) {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 转发媒体消息到目标群组的指定话题
 * @param {Object} ctx 上下文对象
 * @param {Boolean} afterLogMedia 是否在mediaGroupLog之后调用
 * @param {Boolean} fromQueue 是否来自媒体组队列
 * @returns {Promise<Object|null>} 转发结果或null
 */
async function forwardMedia(ctx, afterLogMedia = false, fromQueue = false) {
	const { message, env, mediaGroup } = ctx;
	const chatId = message.chat.id.toString();

	// 检查是否是回复"#投稿"的情况
	let targetMessage = message;
	let isSubmissionReply = false;

	if (message.text && message.text.trim() === '#投稿' && message.reply_to_message && hasMedia(message.reply_to_message)) {
		// 如果是回复"#投稿"，且回复的消息包含媒体，则使用被回复的消息
		targetMessage = message.reply_to_message;
		isSubmissionReply = true;
	}

	// 获取目标话题ID (使用原始消息进行判断，因为需要检查标签)
	// 如果是#投稿回复，强制使用话题4
	let topicId;
	if (isSubmissionReply || (message.caption && message.caption.includes('#投稿'))) {
		topicId = getTargetTopicId(message, chatId, true); // 强制使用带标签的话题
	} else {
		topicId = getTargetTopicId(message, chatId);
	}
	
	if (!topicId) {
		return null;
	}

	// 默认分级为normal
	let grade = 'normal';

	// 检查是否有特定标签决定分级
	if (targetMessage.caption) {
		if (targetMessage.caption.includes('#NSFW') || targetMessage.caption.includes('#nsfw')) {
			grade = 'nsfw';
		} else if (targetMessage.caption.includes('#SFW') || targetMessage.caption.includes('#sfw')) {
			grade = 'sfw';
		} else if (targetMessage.caption.includes('#奇闻异录')) {
			grade = 'qwyl';
		} else if (targetMessage.caption.includes('#等一位英雄')) {
			grade = 'waitHeroes';
		} else if (targetMessage.caption.includes('#迷惑') || targetMessage.caption.includes('#WTF')) {
			grade = 'confuse';
		}
	}

	try {
		// 检查是否是媒体组
		if (targetMessage.media_group_id && !fromQueue) {
			// 如果是媒体组但不是从队列处理的，则添加到队列
			// 导入队列处理函数
			const { queueMediaGroupForward } = await import('./mediaGroupQueue.js');
			
			// 将媒体组添加到队列，传递 env 和 ctx
			Logger.info(`媒体组消息 ${targetMessage.media_group_id} 添加到转发队列`);
			await queueMediaGroupForward({
				...ctx,
				message: targetMessage,
				env,
				executionCtx: ctx // 传递执行上下文
			});
			
			// 返回null，不直接处理
			return null;
		} 
		// 如果是从队列处理的媒体组
		else if (fromQueue && mediaGroup) {
			Logger.info(`从队列处理媒体组 ${mediaGroup.id}，共 ${mediaGroup.totalCount} 条消息`);
			
			// 解析媒体组消息
			const mediaGroupMessages = mediaGroup.messages.map(item => {
				try {
					return JSON.parse(item.raw_json);
				} catch (e) {
					Logger.error('解析媒体组消息JSON失败:', e);
					return null;
				}
			}).filter(msg => msg !== null);
			
			// 按消息ID排序
			mediaGroupMessages.sort((a, b) => a.message_id - b.message_id);
			
			// 发送媒体组
			return await sendMediaGroup(
				{ ...ctx, message: mediaGroupMessages[0] }, // 使用第一条消息作为上下文
				mediaGroupMessages,
				topicId,
				grade
			);
		}
		// 如果是单个媒体消息
		else {
			return await sendSingleMedia(
				{ ...ctx, message: targetMessage }, // 创建新的上下文，使用目标消息
				topicId,
				grade
			);
		}
	} catch (error) {
		Logger.error('转发媒体时出错:', error);
		return null;
	}
}

export { forwardMedia };