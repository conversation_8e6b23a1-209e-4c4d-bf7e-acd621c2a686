// 导入所需模块
import { sendTelegramRequest } from '../utils/telegramApi.js';

// #region 🤖 Telegram Bot 统一命令处理器
/**
 * 统一的命令处理入口
 * @param {Object} message 消息对象
 * @param {Object} botCtx Bot上下文对象
 * @param {string} botUsername 机器人用户名
 * @param {Object} executionCtx 执行上下文对象
 * @returns {boolean} 是否处理了命令
 */
export async function handleCommand(message, botCtx, botUsername, executionCtx) {
	const text = message.text || '';

	// 检查是否是命令
	if (!text.startsWith('/')) {
		return false;
	}

	// 提取命令和参数
	const parts = text.split(' ');
	let command = parts[0];

	// 在群组中删除所有命令消息（不管是发给谁的）
	const chatId = message.chat.id;
	if (chatId < 0) { // 负数表示群组或频道
		await handleCommandMessageDeletion(message, botCtx, executionCtx, command);
	}

	// 移除可能的@botUsername后缀
	if (command.includes('@')) {
		const [cmd, target] = command.split('@');
		// 如果命令不是针对这个机器人的，忽略它
		if (target.toLowerCase() !== botUsername.toLowerCase()) {
			return false;
		}
		command = cmd;
	}

	// 命令路由处理
	const commandResponse = await routeCommand(command, message, botCtx, executionCtx, text);
	
	// 如果有响应，直接发送
	if (commandResponse) {
		// 提取API方法和参数
		const { method, ...params } = commandResponse;
		const apiUrl = `https://api.telegram.org/bot${botCtx.env.TELEGRAM_BOT_TOKEN}/${method}`;
		
		// 发送请求
		await sendTelegramRequest(botCtx.env, apiUrl, params);
		return true; // 返回true表示已处理命令
	}
	
	return false; // 返回false表示未处理命令
}

/**
 * 处理群组中的命令消息删除逻辑
 * @param {Object} message 消息对象
 * @param {Object} botCtx Bot上下文对象
 * @param {Object} executionCtx 执行上下文对象
 * @param {string} command 命令字符串
 */
async function handleCommandMessageDeletion(message, botCtx, executionCtx, command) {
	const chatId = message.chat.id;
	const { BOT_ADMINS } = await import('../config/constants.js');
	
	// 检查是否应该跳过删除
	let shouldSkipDeletion = 
		// 转发的消息不删除
		message.forward_from || 
		message.forward_from_chat || 
		message.forward_date ||
		// 频道推送（用户ID为777000）不删除
		(message.from && message.from.id === 777000);
	
	// 如果是管理员发送的 /adCheck 命令，也跳过删除
	if (!shouldSkipDeletion && (command === '/adcheck' || command === '/adCheck')) {
		const isAdmin = BOT_ADMINS.includes(message.from?.id);
		if (isAdmin) {
			shouldSkipDeletion = true;
		}
	}

	if (shouldSkipDeletion) {
		let reason = '未知';
		if (message.forward_from || message.forward_from_chat || message.forward_date) {
			reason = '转发消息';
		} else if (message.from && message.from.id === 777000) {
			reason = '频道推送';
		} else if ((command === '/adcheck' || command === '/adCheck')) {
			const isAdmin = BOT_ADMINS.includes(message.from?.id);
			if (isAdmin) {
				reason = '管理员adCheck命令';
			}
		}
		
		const Logger = (await import('../utils/logger.js')).default;
		Logger.debug('跳过删除命令消息', {
			chatId: chatId,
			messageId: message.message_id,
			command: command,
			userId: message.from?.id,
			reason: reason
		});
	} else {
		try {
			const { sendTelegramRequest } = await import('../utils/telegramApi.js');
			const Logger = (await import('../utils/logger.js')).default;
			
			executionCtx.waitUntil(
				sendTelegramRequest(
					botCtx.env,
					`https://api.telegram.org/bot${botCtx.env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
					{
						chat_id: chatId,
						message_id: message.message_id
					}
				).catch(error => {
					Logger.debug('删除群组命令消息失败', {
						chatId: chatId,
						messageId: message.message_id,
						error: error.message
					});
				})
			);
			Logger.debug('已安排删除群组命令消息', {
				chatId: chatId,
				messageId: message.message_id,
				command: command,
				userId: message.from.id
			});
		} catch (error) {
			const Logger = (await import('../utils/logger.js')).default;
			Logger.debug('删除群组命令消息时出错', error);
		}
	}
}

/**
 * 命令路由处理
 * @param {string} command 命令字符串
 * @param {Object} message 消息对象
 * @param {Object} botCtx Bot上下文对象
 * @param {Object} executionCtx 执行上下文对象
 * @param {string} text 完整消息文本
 * @returns {Object|null} 命令响应对象或null
 */
async function routeCommand(command, message, botCtx, executionCtx, text) {
	// 基础命令
	if (command === '/start') {
		const { handleStartCommand } = await import('./command/basic.js');
		return await handleStartCommand(message, botCtx);
	}
	
	if (command === '/help') {
		const { handleHelpCommand } = await import('./command/basic.js');
		return handleHelpCommand(message);
	}
	
	// 骰子命令
	if (command === '/roll' || command.startsWith('/r')) {
		const { handleDiceCommand } = await import('./command/dice.js');
		return handleDiceCommand({ message, cleanedText: text });
	}
	
	// 技能检定命令
	if (command.startsWith('/check') || command.startsWith('/dc')) {
		const { handleSkillCheckCommand } = await import('./command/dice.js');
		return handleSkillCheckCommand({ message, cleanedText: text });
	}
	
	// Ping命令
	if (command === '/ping') {
		const { handlePingCommand } = await import('./command/tools.js');
		return await handlePingCommand(message, text, botCtx);
	}
	
	// 重置命令
	if (command === '/reset') {
		const { handleResetCommand } = await import('./command/tools.js');
		return await handleResetCommand(message, botCtx);
	}
	
	// 贴纸命令
	if (command === '/a') {
		const { handleStickerCommand } = await import('./command/tools.js');
		return await handleStickerCommand(message, text);
	}
	
	// 广告检测命令
	if (command === '/adcheck' || command === '/adCheck') {
		const { handleAdCheckCommand } = await import('./command/admin.js');
		return await handleAdCheckCommand(message, text, botCtx);
	}
	
	// 投稿命令
	if (command === '/0') {
		const { handleContributeCommandRoute } = await import('./command/contribute.js');
		return await handleContributeCommandRoute(message, botCtx);
	}
	
	// 举报命令
	if (command === '/jb') {
		const { handleReportCommand } = await import('./command/report.js');
		return await handleReportCommand(message, botCtx);
	}
	
	// 群规查看命令
	if (command === '/rules') {
		const { handleRulesCommand } = await import('./command/groupAdmin.js');
		return await handleRulesCommand(message, botCtx);
	}
	
	return null;
}