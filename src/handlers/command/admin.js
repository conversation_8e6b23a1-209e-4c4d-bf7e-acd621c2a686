// #region 🛡️ 管理命令处理
/**
 * 处理 /adcheck 命令 - 云过滤检测
 */
export async function handleAdCheckCommand(message, text, botCtx) {
	const Logger = (await import('../../utils/logger.js')).default;
	const { BOT_ADMINS } = await import('../../config/constants.js');
	
	Logger.info('命令信息 -> /adCheck 云过滤检测', text, { message, cleanedText: text });
	
	try {
		// 导入必要的模块
		const { executeCloudFilter, formatDetectionResult } = await import('../../cloudFilter/index.js');
		
		// 检测发送者是否为bot管理员
		const isAdmin = BOT_ADMINS.includes(message.from.id);
		
		// 只有管理员才能使用此命令
		if (!isAdmin) {
			Logger.warn('非管理员尝试使用 /adCheck 命令，静默忽略', {
				userId: message.from.id,
				userName: message.from.first_name,
				chatId: message.chat.id
			});
			return null; // 静默处理，不回复任何消息
		}
		
		let tempMessage;
		let isReplyMode = false;
		
		// 检查是否是回复消息
		if (message.reply_to_message) {
			// 回复模式：检测被回复的消息
			tempMessage = message.reply_to_message;
			isReplyMode = true;
			
			// 如果被回复的消息是转发消息，需要特别处理发送者信息
			let actualSender = message.reply_to_message.from;
			let senderInfo = '普通消息';
			
			if (message.reply_to_message.forward_from) {
				// 从用户转发
				actualSender = message.reply_to_message.forward_from;
				senderInfo = '转发自用户';
			} else if (message.reply_to_message.forward_from_chat) {
				// 从频道/群组转发
				actualSender = null; // 频道消息可能没有具体用户
				senderInfo = `转发自${message.reply_to_message.forward_from_chat.type}`;
			}
			
			// 更新tempMessage的from字段为实际发送者
			if (actualSender) {
				tempMessage = {
					...tempMessage,
					from: actualSender
				};
			}
			
			Logger.debug('使用回复模式检测消息', {
				originalMessageId: message.reply_to_message.message_id,
				replyUserId: message.reply_to_message.from?.id,
				actualSenderId: actualSender?.id,
				senderInfo: senderInfo,
				content: (message.reply_to_message.text || message.reply_to_message.caption || '').substring(0, 100)
			});
		} else {
			// 文本模式：检测命令后面的文本
			const parts = text.split(' ');
			parts.shift(); // 移除命令本身
			const textToCheck = parts.join(' ').trim();
			
			if (!textToCheck) {
				return {
					method: 'sendMessage',
					chat_id: message.chat.id,
					text: '❌ 请提供要检测的文本内容，或回复一条消息使用检测\n\n用法：\n• <code>/adCheck 要检测的文本内容</code>\n• 回复消息 + <code>/adCheck</code>',
					parse_mode: 'HTML',
					reply_to_message_id: message.message_id,
				};
			}
			
			// 创建临时消息对象用于检测
			tempMessage = {
				text: textToCheck,
				caption: null,
				forward_from: null,
				forward_from_chat: null,
				from: message.from,
				chat: message.chat,
				message_id: message.message_id
			};
		}
		
		// 执行云过滤检测（命令模式）
		const filterResult = await executeCloudFilter(tempMessage, botCtx.env, {
			mode: 'command'
		});
		
		// 格式化检测结果
		const formattedResult = formatDetectionResult(
			filterResult, 
			isReplyMode, 
			message, 
			isReplyMode ? message.reply_to_message : null
		);
		
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: formattedResult,
			parse_mode: 'HTML',
			reply_to_message_id: isReplyMode ? message.reply_to_message.message_id : message.message_id,
		};
		
	} catch (error) {
		Logger.error('执行广告检测命令时出错:', error);
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: '❌ 检测过程中出现错误，请稍后重试',
			reply_to_message_id: message.message_id,
		};
	}
}
// #endregion 🛡️ 管理命令处理 