// 导入所需模块
import Logger from '../../utils/logger.js';

// #region 🔧 工具命令处理
/**
 * 处理 /ping 命令 - 测试权限系统和响应时间
 */
export async function handlePingCommand(message, text, botCtx) {
	const startTime = Date.now();
	const chatId = message.chat.id;

	Logger.debug('接收到Ping命令', { 
		userId: message.from.id, 
		chatId: chatId,
		username: message.from.first_name 
	});

	// 权限检测
	Logger.debug('开始执行Ping命令权限检测');
	const { checkPermission } = await import('../permissions.js');
	const permissionResult = await checkPermission({ message, env: botCtx.env });
	
	if (!permissionResult.hasPermission) {
		const endTime = Date.now();
		const executionTime = endTime - startTime;
		
		Logger.warn('Ping命令权限检测失败', {
			userId: message.from.id,
			userName: message.from.first_name,
			chatId: chatId,
			reason: permissionResult.reason,
			details: permissionResult.details,
			executionTime: `${executionTime}ms`
		});
		
		// 构建用户链接
		const userId = message.from.id;
		const userName = message.from.first_name;
		const userLink = `<a href="tg://user?id=${userId}">${userName}</a>`;
		
		// 构建详细的权限状态信息
		const permissionStatus = [
			`群组管理员：${permissionResult.details.isGroupAdmin ? '✅' : '❌'}`,
			`匿名管理员：${permissionResult.details.isAnonymous ? '✅' : '❌'}`,
			`Bot管理员：${permissionResult.details.isBotAdmin ? '✅' : '❌'}`
		];
		
		// 如果有错误信息，添加到状态中
		if (permissionResult.details.error) {
			permissionStatus.push(`错误：${permissionResult.details.error}`);
		}
		
		const statusMessage = [
			`${userLink} ⚠️ 权限检查失败`,
			'',
			'📊 权限状态：',
			...permissionStatus,
			'',
			`⏱️ 执行时间：${executionTime}ms`,
			`🔍 失败原因：${permissionResult.reason}`
		].join('\n');
		
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: statusMessage,
			parse_mode: 'HTML',
		};
	}

	// 权限通过，执行ping逻辑
	const endTime = Date.now();
	const executionTime = endTime - startTime;

	Logger.success('Ping命令执行成功', {
		userId: message.from.id,
		userName: message.from.first_name,
		chatId: chatId,
		executionTime: `${executionTime}ms`,
		permissionSource: permissionResult.reason
	});

	// 构建用户链接
	const userId = message.from.id;
	const userName = message.from.first_name;
	const userLink = `<a href="tg://user?id=${userId}">${userName}</a>`;

	// 构建成功响应
	const successMessage = [
		`${userLink} 🏓 pong!`,
		'',
		'📊 权限状态：',
		`群组管理员：${permissionResult.details.isGroupAdmin ? '✅' : '❌'}`,
		`匿名管理员：${permissionResult.details.isAnonymous ? '✅' : '❌'}`,
		`Bot管理员：${permissionResult.details.isBotAdmin ? '✅' : '❌'}`,
		'',
		`⏱️ 响应时间：${executionTime}ms`,
		`🔑 权限来源：${permissionResult.reason}`
	].join('\n');

	return {
		method: 'sendMessage',
		chat_id: message.chat.id,
		text: successMessage,
		parse_mode: 'HTML',
	};
}

/**
 * 处理 /reset 命令
 */
export async function handleResetCommand(message, botCtx) {
	const { isBotAdmin } = await import('../permissions.js');
	
	if (!isBotAdmin({ message })) {
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: '❌ 权限不足，此命令仅限Bot管理员使用',
			reply_to_message_id: message.message_id,
		};
	}

	try {
		const { resetRateLimiter } = await import('../../utils/telegramApi.js');
		const result = await resetRateLimiter(botCtx.env);
		
		const responseText = result && result.success 
			? '✅ 速率限制器已重置' 
			: '❌ 重置速率限制器失败';
			
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: responseText,
			reply_to_message_id: message.message_id,
		};
	} catch (error) {
		Logger.error('重置速率限制器时出错:', error);
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: '❌ 重置速率限制器时出现错误',
			reply_to_message_id: message.message_id,
		};
	}
}

/**
 * 处理 /a 命令 - 发送贴纸
 */
export async function handleStickerCommand(message, text) {
	Logger.info('命令信息 -> /a 贴纸', text, { message, cleanedText: text });
	
	return {
		method: 'sendSticker',
		chat_id: message.chat.id,
		sticker: 'CAACAgUAAx0CWSukJAACD9FluefIFjplwq1zhNjSc7t3McjlEgACcCoAAs4w0VdofceZD8TTCDQE'
	};
}
// #endregion 🔧 工具命令处理 