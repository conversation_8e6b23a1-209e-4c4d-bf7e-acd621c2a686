// #region 🎲 骰娘功能模块
/**
 * 骰娘功能处理
 * 支持 /r、/r@botname 和 /r{n}d{m} 格式的骰子命令
 * 按照旧版格式回复，包括用户链接和特殊格式化数字
 */

import Logger from '../../utils/logger.js';

// #region 🔧 工具函数
// 生成指定范围内的随机整数
function getRandomInt(min, max) {
	min = Math.ceil(min);
	max = Math.floor(max);
	return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 计算大成功和大失败的阈值
function getCriticalThresholds(diceSides) {
	const criticalSuccessThreshold = Math.round(diceSides * 0.95);
	const criticalFailureThreshold = Math.round(diceSides * 0.05);
	
	return {
		criticalSuccess: Math.min(criticalSuccessThreshold, diceSides), // 确保不超过最大值
		criticalFailure: Math.max(criticalFailureThreshold, 1) // 确保不小于1
	};
}

// 检测骰子结果类型
function checkDiceResult(roll, diceSides) {
	const { criticalSuccess, criticalFailure } = getCriticalThresholds(diceSides);
	
	if (roll >= criticalSuccess) {
		return 'critical_success';
	} else if (roll <= criticalFailure) {
		return 'critical_failure';
	} else {
		return 'normal';
	}
}

// 可爆炸骰子投掷
function rollExplodingDice(diceSides) {
	let total = 0;
	let rolls = [];
	let currentRoll;
	let explosions = 0;
	
	do {
		currentRoll = getRandomInt(1, diceSides);
		rolls.push(currentRoll);
		total += currentRoll;
		
		if (currentRoll === diceSides) {
			explosions++;
			// 防止无限爆炸，最多爆炸10次
			if (explosions >= 10) break;
		}
	} while (currentRoll === diceSides);
	
	return { total, rolls, explosions };
}

// 优势/劣势骰子投掷
function rollAdvantageDisadvantage(diceSides, isAdvantage) {
	const roll1 = getRandomInt(1, diceSides);
	const roll2 = getRandomInt(1, diceSides);
	
	const selectedRoll = isAdvantage ? Math.max(roll1, roll2) : Math.min(roll1, roll2);
	
	return {
		selectedRoll,
		rolls: [roll1, roll2],
		discardedRoll: isAdvantage ? Math.min(roll1, roll2) : Math.max(roll1, roll2)
	};
}

// 保留最高值骰子投掷
function rollKeepHighest(diceCount, diceSides, keepCount) {
	const allRolls = [];
	
	for (let i = 0; i < diceCount; i++) {
		allRolls.push(getRandomInt(1, diceSides));
	}
	
	// 排序并保留最高的几个
	const sortedRolls = [...allRolls].sort((a, b) => b - a);
	const keptRolls = sortedRolls.slice(0, keepCount);
	const discardedRolls = sortedRolls.slice(keepCount);
	
	return {
		allRolls,
		keptRolls,
		discardedRolls,
		total: keptRolls.reduce((sum, roll) => sum + roll, 0)
	};
}
// #endregion 🔧 工具函数

// #region 🎯 主要处理逻辑
	// 处理技能检定命令
export function handleSkillCheckCommand(ctx) {
	const message = ctx.message;
	const text = ctx.cleanedText || message.text || '';
	const chatId = message.chat.id;

	Logger.info('命令信息 -> 技能检定', text, ctx);

	// 解析技能检定命令
	// /check 80 力量 或 /dc 15 开锁
	const checkRegex = /^\/(?:check|dc)\s+(\d+)(?:\s+(.+))?/;
	const match = text.match(checkRegex);

	if (!match) {
		return null; // 格式不匹配
	}

	const targetValue = parseInt(match[1], 10);
	const skillName = match[2] ? match[2].trim() : '';
	const isCheckCommand = text.startsWith('/check');

	// 限制目标值范围
	const limitedTarget = Math.min(Math.max(targetValue, 1), 100);

	// 投掷d100
	const roll = getRandomInt(1, 100);
	const resultType = checkDiceResult(roll, 100);

	// 判断成功失败
	let success = false;
	let successLevel = '';

	if (isCheckCommand) {
		// /check: 投掷值 <= 技能值为成功
		success = roll <= limitedTarget;
		if (resultType === 'critical_success') {
			successLevel = '大成功';
		} else if (resultType === 'critical_failure') {
			successLevel = '大失败';
		} else {
			successLevel = success ? '成功' : '失败';
		}
	} else {
		// /dc: 投掷值 >= DC值为成功
		success = roll >= limitedTarget;
		if (resultType === 'critical_success') {
			successLevel = '大成功';
		} else if (resultType === 'critical_failure') {
			successLevel = '大失败';
		} else {
			successLevel = success ? '成功' : '失败';
		}
	}

	// 构建用户链接和回复消息
	const userId = message.from.id;
	const userName = message.from.first_name;
	const userLink = `<a href="tg://user?id=${userId}">${userName}</a>`;

	// 根据结果类型添加特殊标识
	let resultDisplay = `<code>${roll}</code>`;
	if (resultType === 'critical_success') {
		resultDisplay = `✨<code>${roll}</code>✨`;
	} else if (resultType === 'critical_failure') {
		resultDisplay = `💥<code>${roll}</code>💥`;
	}

	// 构建回复消息
	const commandType = isCheckCommand ? '技能检定' : 'DC检定';
	const targetText = skillName ? `${skillName} (${limitedTarget})` : `${limitedTarget}`;
	const resultEmoji = success ? (resultType === 'critical_success' ? '✨' : '✅') : 
	                             (resultType === 'critical_failure' ? '💥' : '❌');
	
	const replyText = `${userLink} 🎯 ${commandType}: ${targetText}\n` +
	                  `投掷: ${resultDisplay} ${resultEmoji} <b>${successLevel}</b>`;

	return {
		method: 'sendMessage',
		chat_id: chatId,
		text: replyText,
		parse_mode: 'HTML',
	};
}

// 处理骰子命令，返回要发送的消息对象
export function handleDiceCommand(ctx) {
	const message = ctx.message;
	// 优先使用清理后的文本（如果有）
	const text = ctx.cleanedText || message.text || '';
	const chatId = message.chat.id;

	Logger.info('命令信息 -> 骰子', text, ctx);

	// #region 🎛️ 参数解析与验证
	// 默认骰子参数
	let diceCount = 1; 
	let diceSides = 20;
	let diceMode = 'normal'; // normal, exploding, advantage, disadvantage, keep_highest

	// 检测特殊骰子类型
	let specialMode = '';
	if (text.startsWith('/re')) {
		diceMode = 'exploding';
		specialMode = 'e';
	} else if (text.startsWith('/ra')) {
		diceMode = 'advantage';
		specialMode = 'a';
	} else if (text.startsWith('/rd')) {
		diceMode = 'disadvantage';
		specialMode = 'd';
	} else if (text.startsWith('/rk')) {
		diceMode = 'keep_highest';
		specialMode = 'k';
	}

	// 匹配不同格式的骰子命令
	if (specialMode) {
		// 特殊骰子格式: /re3d6, /ra1d20, /rk4d6 等
		const specialRegex = new RegExp(`^\/r${specialMode}(\\d+)d(\\d+)`);
		const specialMatch = text.match(specialRegex);
		
		if (specialMatch) {
			diceCount = parseInt(specialMatch[1], 10);
			diceSides = parseInt(specialMatch[2], 10);
		} else {
			// 简单格式: /re, /ra, /rd, /rk (使用默认值)
			diceCount = 1;
			diceSides = 20;
		}
	} else {
		// 标准骰子格式
		// 1. /r{n}d{m} 格式
		const nDmRegex = /^\/r(\d+)d(\d+)/;
		const nDmMatch = text.match(nDmRegex);

		if (nDmMatch) {
			// 如果匹配到 /r{n}d{m} 格式
			diceCount = parseInt(nDmMatch[1], 10);
			diceSides = parseInt(nDmMatch[2], 10);
		} else if (text.trim().startsWith('/r')) {
			// 如果是简单的 /r 命令，使用默认值
			diceCount = 1;
			diceSides = 20;
		} else {
			// 不是骰子命令，返回null表示不处理
			return null;
		}
	}

	// 限制骰子数量和面数，防止滥用
	diceCount = Math.min(Math.max(diceCount, 1), 100); // 最少1个，最多100个
	diceSides = Math.min(Math.max(diceSides, 2), 1000); // 最少2面，最多1000面
	
	// 对于keep_highest模式，限制保留数量
	let keepCount = diceCount;
	if (diceMode === 'keep_highest') {
		keepCount = Math.max(1, diceCount - 1); // 默认丢弃1个最低的
	}
	// #endregion 🎛️ 参数解析与验证

	// #region 🎰 骰子投掷逻辑
	let results = [];
	let resultTypes = [];
	let total = 0;
	let hasCriticalSuccess = false;
	let hasCriticalFailure = false;
	let specialInfo = ''; // 用于存储特殊骰子的额外信息

	if (diceMode === 'exploding') {
		// 可爆炸骰子
		for (let i = 0; i < diceCount; i++) {
			const explodeResult = rollExplodingDice(diceSides);
			results.push(explodeResult.total);
			total += explodeResult.total;
			
			// 对于爆炸骰子，我们检查最后一个有效结果的类型
			const lastRoll = explodeResult.rolls[explodeResult.rolls.length - 1];
			const resultType = checkDiceResult(lastRoll, diceSides);
			resultTypes.push(resultType);
			
			if (resultType === 'critical_success') hasCriticalSuccess = true;
			if (resultType === 'critical_failure') hasCriticalFailure = true;
			
			// 添加爆炸信息
			if (explodeResult.explosions > 0) {
				specialInfo += `💥×${explodeResult.explosions} `;
			}
		}
	} else if (diceMode === 'advantage' || diceMode === 'disadvantage') {
		// 优势/劣势骰子
		const isAdvantage = diceMode === 'advantage';
		
		for (let i = 0; i < diceCount; i++) {
			const advResult = rollAdvantageDisadvantage(diceSides, isAdvantage);
			results.push(advResult.selectedRoll);
			total += advResult.selectedRoll;
			
			const resultType = checkDiceResult(advResult.selectedRoll, diceSides);
			resultTypes.push(resultType);
			
			if (resultType === 'critical_success') hasCriticalSuccess = true;
			if (resultType === 'critical_failure') hasCriticalFailure = true;
			
			// 添加丢弃信息
			specialInfo += `[${advResult.rolls.join(',')}→${advResult.selectedRoll}] `;
		}
	} else if (diceMode === 'keep_highest') {
		// 保留最高值骰子
		const keepResult = rollKeepHighest(diceCount, diceSides, keepCount);
		results = keepResult.keptRolls;
		total = keepResult.total;
		
		// 检查保留的骰子的类型
		for (const roll of keepResult.keptRolls) {
			const resultType = checkDiceResult(roll, diceSides);
			resultTypes.push(resultType);
			
			if (resultType === 'critical_success') hasCriticalSuccess = true;
			if (resultType === 'critical_failure') hasCriticalFailure = true;
		}
		
		// 添加丢弃信息
		if (keepResult.discardedRolls.length > 0) {
			specialInfo = `(丢弃: ${keepResult.discardedRolls.join(',')}) `;
		}
	} else {
		// 标准骰子
		for (let i = 0; i < diceCount; i++) {
			const roll = getRandomInt(1, diceSides);
			const resultType = checkDiceResult(roll, diceSides);
			
			results.push(roll);
			resultTypes.push(resultType);
			total += roll;
			
			// 记录是否有大成功或大失败
			if (resultType === 'critical_success') {
				hasCriticalSuccess = true;
			} else if (resultType === 'critical_failure') {
				hasCriticalFailure = true;
			}
		}
	}
	// #endregion 🎰 骰子投掷逻辑

	// #region 💬 消息格式化与回复
	// 构建用户链接
	const userId = message.from.id;
	const userName = message.from.first_name;
	const userLink = `<a href="tg://user?id=${userId}">${userName}</a>`;

	// 构建回复消息，更接近旧版格式
	let replyText;

	// 确定骰子类型的显示标识
	const getModeEmoji = (mode) => {
		switch (mode) {
			case 'exploding': return '💥';
			case 'advantage': return '⬆️';
			case 'disadvantage': return '⬇️';
			case 'keep_highest': return '🔝';
			default: return '🎲';
		}
	};

	const modeEmoji = getModeEmoji(diceMode);
	const isSpecialDice = diceMode !== 'normal';

	if (results.length === 1 && !isSpecialDice) {
		// 单个标准骰子的简单格式
		const result = results[0];
		const resultType = resultTypes[0];
		
		// 根据结果类型添加特殊标识
		let resultDisplay = `<code>${result}</code>`;
		if (resultType === 'critical_success') {
			resultDisplay = `✨<code>${result}</code>✨ <b>大成功!</b>`;
		} else if (resultType === 'critical_failure') {
			resultDisplay = `💥<code>${result}</code>💥 <b>大失败!</b>`;
		}
		
		replyText = `${userLink} 🎲 ${resultDisplay}`;
	} else {
		// 多个骰子或特殊骰子显示详细结果
		const formattedResults = results.map((r, index) => {
			const resultType = resultTypes[index];
			if (resultType === 'critical_success') {
				return `✨<code>${r}</code>✨`;
			} else if (resultType === 'critical_failure') {
				return `💥<code>${r}</code>💥`;
			} else {
				return `<code>${r}</code>`;
			}
		}).join(', ');
		
		// 构建骰子描述
		let diceDescription;
		if (diceMode === 'keep_highest') {
			diceDescription = `${diceCount}d${diceSides}k${keepCount}`;
		} else if (diceMode === 'advantage') {
			diceDescription = `${diceCount}d${diceSides}优势`;
		} else if (diceMode === 'disadvantage') {
			diceDescription = `${diceCount}d${diceSides}劣势`;
		} else if (diceMode === 'exploding') {
			diceDescription = `${diceCount}d${diceSides}爆炸`;
		} else {
			diceDescription = `${diceCount}d${diceSides}`;
		}
		
		// 构建基础结果文本
		replyText = `${userLink} ${modeEmoji} ${diceDescription}: [${formattedResults}] = <code>${total}</code>`;
		
		// 添加特殊信息
		if (specialInfo.trim()) {
			replyText += `\n${specialInfo.trim()}`;
		}
		
		// 添加整体大成功/大失败提示
		if (hasCriticalSuccess && hasCriticalFailure) {
			replyText += `\n🎭 <b>冰火两重天!</b>`;
		} else if (hasCriticalSuccess) {
			replyText += `\n✨ <b>包含大成功!</b>`;
		} else if (hasCriticalFailure) {
			replyText += `\n💥 <b>包含大失败!</b>`;
		}
	}

	// 返回一个Telegram API方法对象，用于webhook回复
	return {
		method: 'sendMessage',
		chat_id: chatId,
		text: replyText,
		parse_mode: 'HTML',
		// 不使用reply_to_message_id，因为原命令消息可能已被删除
	};
	// #endregion 💬 消息格式化与回复
}
// #endregion 🎯 主要处理逻辑
// #endregion 🎲 骰娘功能模块
