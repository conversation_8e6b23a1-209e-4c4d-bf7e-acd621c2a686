// #region 🏠 基础命令处理
/**
 * 处理 /start 命令
 */
export async function handleStartCommand(message, botCtx) {
	const text = message.text || '';
	const parts = text.split(' ');
	
	// 检查是否是验证启动命令
	if (parts.length > 1 && parts[1].startsWith('verify_')) {
		const verificationCode = parts[1].substring(7); // 移除 'verify_' 前缀
		const { executeVerificationActions } = await import('../memberManagement/actions/verificationActions.js');
		const result = await executeVerificationActions({
			type: 'handle_verification_start',
			message,
			verificationCode,
			env: botCtx.env,
			ctx: botCtx.executionCtx
		});
		
		if (result.success && result.response) {
			return result.response;
		} else if (result.success) {
			// 如果成功但没有直接响应（比如发送了图片），返回空
			return null;
		} else {
			// 处理失败，返回错误响应
			return result.response || {
				method: 'sendMessage',
				chat_id: message.chat.id,
				text: '❌ 验证处理失败，请稍后重试'
			};
		}
	}
	
	// 普通的 /start 命令
	return {
		method: 'sendMessage',
		chat_id: message.chat.id,
		text: '欢迎使用机器人！',
	};
}

/**
 * 处理 /help 命令
 */
export function handleHelpCommand(message) {
	return {
		method: 'sendMessage',
		chat_id: message.chat.id,
		text: '这是帮助信息...',
	};
}
// #endregion 🏠 基础命令处理 