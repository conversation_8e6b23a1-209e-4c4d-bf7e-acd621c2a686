// 导入所需模块
import { sendTelegramRequest } from '../../utils/telegramApi.js';
import { DefaultGroupsBind } from '../../config/constants.js';
import Logger from '../../utils/logger.js';
import { createCommandContributeButtons } from '../../utils/buttonUtils.js';
import { scheduleMessageDelete } from '../../utils/messageScheduler.js';

// #region 📤 投稿命令处理
/**
 * 处理 /0 投稿命令路由
 */
export async function handleContributeCommandRoute(message, botCtx) {
	try {
		// 检查管理员权限
		const { checkPermission } = await import('../permissions.js');
		const permissionResult = await checkPermission({ message, env: botCtx.env });
		
		if (!permissionResult.hasPermission) {
			Logger.warn('/0命令权限检测失败', {
				userId: message.from.id,
				userName: message.from.first_name,
				chatId: message.chat.id,
				reason: permissionResult.reason,
				details: permissionResult.details
			});
			
			// 静默处理：权限不足时不回复任何消息
			return null;
		}
		
		Logger.success('/0命令权限验证通过', {
			userId: message.from.id,
			userName: message.from.first_name,
			chatId: message.chat.id,
			permissionSource: permissionResult.reason
		});
		
		// 调用实际的投稿命令处理函数
		await handleContributeCommand(message, botCtx);
		return null; // handleContributeCommand内部处理所有响应
	} catch (error) {
		Logger.error('处理/0命令时出错:', error);
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: '❌ 处理投稿命令时出现错误',
			reply_to_message_id: message.message_id,
		};
	}
}

/**
 * 处理 /0 投稿命令
 * @param {Object} message 消息对象
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Object|null>} 响应对象或null
 */
async function handleContributeCommand(message, ctx) {
	try {
		const { env } = ctx;
		const chatId = message.chat.id.toString();
		const replyMessage = message.reply_to_message;

		Logger.debug('处理/0投稿命令:', { chatId, messageId: message.message_id });

		// 1. 检查是否在允许的群组中
		const groupConfig = DefaultGroupsBind.find(group => group.id === chatId);
		if (!groupConfig) {
			Logger.debug('群组不在允许范围内:', chatId);
			return null;
		}

		// 2. 检查是否是回复消息
		if (!replyMessage) {
			Logger.debug('不是回复消息，跳过处理');
			// 删除命令消息
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
				chat_id: chatId,
				message_id: message.message_id
			});
			return null;
		}

		// 3. 检查回复的消息是否包含媒体或文本
		if (!hasMediaContent(replyMessage) && !replyMessage.text) {
			Logger.debug('回复的消息不包含媒体或文本，跳过处理');
			// 删除命令消息
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
				chat_id: chatId,
				message_id: message.message_id
			});
			return null;
		}

		// 4. 删除命令消息
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
			chat_id: chatId,
			message_id: message.message_id
		});

		// 5. 检查源消息是否可访问（是否来自机器人或频道）
		if ((replyMessage.from && replyMessage.from.is_bot) || (replyMessage.from && replyMessage.from.id === 777000)) {
			let alertText = '';
			
			if (replyMessage.from.id === 777000) {
				Logger.debug('检测到回复的是Telegram官方频道消息，无法投稿');
				alertText = '🤔 不该处理的消息，这是群内绑定频道发送的推送';
			} else {
				Logger.debug('检测到回复的是机器人消息，无法投稿');
				alertText = '🥺 无法取得消息内容，可能是其他机器人发送的消息';
			}
			
			// 发送提示消息，5秒后自动删除
			const alertMessage = await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
				chat_id: chatId,
				text: alertText,
				reply_to_message_id: replyMessage.message_id
			});
			
			// 启动5秒自动删除定时器
			if (alertMessage.ok) {
				await scheduleMessageDelete(
					env,
					chatId,
					alertMessage.result.message_id,
					5,
					'投稿提示消息自动删除'
				);
			}
			
			return null; // 不生成投稿按钮，直接返回
		}

		// 6. 创建投稿处理按钮
		const contributeButtons = createCommandContributeButtons(replyMessage, groupConfig);

		// 7. 发送带按钮的回复消息
		// 获取原文说明文字用于显示
		const originalText = replyMessage.text || '';
		const originalCaption = replyMessage.caption && replyMessage.caption.trim() ? replyMessage.caption.trim() : '';
		let displayText = '📝 投稿处理';
		
		// 优先使用文本内容，如果没有则使用说明文字
		if (originalText) {
			displayText += '\n' + originalText;
		} else if (originalCaption) {
			displayText += '\n' + originalCaption;
		}
		
		const responseResult = await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
			chat_id: chatId,
			reply_to_message_id: replyMessage.message_id,
			text: displayText,
			reply_markup: contributeButtons
		});

		if (responseResult.ok) {
			const buttonMessageId = responseResult.result.message_id;
			Logger.debug('投稿处理按钮已发送:', buttonMessageId);
			
			// 8. 启动15秒自动删除定时器
			await scheduleMessageDelete(
				env,
				chatId,
				buttonMessageId,
				15,
				'投稿按钮自动删除'
			);
		} else {
			Logger.error('发送投稿处理按钮失败:', responseResult.description);
		}

		return null; // 不需要返回额外响应
	} catch (error) {
		Logger.error('处理/0投稿命令时出错:', error);
		return null;
	}
}

/**
 * 检查消息是否包含媒体内容
 * @param {Object} message 消息对象
 * @returns {boolean} 是否包含媒体
 */
function hasMediaContent(message) {
	return !!(
		message.photo ||
		message.video ||
		message.animation ||
		message.document ||
		message.audio ||
		message.voice ||
		message.video_note ||
		message.sticker
	);
}
// #endregion 📤 投稿命令处理 