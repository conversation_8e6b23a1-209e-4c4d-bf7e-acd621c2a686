/**
 * 群组管理相关命令处理器
 * 处理群规查看等功能
 */

import Logger from '../../utils/logger.js';
import { sendTelegramRequest } from '../../utils/telegramApi.js';
import { isGroupAdmin, isBotAdmin } from '../permissions.js';
import { 
	getGroupRules, 
	formatRulesDisplay
} from '../../utils/groupRulesUtils.js';

/**
 * 处理 /rules 命令 - 查看群规
 * @param {Object} message 消息对象
 * @param {Object} ctx Bot上下文对象
 * @returns {Promise<Object|null>} API响应对象或null
 */
export async function handleRulesCommand(message, ctx) {
	try {
		const { env } = ctx;
		const chatId = message.chat.id.toString();
		const userId = message.from?.id;
		const userName = message.from?.first_name || '未知用户';

		// 检查是否在群组中
		if (message.chat.type === 'private') {
			return {
				method: 'sendMessage',
				chat_id: chatId,
				text: '❌ 此命令只能在群组中使用',
				reply_to_message_id: message.message_id
			};
		}

		Logger.info('/rules 命令被调用:', {
			chatId,
			userId,
			userName
		});

		// 获取群规配置
		const rulesConfig = await getGroupRules(env, chatId);
		const replyText = formatRulesDisplay(rulesConfig);

		return {
			method: 'sendMessage',
			chat_id: chatId,
			text: replyText,
			parse_mode: 'Markdown',
			disable_web_page_preview: true,
			reply_to_message_id: message.message_id
		};

	} catch (error) {
		Logger.error('处理 /rules 命令时出错:', error);
		
		return {
			method: 'sendMessage',
			chat_id: message.chat.id,
			text: '❌ 处理命令时出错，请稍后再试',
			reply_to_message_id: message.message_id
		};
	}
} 