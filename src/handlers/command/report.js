/**
 * 举报功能命令处理器
 * 处理 /jb 举报命令，支持私聊通知管理员和推送到管理群
 */

import Logger from '../../utils/logger.js';
import { sendTelegramRequest } from '../../utils/telegramApi.js';
import { 
	getGroupConfig, 
	getReportTargets, 
	createReportLog, 
	updateReportNotificationStatus,
	getHandleActionDisplayName
} from '../../utils/groupConfigUtils.js';

// #region 📢 举报命令处理

/**
 * 处理举报命令
 * @param {Object} message 消息对象
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Object|null>} 响应对象或null
 */
export async function handleReportCommand(message, ctx) {
	try {
		const { env } = ctx;
		const chatId = message.chat.id.toString();
		const reporterId = message.from.id;
		const reporterName = message.from.first_name || '匿名用户';
		
		Logger.debug('处理举报命令:', { 
			chatId, 
			reporterId, 
			reporterName,
			hasReply: !!message.reply_to_message 
		});

		// 1. 检查是否在群组中（举报功能仅限群组）
		if (chatId > 0) {
			Logger.debug('私聊中不支持举报功能');
			return {
				method: 'sendMessage',
				chat_id: chatId,
				text: '⚠️ 举报功能仅在群组中可用',
				reply_to_message_id: message.message_id
			};
		}

		// 2. 查询群组配置
		const groupConfig = await getGroupConfig(env, chatId);
		if (!groupConfig || groupConfig.report_mode === 'disabled') {
			Logger.debug('群组未开启举报功能:', { chatId, config: groupConfig?.report_mode });
			return {
				method: 'sendMessage',
				chat_id: chatId,
				text: '⚠️ 本群暂未开启举报功能\n\n如需开启，请联系群管理员配置',
				reply_to_message_id: message.message_id
			};
		}

		// 3. 处理举报内容
		const reportData = await collectReportData(message, groupConfig);
		
		// 4. 创建举报记录
		const reportId = await createReportLog(env, reportData);
		if (!reportId) {
			Logger.error('创建举报记录失败');
			return {
				method: 'sendMessage',
				chat_id: chatId,
				text: '❌ 举报提交失败，请稍后重试',
				reply_to_message_id: message.message_id
			};
		}

		// 5. 根据配置模式处理举报
		let notificationResult = false;
		if (groupConfig.report_mode === 'private') {
			notificationResult = await handlePrivateReport(env, chatId, reporterId, reporterName, message, reportId);
		} else if (groupConfig.report_mode === 'group') {
			notificationResult = await handleGroupReport(env, chatId, reporterId, reporterName, message, groupConfig, reportId);
		}

		// 6. 更新通知状态
		await updateReportNotificationStatus(env, reportId, notificationResult ? 'sent' : 'failed');

		// 7. 返回确认消息
		const responseText = notificationResult 
			? '✅ 举报已提交，管理员将尽快处理'
			: '⚠️ 举报已记录，但通知发送可能存在问题';
			
		return {
			method: 'sendMessage',
			chat_id: chatId,
			text: responseText,
			reply_to_message_id: message.message_id
		};

	} catch (error) {
		Logger.error('处理举报命令失败:', error);
		return {
			method: 'sendMessage',
			chat_id: message.chat.id.toString(),
			text: '❌ 举报处理失败，请稍后重试',
			reply_to_message_id: message.message_id
		};
	}
}

// #endregion 📢 举报命令处理

// #region 🔍 举报数据收集

/**
 * 收集举报相关数据
 * @param {Object} message 消息对象
 * @param {Object} groupConfig 群组配置
 * @returns {Object} 举报数据
 */
async function collectReportData(message, groupConfig) {
	const chatId = message.chat.id.toString();
	const reporterId = message.from.id;
	const reporterName = message.from.first_name || '匿名用户';
	
	// 检查是否回复了某条消息
	let targetMessageId = null;
	let reportContent = '';
	
	if (message.reply_to_message) {
		targetMessageId = message.reply_to_message.message_id;
		
		// 尝试获取被举报消息的内容
		const targetMessage = message.reply_to_message;
		if (targetMessage.text) {
			reportContent = targetMessage.text.substring(0, 500); // 限制长度
		} else if (targetMessage.caption) {
			reportContent = targetMessage.caption.substring(0, 500);
		} else {
			reportContent = '[媒体消息]';
		}
	}
	
	// 获取举报原因（从命令参数中）
	const commandParts = message.text.split(' ');
	const reportReason = commandParts.length > 1 ? commandParts.slice(1).join(' ') : '违规内容';
	
	return {
		chat_id: chatId,
		reporter_id: reporterId,
		reporter_name: reporterName,
		target_message_id: targetMessageId,
		report_reason: reportReason,
		report_content: reportContent,
		report_mode: groupConfig.report_mode,
		notification_targets: groupConfig.report_target_groups || []
	};
}

// #endregion 🔍 举报数据收集

// #region 👥 私聊通知处理

/**
 * 处理私聊通知所有管理员
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @param {number} reporterId 举报者ID
 * @param {string} reporterName 举报者姓名
 * @param {Object} message 消息对象
 * @param {number} reportId 举报记录ID
 * @returns {Promise<boolean>} 是否成功
 */
async function handlePrivateReport(env, chatId, reporterId, reporterName, message, reportId) {
	try {
		// 获取群组所有管理员
		const admins = await getGroupAdmins(env, chatId);
		let successCount = 0;
		
		for (const admin of admins) {
			if (admin.user.is_bot) continue; // 跳过机器人
			
			try {
				const notificationText = buildPrivateNotificationText(message, reporterName, reportId);
				const keyboard = buildReportHandleKeyboard(reportId, chatId);
				
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
					chat_id: admin.user.id,
					text: notificationText,
					reply_markup: keyboard,
					parse_mode: 'HTML'
				});
				
				successCount++;
				Logger.debug('私聊通知管理员成功:', { adminId: admin.user.id, adminName: admin.user.first_name });
			} catch (error) {
				Logger.warn(`私聊通知管理员失败 ${admin.user.id}:`, error);
			}
		}
		
		Logger.info('私聊通知完成:', { total: admins.length, success: successCount });
		return successCount > 0;
	} catch (error) {
		Logger.error('私聊通知处理失败:', error);
		return false;
	}
}

/**
 * 获取群组管理员列表
 * @param {Object} env 环境变量
 * @param {string} chatId 群组ID
 * @returns {Promise<Array>} 管理员列表
 */
async function getGroupAdmins(env, chatId) {
	try {
		const response = await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getChatAdministrators`, {
			chat_id: chatId
		});
		
		if (response.ok) {
			return response.result || [];
		}
		
		Logger.warn('获取群组管理员失败:', response);
		return [];
	} catch (error) {
		Logger.error('获取群组管理员异常:', error);
		return [];
	}
}

// #endregion 👥 私聊通知处理

// #region 📢 群组推送处理

/**
 * 处理推送到指定管理群
 * @param {Object} env 环境变量
 * @param {string} chatId 源群组ID
 * @param {number} reporterId 举报者ID
 * @param {string} reporterName 举报者姓名
 * @param {Object} message 消息对象
 * @param {Object} groupConfig 群组配置
 * @param {number} reportId 举报记录ID
 * @returns {Promise<boolean>} 是否成功
 */
async function handleGroupReport(env, chatId, reporterId, reporterName, message, groupConfig, reportId) {
	try {
		// 获取推送目标群组
		const targets = await getReportTargets(env, chatId);
		if (targets.length === 0) {
			Logger.warn('没有配置推送目标群组:', chatId);
			return false;
		}
		
		let successCount = 0;
		
		for (const target of targets) {
			try {
				const notificationText = buildGroupNotificationText(message, reporterName, reportId);
				const keyboard = buildReportHandleKeyboard(reportId, chatId);
				
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
					chat_id: target.chat_id,
					text: notificationText,
					reply_markup: keyboard,
					parse_mode: 'HTML'
				});
				
				successCount++;
				Logger.debug('群组推送成功:', { targetId: target.chat_id, targetTitle: target.title });
			} catch (error) {
				Logger.warn(`群组推送失败 ${target.chat_id}:`, error);
			}
		}
		
		Logger.info('群组推送完成:', { total: targets.length, success: successCount });
		return successCount > 0;
	} catch (error) {
		Logger.error('群组推送处理失败:', error);
		return false;
	}
}

// #endregion 📢 群组推送处理

// #region 📝 通知消息构建

/**
 * 构建私聊通知消息文本
 * @param {Object} message 原始消息
 * @param {string} reporterName 举报者姓名
 * @param {number} reportId 举报记录ID
 * @returns {string} 通知文本
 */
function buildPrivateNotificationText(message, reporterName, reportId) {
	const chatTitle = message.chat.title || '未知群组';
	const chatId = message.chat.id.toString();
	const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
	
	let text = `📢 <b>收到群组举报</b>\n\n`;
	text += `🏷️ <b>群组:</b> ${chatTitle}\n`;
	text += `👤 <b>举报者:</b> ${reporterName}\n`;
	text += `⏰ <b>时间:</b> ${timestamp}\n`;
	text += `🆔 <b>举报ID:</b> #${reportId}\n\n`;
	
	// 如果有回复的消息，添加内容预览
	if (message.reply_to_message) {
		text += `📄 <b>被举报内容:</b>\n`;
		if (message.reply_to_message.text) {
			const preview = message.reply_to_message.text.substring(0, 200);
			text += `<code>${preview}${message.reply_to_message.text.length > 200 ? '...' : ''}</code>\n\n`;
		} else {
			text += `<i>[媒体消息]</i>\n\n`;
		}
	}
	
	text += `请及时处理此举报。`;
	
	return text;
}

/**
 * 构建群组推送通知消息文本
 * @param {Object} message 原始消息
 * @param {string} reporterName 举报者姓名
 * @param {number} reportId 举报记录ID
 * @returns {string} 通知文本
 */
function buildGroupNotificationText(message, reporterName, reportId) {
	const chatTitle = message.chat.title || '未知群组';
	const chatId = message.chat.id.toString();
	const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
	
	let text = `📢 <b>群组举报通知</b>\n\n`;
	text += `🏷️ <b>源群组:</b> ${chatTitle}\n`;
	text += `👤 <b>举报者:</b> ${reporterName}\n`;
	text += `⏰ <b>时间:</b> ${timestamp}\n`;
	text += `🆔 <b>举报ID:</b> #${reportId}\n\n`;
	
	// 如果有回复的消息，添加内容预览
	if (message.reply_to_message) {
		text += `📄 <b>被举报内容:</b>\n`;
		if (message.reply_to_message.text) {
			const preview = message.reply_to_message.text.substring(0, 200);
			text += `<code>${preview}${message.reply_to_message.text.length > 200 ? '...' : ''}</code>\n\n`;
		} else {
			text += `<i>[媒体消息]</i>\n\n`;
		}
	}
	
	return text;
}

/**
 * 构建举报处理按钮键盘
 * @param {number} reportId 举报记录ID
 * @param {string} chatId 源群组ID
 * @returns {Object} 键盘对象
 */
function buildReportHandleKeyboard(reportId, chatId) {
	return {
		inline_keyboard: [
			[
				{ 
					text: '🔍 查看群组', 
					url: `https://t.me/c/${chatId.replace('-100', '')}` 
				}
			],
			[
				{ 
					text: '✅ 已处理', 
					callback_data: `report_handle:${reportId}:handled` 
				},
				{ 
					text: '👁️ 忽略', 
					callback_data: `report_handle:${reportId}:ignored` 
				}
			],
			[
				{ 
					text: '⚠️ 警告', 
					callback_data: `report_handle:${reportId}:warned` 
				},
				{ 
					text: '🚫 封禁', 
					callback_data: `report_handle:${reportId}:banned` 
				}
			]
		]
	};
}

// #endregion 📝 通知消息构建

// #region 🔄 举报处理回调

/**
 * 处理举报处理回调
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 * @returns {Promise<boolean>} 是否成功处理
 */
export async function handleReportCallback(callbackQuery, env) {
	try {
		const { data, from, message } = callbackQuery;
		
		if (!data.startsWith('report_handle:')) {
			return false; // 不是举报处理回调
		}
		
		const [, reportIdStr, action] = data.split(':');
		const reportId = parseInt(reportIdStr);
		
		if (!reportId || !action) {
			Logger.warn('举报回调数据格式错误:', data);
			return false;
		}
		
		// 更新举报处理状态
		const handleData = {
			handle_status: action === 'ignored' ? 'ignored' : 'handled',
			handled_by: from.id,
			handled_by_name: from.first_name || '管理员',
			handle_action: action,
			handle_comment: ''
		};
		
		const { updateReportHandleStatus } = await import('../../utils/groupConfigUtils.js');
		const updateSuccess = await updateReportHandleStatus(env, reportId, handleData);
		
		if (updateSuccess) {
			// 发送处理确认
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: callbackQuery.id,
				text: `${getHandleActionDisplayName(action)} - 举报已处理`,
				show_alert: false
			});
			
			// 更新消息显示处理状态
			await updateReportMessage(env, message, reportId, action, from.first_name);
		} else {
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: callbackQuery.id,
				text: '❌ 处理失败，请稍后重试',
				show_alert: false
			});
		}
		
		return true;
	} catch (error) {
		Logger.error('处理举报回调失败:', error);
		return false;
	}
}

/**
 * 更新举报消息显示处理状态
 * @param {Object} env 环境变量
 * @param {Object} message 消息对象
 * @param {number} reportId 举报记录ID
 * @param {string} action 处理动作
 * @param {string} handlerName 处理人姓名
 */
async function updateReportMessage(env, message, reportId, action, handlerName) {
	try {
		let updatedText = message.text || '';
		
		// 在消息末尾添加处理状态
		const statusText = `\n\n📋 <b>处理状态:</b> ${getHandleActionDisplayName(action)}\n👤 <b>处理人:</b> ${handlerName}\n⏰ <b>处理时间:</b> ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`;
		
		if (!updatedText.includes('📋 处理状态:')) {
			updatedText += statusText;
		}
		
		// 移除操作按钮
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageText`, {
			chat_id: message.chat.id,
			message_id: message.message_id,
			text: updatedText,
			parse_mode: 'HTML',
			reply_markup: {
				inline_keyboard: [[
					{ 
						text: '✅ 已处理', 
						callback_data: 'report_handled' 
					}
				]]
			}
		});
	} catch (error) {
		Logger.warn('更新举报消息状态失败:', error);
	}
}

// #endregion 🔄 举报处理回调 