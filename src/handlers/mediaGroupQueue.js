/**
 * 媒体组转发队列处理工具
 * 用于解决媒体组消息一张一张转发的问题
 */

import Logger from '../utils/logger.js';
import { forwardMedia } from './forwardMedia.js';
import { MEDIA_GROUP_DELAY } from '../config/constants.js';

/**
 * 将媒体组添加到转发队列
 * @param {Object} ctx 上下文对象
 * @returns {Promise<boolean>} 是否成功添加到队列
 */
export async function queueMediaGroupForward(ctx) {
    try {
        const { message, env } = ctx;
        const mediaGroupId = message.media_group_id;
        const chatId = message.chat.id.toString();
        const fromId = message.from?.id;
        
        if (!mediaGroupId) {
            Logger.warn('尝试将非媒体组消息添加到队列');
            return false;
        }
        
        const now = Math.floor(Date.now() / 1000);
        const processAfter = now + Math.ceil(MEDIA_GROUP_DELAY / 1000);
        
        // 查询队列中是否已存在该媒体组
        const existingQuery = await env.DB.prepare(
            `SELECT id, updated_at FROM tg_forward_media_queue 
             WHERE media_group_id = ? AND chat_id = ? AND status = 'pending'`
        ).bind(mediaGroupId, chatId).first();
        
        if (existingQuery) {
            // 更新现有记录的时间戳
            Logger.debug(`更新媒体组转发队列记录: ${mediaGroupId}`);
            await env.DB.prepare(
                `UPDATE tg_forward_media_queue 
                 SET updated_at = ?, process_after = ?
                 WHERE id = ?`
            ).bind(now, processAfter, existingQuery.id).run();
        } else {
            // 创建新记录
            Logger.debug(`添加媒体组到转发队列: ${mediaGroupId}`);
            await env.DB.prepare(
                `INSERT INTO tg_forward_media_queue 
                 (media_group_id, chat_id, from_id, created_at, updated_at, process_after)
                 VALUES (?, ?, ?, ?, ?, ?)`
            ).bind(mediaGroupId, chatId, fromId, now, now, processAfter).run();
        }
        
        // 启动延迟处理任务
        scheduleMediaGroupProcessing(ctx, mediaGroupId, chatId);
        
        return true;
    } catch (error) {
        Logger.error(`将媒体组添加到转发队列时出错:`, error);
        return false;
    }
}

/**
 * 调度媒体组处理
 * @param {Object} ctx 上下文对象
 * @param {string} mediaGroupId 媒体组ID
 * @param {string} chatId 聊天ID
 */
async function scheduleMediaGroupProcessing(ctx, mediaGroupId, chatId) {
    const { env, executionCtx } = ctx;
    if (!executionCtx) {
        Logger.error('执行上下文不存在');
        return;
    }

    // 使用 executionCtx.waitUntil 包装 setTimeout
    executionCtx.waitUntil(
        new Promise((resolve) => {
            setTimeout(async () => {
                try {
                    // 创建一个包含必要信息的上下文
                    const processCtx = {
                        ...ctx,
                        message: {
                            ...ctx.message,
                            media_group_id: mediaGroupId,
                            chat: { id: chatId }
                        }
                    };
                    await processMediaGroupQueue(processCtx, mediaGroupId, chatId);
                    resolve();
                } catch (error) {
                    Logger.error(`处理媒体组队列时出错:`, error);
                    resolve();
                }
            }, MEDIA_GROUP_DELAY);
        })
    );
}

/**
 * 处理媒体组队列
 * @param {Object} ctx 上下文对象
 * @param {string} mediaGroupId 媒体组ID
 * @param {string} chatId 聊天ID
 */
async function processMediaGroupQueue(ctx, mediaGroupId, chatId) {
    if (!mediaGroupId || !chatId) {
        Logger.error(`处理媒体组队列时缺少必要参数: mediaGroupId=${mediaGroupId}, chatId=${chatId}`);
        return;
    }
    
    const { env } = ctx;
    const now = Math.floor(Date.now() / 1000);
    
    // 查询队列记录
    const queueRecord = await env.DB.prepare(
        `SELECT id, updated_at, process_after FROM tg_forward_media_queue 
         WHERE media_group_id = ? AND chat_id = ? AND status = 'pending'`
    ).bind(mediaGroupId, chatId).first();
    
    if (!queueRecord) {
        Logger.warn(`未找到媒体组队列记录: ${mediaGroupId}`);
        return;
    }
    
    // 检查是否应该处理
    if (now < queueRecord.process_after) {
        Logger.debug(`媒体组 ${mediaGroupId} 还未到处理时间，跳过`);
        return;
    }
    
    // 更新状态为处理中
    await env.DB.prepare(
        `UPDATE tg_forward_media_queue SET status = 'processing' WHERE id = ?`
    ).bind(queueRecord.id).run();
    
    try {
        // 根据tg_log_media_group表的实际结构查询媒体组消息
        // 使用id作为排序字段，因为它是自增主键
        const mediaGroupMessages = await env.DB.prepare(
            `SELECT * FROM tg_log_media_group 
             WHERE media_group_id = ? 
             ORDER BY id ASC`
        ).bind(mediaGroupId).all();
        
        if (!mediaGroupMessages.results || mediaGroupMessages.results.length === 0) {
            Logger.warn(`未找到媒体组消息记录: ${mediaGroupId}`);
            await markQueueAsFailed(env, queueRecord.id, '未找到媒体组消息记录');
            return;
        }
        
        Logger.debug(`开始处理媒体组 ${mediaGroupId}，共 ${mediaGroupMessages.results.length} 条消息`);
        
        // 创建一个包含完整媒体组信息的上下文对象
        const mediaGroupCtx = {
            ...ctx,
            mediaGroup: {
                id: mediaGroupId,
                messages: mediaGroupMessages.results,
                totalCount: mediaGroupMessages.results.length
            }
        };
        
        // 调用转发函数，传入完整的媒体组上下文
        await forwardMedia(mediaGroupCtx, false, true);
        
        // 更新状态为已完成
        await env.DB.prepare(
            `UPDATE tg_forward_media_queue SET status = 'completed' WHERE id = ?`
        ).bind(queueRecord.id).run();
        
        Logger.success(`媒体组 ${mediaGroupId} 处理完成`);
    } catch (error) {
        Logger.error(`处理媒体组 ${mediaGroupId} 时出错:`, error);
        await markQueueAsFailed(env, queueRecord.id, error.message);
    }
}

/**
 * 标记队列记录为失败
 * @param {Object} env 环境变量
 * @param {number} queueId 队列记录ID
 * @param {string} errorMessage 错误消息
 */
async function markQueueAsFailed(env, queueId, errorMessage) {
    try {
        // 根据tg_forward_media_queue表的结构，只更新status字段
        await env.DB.prepare(
            `UPDATE tg_forward_media_queue SET status = 'failed' WHERE id = ?`
        ).bind(queueId).run();
        
        Logger.error(`队列记录 ${queueId} 标记为失败: ${errorMessage || 'Unknown error'}`);
    } catch (error) {
        Logger.error(`标记队列记录为失败时出错:`, error);
    }
}

/**
 * 清理旧的队列记录
 * @param {Object} env 环境变量
 */
export async function cleanupOldQueueRecords(env) {
    try {
        const oneDayAgo = Math.floor(Date.now() / 1000) - 86400; // 24小时前
        
        const result = await env.DB.prepare(
            `DELETE FROM tg_forward_media_queue WHERE created_at < ?`
        ).bind(oneDayAgo).run();
        
        if (result.changes > 0) {
            Logger.debug(`清理了 ${result.changes} 条旧的队列记录`);
        }
    } catch (error) {
        Logger.error(`清理旧队列记录时出错:`, error);
    }
}

/**
 * 处理所有待处理的队列记录
 * @param {Object} env 环境变量
 */
export async function processAllPendingQueues(env) {
    try {
        const now = Math.floor(Date.now() / 1000);
        
        // 获取所有应该处理的记录
        const pendingQueues = await env.DB.prepare(
            `SELECT id, media_group_id, chat_id FROM tg_forward_media_queue 
             WHERE status = 'pending' AND process_after <= ?`
        ).bind(now).all();
        
        if (!pendingQueues.results || pendingQueues.results.length === 0) {
            return;
        }
        
        Logger.debug(`发现 ${pendingQueues.results.length} 条待处理的队列记录`);
        
        // 处理每条记录
        for (const queue of pendingQueues.results) {
            // 检查必要的字段是否存在
            if (!queue.media_group_id || !queue.chat_id) {
                Logger.error(`队列记录 ${queue.id} 缺少必要字段`);
                continue;
            }
            
            // 创建一个模拟的上下文对象
            const mockCtx = {
                env,
                message: {
                    media_group_id: queue.media_group_id,
                    chat: { id: queue.chat_id }
                }
            };
            
            try {
                await processMediaGroupQueue(mockCtx, queue.media_group_id, queue.chat_id);
            } catch (error) {
                Logger.error(`处理队列记录 ${queue.id} 时出错:`, error);
            }
        }
    } catch (error) {
        Logger.error(`处理待处理队列时出错:`, error);
    }
}