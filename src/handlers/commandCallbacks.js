/**
 * 命令回调处理模块
 * 处理/0投稿命令相关的callback_query事件
 */

import { sendTelegramRequest } from '../utils/telegramApi.js';
import { DefaultGroupsBind, BOT_ADMINS } from '../config/constants.js';
import Logger from '../utils/logger.js';
import { createCaptionWithSource } from '../utils/mediaGroupUtils.js';
import { isBilifeedbotContent, simplifyBilifeedbotContent, hasCustomEmoji } from '../utils/stringUtils.js';
import { processGradeText, getGradeConfig } from '../utils/gradeUtils.js';
import { getMediaGroupMessages, sendMediaGroupToChannel } from '../utils/mediaGroupUtils.js';
import { checkPermission } from './permissions.js';

/**
 * 处理命令相关的回调查询
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<boolean>} 是否处理了该回调
 */
export async function handleCommandCallbacks(callbackQuery, env, ctx) {
	const { id, data, message } = callbackQuery;
	
	// 检查是否是命令相关的回调
	if (!data.startsWith('cmd')) {
		return false;
	}

	Logger.debug('处理命令回调:', data);

	// 权限验证：构造伪消息对象用于权限检查
	try {
		const fakeMessage = {
			from: callbackQuery.from,
			chat: callbackQuery.message.chat
		};
		const permissionResult = await checkPermission({ message: fakeMessage, env });

		if (!permissionResult.hasPermission) {
			Logger.warn('/0命令回调权限检测失败', {
				userId: callbackQuery.from.id,
				userName: callbackQuery.from.first_name,
				chatId: callbackQuery.message.chat.id,
				reason: permissionResult.reason,
				callbackData: data
			});

			// 权限不足时回复错误消息
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: '👅 仅限管理员操作哦',
				show_alert: true // 使用alert显示重要的权限错误
			});

			return true; // 表示已处理该回调
		}

		Logger.success('/0命令回调权限验证通过', {
			userId: callbackQuery.from.id,
			userName: callbackQuery.from.first_name,
			chatId: callbackQuery.message.chat.id,
			permissionSource: permissionResult.reason,
			callbackData: data
		});
	} catch (error) {
		Logger.error('回调权限检查时出错:', error);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '❌ 权限验证失败',
			show_alert: false
		});
		return true;
	}

	// 权限验证通过，处理具体的回调操作
	try {
		if (data.startsWith('cmd_submit:')) {
			await handleCommandSubmit(callbackQuery, env);
		} else if (data.startsWith('cmd:')) {
			await handleCommandOption(callbackQuery, env);
		} else if (data.startsWith('cmd_cancel:')) {
			await handleCommandCancel(callbackQuery, env);
		}
		
		return true;
	} catch (error) {
		Logger.error('处理命令回调时出错:', error);
		
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '处理请求时出错，请稍后再试',
			show_alert: false
		});
		
		return true;
	}
}

/**
 * 处理命令投稿
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
async function handleCommandSubmit(callbackQuery, env) {
	const { id, data, message } = callbackQuery;
	const parts = data.split(':');
	
	if (parts.length !== 4) {
		Logger.warn('投稿数据格式错误:', data);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '数据格式错误',
			show_alert: false
		});
		return;
	}

	const sourceGroupId = '-100' + parts[1]; // 源群组ID
	const originalMessageId = parseInt(parts[2]); // 原始消息ID
	const targetChannelId = '-100' + parts[3]; // 目标频道ID

	// 获取源消息
	const replyMessage = message.reply_to_message;
	if (!replyMessage) {
		Logger.error('未找到回复的源消息');
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '未找到源消息',
			show_alert: false
		});
		return;
	}

	try {
		// 读取当前按钮状态，获取选中的分级选项
		const selectedOptions = getCurrentSelectedOption(message.reply_markup);
		Logger.debug('检测到的选中选项:', selectedOptions);
		
		const gradeOption = selectedOptions.gradeOption;
		const removeCaption = selectedOptions.removeCaption;
		
		Logger.debug('解析后的选项:', { gradeOption, removeCaption });
		
		let result = null;
		
		// 检查是否是纯文本消息
		const isPureText = !replyMessage.media_group_id && !replyMessage.photo && !replyMessage.video && !replyMessage.document && !replyMessage.audio && !replyMessage.voice && !replyMessage.sticker;
		
		if (isPureText) {
			Logger.debug('处理纯文本投稿');

			// 检测是否包含自定义表情（大会员表情）
			const hasCustomEmojiInMessage = hasCustomEmoji(replyMessage);
			
			if (hasCustomEmojiInMessage) {
				Logger.debug('检测到自定义表情，使用带来源转发');
				
				// 对于包含自定义表情的纯文本消息，使用带来源转发以保持表情正常显示
				result = await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`, {
					chat_id: targetChannelId,
					from_chat_id: sourceGroupId,
					message_id: originalMessageId
				});
			} else {
				Logger.debug('无自定义表情，使用重新编辑模式');
				
				// 统一via组装逻辑
				const { caption: viaText, entities: viaEntities } = createCaptionWithSource(replyMessage, '', gradeOption, targetChannelId);

				const { text, entities } = processGradeText({
					originalText: replyMessage.text || '',
					originalEntities: replyMessage.entities || [],
					grade: gradeOption,
					viaText,
					viaEntities
				});

				result = await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
					chat_id: targetChannelId,
					text: text,
					entities: entities
				});
			}
		}
		// 检查是否是媒体组
		else if (replyMessage.media_group_id) {
			Logger.debug('处理媒体组投稿，media_group_id:', replyMessage.media_group_id);
			
			// 从 tg_log_media_group 中获取完整的媒体组数据
			const mediaGroupRecords = await getMediaGroupMessages(env, replyMessage.media_group_id);
			Logger.debug('找到媒体组记录数量:', mediaGroupRecords.length);
			
			if (mediaGroupRecords.length > 0) {
				// 构建媒体组消息，解析JSON并应用分级处理
				const mediaMessages = mediaGroupRecords.map(record => {
					try {
						const originalMessage = typeof record.raw_json === 'string' 
							? JSON.parse(record.raw_json) 
							: record.raw_json;
						
						// 如果这是当前回复的消息，应用选中的分级处理
						if (originalMessage.message_id === replyMessage.message_id) {
							Logger.debug('对目标消息应用分级处理:', gradeOption);
							return applyGradeToMessage(originalMessage, gradeOption, removeCaption, targetChannelId);
						}
						
						// 其他消息：如果选中了需要遮罩的分级，也给它们加上遮罩
						const gradeConfig = getGradeConfig(gradeOption);
						if (gradeConfig.needsSpoiler) {
							const processedMsg = { ...originalMessage };
							processedMsg.has_media_spoiler = true;
							Logger.debug('给媒体组其他消息添加遮罩:', originalMessage.message_id);
							return processedMsg;
						}
						
						return originalMessage;
					} catch (e) {
						Logger.error('解析媒体组消息JSON失败:', e);
						return null;
					}
				}).filter(msg => msg !== null);
				
				Logger.debug('构建的媒体消息数量:', mediaMessages.length);
				
				if (mediaMessages.length > 0) {
					// 使用媒体组发送到频道
					result = await sendMediaGroupToChannel(env, targetChannelId, mediaMessages);
				} else {
					Logger.warn('无法构建媒体组消息，降级为单条消息处理');
					const processedMessage = applyGradeToMessage(replyMessage, gradeOption, removeCaption, targetChannelId);
					result = await sendToChannel(env, targetChannelId, processedMessage);
				}
			} else {
				Logger.warn('未找到媒体组记录，降级为单条消息处理');
				const processedMessage = applyGradeToMessage(replyMessage, gradeOption, removeCaption, targetChannelId);
				result = await sendToChannel(env, targetChannelId, processedMessage);
			}
		} else {
			// 单个消息：直接处理
			Logger.debug('处理单个消息投稿');
			const processedMessage = applyGradeToMessage(replyMessage, gradeOption, removeCaption, targetChannelId);
			result = await sendToChannel(env, targetChannelId, processedMessage);
		}
		
		if (result && result.ok) {
			// 投稿成功
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: '✅ 投稿成功！',
				show_alert: false
			});

			// 给原始消息添加反应
			try {
				await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/setMessageReaction`, {
					chat_id: sourceGroupId,
					message_id: originalMessageId,
					reaction: [{ type: 'emoji', emoji: '🎉' }],
					is_big: false
				});
			} catch (error) {
				Logger.debug('添加反应失败:', error);
			}

			// 检测并删除投稿群内对应的投稿消息和数据
			try {
				const { deleteSubmissionData } = await import('../utils/submissionDeletion.js');
				await deleteSubmissionData(env, originalMessageId, sourceGroupId, {
					deleteMessages: true,
					deleteDatabase: true,
					forceCleanup: true // 投稿成功后强制清理，即使消息删除失败
				});
			} catch (error) {
				Logger.error('删除投稿群内投稿失败:', error);
			}

			// 删除处理按钮消息
			await deleteCommandMessage(env, message);
		} else {
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
				callback_query_id: id,
				text: `❌ 投稿失败: ${result?.description || '未知错误'}`,
				show_alert: false
			});
		}
	} catch (error) {
		Logger.error('投稿时出错:', error);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '❌ 投稿失败',
			show_alert: false
		});
	}
}

/**
 * 处理分级选项
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
async function handleCommandOption(callbackQuery, env) {
	const { id, data, message } = callbackQuery;
	const parts = data.split(':');
	
	if (parts.length !== 3) {
		Logger.warn('选项数据格式错误:', data);
		return;
	}

	const option = parts[1]; // 选项类型
	const messageId = parseInt(parts[2]); // 消息ID

	const replyMessage = message.reply_to_message;
	if (!replyMessage) {
		Logger.error('未找到回复的源消息');
		return;
	}

	try {
		// 获取原文说明文字用于显示
		const originalCaption = replyMessage.caption && replyMessage.caption.trim() ? replyMessage.caption.trim() : '';
		
		// 更新按钮状态后，重新获取当前选中状态
		const updatedMarkup = updateButtonStates(message.reply_markup, option);
		const currentState = getCurrentSelectedOption(updatedMarkup);
		
		let newText = '📝 投稿处理';
		
		// 构建状态显示文本
		const statusParts = [];
		
		// 添加分级状态
		if (currentState.gradeOption) {
			const gradeDisplayName = getOptionDisplayName(currentState.gradeOption);
			statusParts.push(gradeDisplayName);
		}
		
		// 添加去除说明状态
		if (currentState.removeCaption) {
			statusParts.push('去除说明');
		}
		
		// 如果有状态，添加到标题
		if (statusParts.length > 0) {
			newText += ' [' + statusParts.join(' + ') + ']';
		}
		
		// 显示原始内容（如果没有选择去除说明）
		if (originalCaption && !currentState.removeCaption) {
			newText += '\n' + originalCaption;
		}

		// 更新按钮消息文本
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/editMessageText`, {
			chat_id: message.chat.id,
			message_id: message.message_id,
			text: newText,
			reply_markup: updatedMarkup
		});
		
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: `✅ 已应用: ${getOptionDisplayName(option)}`,
			show_alert: false
		});

	} catch (error) {
		Logger.error('处理选项时出错:', error);
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '❌ 处理失败',
			show_alert: false
		});
	}
}

/**
 * 处理取消操作
 * @param {Object} callbackQuery 回调查询对象
 * @param {Object} env 环境变量
 */
async function handleCommandCancel(callbackQuery, env) {
	const { id, message } = callbackQuery;

	try {
		// 删除按钮消息
		await deleteCommandMessage(env, message);
		
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
			callback_query_id: id,
			text: '已取消投稿处理',
			show_alert: false
		});
	} catch (error) {
		Logger.error('处理取消时出错:', error);
	}
}

/**
 * 发送消息到频道
 * @param {Object} env 环境变量
 * @param {string} channelId 频道ID
 * @param {Object} processedMessage 已处理的消息（包含分级处理后的说明文字）
 * @returns {Promise<Object>} API响应
 */
async function sendToChannel(env, channelId, processedMessage) {
	// 使用已处理的消息内容，不再重新生成说明文字
	const caption = processedMessage.caption || '';
	const captionEntities = processedMessage.caption_entities || [];
	const hasSpoiler = processedMessage.has_media_spoiler || false;
	
	if (processedMessage.photo) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendPhoto`, {
			chat_id: channelId,
			photo: processedMessage.photo[processedMessage.photo.length - 1].file_id,
			caption: caption,
			caption_entities: captionEntities,
			has_spoiler: hasSpoiler
		});
	} else if (processedMessage.video) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendVideo`, {
			chat_id: channelId,
			video: processedMessage.video.file_id,
			caption: caption,
			caption_entities: captionEntities,
			has_spoiler: hasSpoiler
		});
	} else if (processedMessage.animation) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAnimation`, {
			chat_id: channelId,
			animation: processedMessage.animation.file_id,
			caption: caption,
			caption_entities: captionEntities,
			has_spoiler: hasSpoiler
		});
	} else if (processedMessage.document) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendDocument`, {
			chat_id: channelId,
			document: processedMessage.document.file_id,
			caption: caption,
			caption_entities: captionEntities
		});
	} else if (processedMessage.audio) {
		return await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendAudio`, {
			chat_id: channelId,
			audio: processedMessage.audio.file_id,
			caption: caption,
			caption_entities: captionEntities
		});
	}
	
	return { ok: false, description: '不支持的媒体类型' };
}

/**
 * 更新按钮状态（添加/移除✅标记）
 * @param {Object} replyMarkup 原始按钮配置
 * @param {string} selectedOption 选中的选项
 * @returns {Object} 更新后的按钮配置
 */
function updateButtonStates(replyMarkup, selectedOption) {
	if (!replyMarkup?.inline_keyboard) {
		return replyMarkup;
	}

	const updatedKeyboard = replyMarkup.inline_keyboard.map(row => {
		return row.map(button => {
			if (button.callback_data && button.callback_data.startsWith('cmd:')) {
				const buttonOption = button.callback_data.split(':')[1];
				
				// 移除已有的✅标记
				let cleanText = button.text.replace('✅', '').trim();
				
				// 特殊处理【去除说明】按钮：独立切换状态，不受分级按钮影响
				if (buttonOption === 'removeCaption') {
					if (selectedOption === 'removeCaption') {
						// 如果当前点击的是去除说明按钮，切换其状态
						if (button.text.startsWith('✅')) {
							// 如果已经选中，则取消选中
							cleanText = cleanText;
						} else {
							// 如果未选中，则选中
							cleanText = '✅' + cleanText;
						}
					} else {
						// 如果点击的是其他按钮，保持去除说明按钮的当前状态
						if (button.text.startsWith('✅')) {
							cleanText = '✅' + cleanText;
						}
					}
				} else {
					// 分级按钮逻辑：只有点击分级按钮时才更新分级按钮状态
					if (selectedOption !== 'removeCaption') {
						// 清除所有分级按钮的选中状态
						if (buttonOption === selectedOption) {
							// 选中当前点击的分级按钮
							cleanText = '✅' + cleanText;
						}
						// 其他分级按钮保持未选中状态（cleanText已经移除了✅）
					} else {
						// 如果点击的是去除说明按钮，保持分级按钮的当前状态
						if (button.text.startsWith('✅')) {
							cleanText = '✅' + cleanText;
						}
					}
				}
				
				return {
					...button,
					text: cleanText
				};
			}
			return button;
		});
	});

	return {
		inline_keyboard: updatedKeyboard
	};
}

/**
 * 获取选项的显示名称
 * @param {string} option 选项代码
 * @returns {string} 显示名称
 */
function getOptionDisplayName(option) {
	if (option === 'removeCaption') {
		return '去除说明';
	}
	const config = getGradeConfig(option);
	return config.displayName;
}

/**
 * 获取当前选中的选项
 * @param {Object} replyMarkup 按钮配置
 * @returns {Object} 选中的选项信息 {gradeOption: string|null, removeCaption: boolean}
 */
function getCurrentSelectedOption(replyMarkup) {
	if (!replyMarkup?.inline_keyboard) {
		return { gradeOption: null, removeCaption: false };
	}
	
	let gradeOption = null;
	let removeCaption = false;
	
	// 查找带有✅标记的按钮
	for (const row of replyMarkup.inline_keyboard) {
		for (const button of row) {
			if (button.callback_data && button.callback_data.startsWith('cmd:') && button.text.startsWith('✅')) {
				const option = button.callback_data.split(':')[1];
				if (option === 'removeCaption') {
					removeCaption = true;
				} else {
					gradeOption = option;
				}
			}
		}
	}
	
	// 总是返回对象格式，确保一致性
	return { gradeOption, removeCaption };
}

/**
 * 根据选项对消息应用分级处理
 * @param {Object} message 原始消息
 * @param {string} gradeOption 分级选项类型
 * @param {boolean} removeCaption 是否去除说明文字
 * @param {string} targetChannelId 目标频道ID（可选）
 * @returns {Object} 处理后的消息
 */
function applyGradeToMessage(message, gradeOption, removeCaption = false, targetChannelId = null) {
	let processedMessage = { ...message };
	
	// 确定原始文本：如果选择了去除说明，则使用空字符串
	let originalText = removeCaption ? '' : (message.caption || '');
	let originalEntities = removeCaption ? [] : (message.caption_entities || []);
	
	// 检测是否是bilifeedbot转换的视频并精简
	if (!removeCaption && originalText && isBilifeedbotContent(originalText)) {
		Logger.debug('在applyGradeToMessage中检测到bilifeedbot转换视频，精简说明文字');
		const simplified = simplifyBilifeedbotContent(originalText, originalEntities);
		originalText = simplified.caption;
		originalEntities = simplified.entities;
	}
	
	// 去除#投稿标签
	if (!removeCaption && originalText && originalText.includes('#投稿')) {
		Logger.debug('去除#投稿标签');
		originalText = originalText.replace('#投稿', '').trim();
		
		// 处理实体偏移量调整
		if (originalEntities && originalEntities.length > 0) {
			const originalCaption = message.caption || '';
			const submissionTagIndex = originalCaption.indexOf('#投稿');
			
			if (submissionTagIndex >= 0) {
				originalEntities = originalEntities
					.map((entity) => {
						// 跳过可能的#投稿标签实体
						if (entity.offset <= submissionTagIndex &&
							entity.offset + entity.length >= submissionTagIndex + 3) {
							return null;
						}
						
						// 调整偏移量（减去#投稿标签的长度）
						return {
							...entity,
							offset: Math.max(0, entity.offset - (submissionTagIndex < entity.offset ? 3 : 0))
						};
					})
					.filter((entity) => entity !== null);
			}
		}
	}
	
	// 检查是否是机器人管理员
	const isAdmin = message.from && BOT_ADMINS.includes(message.from.id);
	
	// 对于管理员：直接使用分级处理，不添加via信息
	// 对于普通用户：获取via信息并添加
	let viaText = '';
	let viaEntities = [];
	
	if (!isAdmin) {
		const viaOnlyCaptionData = createCaptionWithSource(message, '', 'normal', targetChannelId);
		viaText = viaOnlyCaptionData.caption || '';
		viaEntities = viaOnlyCaptionData.entities || [];
	}
	
	// 使用通用分级处理函数
	const result = processGradeText({
		originalText: originalText,
		originalEntities: originalEntities,
		grade: gradeOption,
		viaText: viaText,
		viaEntities: viaEntities
	});
	
	// 应用处理结果
	processedMessage.caption = result.text;
	processedMessage.caption_entities = result.entities;
			
	// 应用遮罩设置
	if (result.needsSpoiler) {
		processedMessage.has_media_spoiler = true;
	}
	
	return processedMessage;
}

/**
 * 删除命令消息
 * @param {Object} env 环境变量
 * @param {Object} message 消息对象
 */
async function deleteCommandMessage(env, message) {
	try {
		// 判断删除策略
		const TARGET_GROUP_ID = '-1002599022189'; // 投稿处理群
		const chatId = message.chat.id.toString();
		
		if (chatId === TARGET_GROUP_ID && message.reply_to_message && message.from?.is_bot) {
			// 在投稿处理群内，且回复的是机器人消息，删除回复目标和按钮消息
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
				chat_id: chatId,
				message_id: message.reply_to_message.message_id
			});
		}
		
		// 删除按钮消息本身
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
			chat_id: chatId,
			message_id: message.message_id
		});
	} catch (error) {
		Logger.debug('删除消息失败:', error);
	}
} 

