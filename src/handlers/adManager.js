/**
 * 广告投放管理器
 * 负责处理广告规则的创建、管理和定时发送
 */

import Logger from '../utils/logger.js';
import { sendTelegramRequest } from '../utils/telegramApi.js';

//#region 广告投放管理核心功能
export class AdManager {
  
  //#region 创建广告规则
  static async createCampaign(db, campaignData) {
    Logger.tagLog('AD_MANAGER', '创建广告规则', campaignData);
    
    try {
      const {
        name,
        target_channel_id,
        source_channel_id = '-1001549390517',
        source_message_id,
        start_date,
        end_date,
        frequency_days,
        publish_time,
        is_pin = false
      } = campaignData;
      
      const result = await db.prepare(`
        INSERT INTO ad_campaigns (
          name, target_channel_id, source_channel_id, source_message_id,
          start_date, end_date, frequency_days, publish_time, is_pin
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        name, target_channel_id, source_channel_id, source_message_id,
        start_date, end_date, frequency_days, publish_time, is_pin
      ).run();
      
      Logger.success('广告规则创建成功', { campaignId: result.meta.last_row_id });
      return { success: true, campaignId: result.meta.last_row_id };
      
    } catch (error) {
      Logger.error('创建广告规则失败', error);
      throw error;
    }
  }
  //#endregion
  
  //#region 检查并执行定时任务
  static async checkAndExecuteScheduledAds(db) {
    Logger.tagLog('AD_CRON', '开始检查定时广告任务');
    
    try {
      // 获取所有启用的广告规则
      const activeCampaigns = await db.prepare(`
        SELECT * FROM ad_campaigns 
        WHERE is_active = 1 
        AND start_date <= date('now') 
        AND end_date >= date('now')
      `).all();

      Logger.info(`找到 ${(activeCampaigns.results || []).length} 个活跃的广告规则`);

      for (const campaign of activeCampaigns.results || []) {
        if (await this.shouldSendAd(db, campaign)) {
          await this.executeAdPost(db, campaign);
        }
      }
      
      Logger.success('定时广告任务检查完成');
      
    } catch (error) {
      Logger.error('定时广告任务执行失败', error);
      // 不抛出异常，避免影响其他功能
    }
  }
  //#endregion

  //#region 判断是否应该发送广告
  static async shouldSendAd(db, campaign) {
    try {
      // 获取当前UTC时间（在Cloudflare Workers环境中，new Date()返回的就是UTC时间）
      const nowUTC = new Date();
      
      // 将当前UTC时间转换为北京时间（UTC + 8小时）
      const beijingOffset = 8 * 60 * 60 * 1000; // 8小时的毫秒数
      const nowBeijing = new Date(nowUTC.getTime() + beijingOffset);
      const currentHourBeijing = nowBeijing.getHours();
      const currentMinuteBeijing = nowBeijing.getMinutes();
      
      // 数据库中的publish_time存储的是北京时间，直接解析
      const [targetHour, targetMinute] = campaign.publish_time.split(':').map(Number);
      
      Logger.debug(`时间检查 - 规则${campaign.id}`, {
        currentBeijing: `${currentHourBeijing.toString().padStart(2, '0')}:${currentMinuteBeijing.toString().padStart(2, '0')}`,
        targetBeijing: campaign.publish_time,
        currentUTC: `${nowUTC.getHours().toString().padStart(2, '0')}:${nowUTC.getMinutes().toString().padStart(2, '0')}`
      });
      
      // 简化时间检查：用北京时间直接比较
      const currentTimeMinutes = currentHourBeijing * 60 + currentMinuteBeijing;
      const targetTimeMinutes = targetHour * 60 + targetMinute;
      
      // 检查当前北京时间是否已经到了设定的发送时间
      const hasReachedTargetTime = currentTimeMinutes >= targetTimeMinutes;
      
      if (!hasReachedTargetTime) {
        Logger.debug(`规则 ${campaign.id} 尚未到达发送时间，当前北京时间: ${currentHourBeijing.toString().padStart(2, '0')}:${currentMinuteBeijing.toString().padStart(2, '0')}，目标北京时间: ${campaign.publish_time}`);
        return false;
      }
      
      Logger.debug(`规则 ${campaign.id} 已到达发送时间`, {
        currentBeijing: `${currentHourBeijing.toString().padStart(2, '0')}:${currentMinuteBeijing.toString().padStart(2, '0')}`,
        targetBeijing: campaign.publish_time,
        readyToSend: hasReachedTargetTime
      });
      
      // 查找该规则的最后一次发送记录（简化匹配逻辑，只用campaign_id）
      const lastPost = await db.prepare(`
        SELECT sent_at FROM ad_posts 
        WHERE campaign_id = ?
        ORDER BY sent_at DESC 
        LIMIT 1
      `).bind(campaign.id).first();
      
      if (!lastPost) {
        Logger.info(`广告规则 ${campaign.id} 从未发送过，可以发送`);
        return true;
      }
      
      // 计算距离上次发送的天数（复用已有的北京时间偏移量）
      const beijingNow = new Date(nowUTC.getTime() + beijingOffset);
      const lastSentDate = new Date(lastPost.sent_at);
      const beijingLastSent = new Date(lastSentDate.getTime() + beijingOffset);
      
      // 基于北京时间的日期计算天数差
      const beijingToday = new Date(beijingNow.getFullYear(), beijingNow.getMonth(), beijingNow.getDate());
      const beijingLastSentDay = new Date(beijingLastSent.getFullYear(), beijingLastSent.getMonth(), beijingLastSent.getDate());
      const daysDiff = Math.floor((beijingToday - beijingLastSentDay) / (1000 * 60 * 60 * 24));
      
      // 根据frequency_days判断是否到了发送时间
      const shouldSend = daysDiff > campaign.frequency_days;
      
      Logger.debug(`广告规则 ${campaign.id} 频率检查结果`, {
        lastSent: lastPost.sent_at,
        daysDiff,
        frequencyDays: campaign.frequency_days,
        shouldSend,
        publishTime: campaign.publish_time,
        currentBeijing: `${currentHourBeijing}:${currentMinuteBeijing}`
      });
      
      return shouldSend;
      
    } catch (error) {
      Logger.error(`判断广告发送时机失败 (规则ID: ${campaign.id})`, error);
      return false;
    }
  }
  //#endregion

  //#region 执行广告发送
  static async executeAdPost(db, campaign) {
    Logger.tagLog('AD_SEND', '开始发送广告', { 
      campaignId: campaign.id, 
      name: campaign.name,
      targetChannel: campaign.target_channel_id 
    });
    
    try {
      // 1. 转发消息到目标频道
      const sentMessage = await this.forwardMessage(
        campaign.source_channel_id,
        campaign.source_message_id,
        campaign.target_channel_id
      );
      
      if (!sentMessage || !sentMessage.message_id) {
        throw new Error('消息转发失败，未获得有效的消息ID');
      }
      
      // 2. 删除该规则在该频道的旧广告
      await this.deleteOldAd(db, campaign);
      
      // 3. 记录新的发送记录（包含publish_time字段）
      await db.prepare(`
        INSERT INTO ad_posts (campaign_id, target_channel_id, source_message_id, sent_message_id, sent_at, publish_time)
        VALUES (?, ?, ?, ?, datetime('now'), ?)
      `).bind(
        campaign.id,
        campaign.target_channel_id,
        campaign.source_message_id,
        sentMessage.message_id,
        campaign.publish_time
      ).run();
      
      // 4. 置顶处理
      if (campaign.is_pin) {
        await this.pinMessage(campaign.target_channel_id, sentMessage.message_id);
      }
      
      Logger.success('广告发送成功', { 
        campaignId: campaign.id,
        messageId: sentMessage.message_id 
      });
      
    } catch (error) {
      Logger.error(`广告发送失败 (规则ID: ${campaign.id})`, error);
      // 不抛出异常，避免影响其他广告的发送
    }
  }
  //#endregion

  //#region 删除旧广告
  static async deleteOldAd(db, campaign) {
    try {
      // 查找该规则最新的未删除广告记录（简化匹配逻辑，只用campaign_id）
      const lastPost = await db.prepare(`
        SELECT * FROM ad_posts 
        WHERE campaign_id = ? AND is_deleted = 0
        ORDER BY sent_at DESC 
        LIMIT 1
      `).bind(campaign.id).first();
      
      if (lastPost) {
        try {
          // 删除Telegram中的消息
          await this.deleteMessage(lastPost.target_channel_id, lastPost.sent_message_id);
          
          // 标记数据库记录为已删除
          await db.prepare(`
            UPDATE ad_posts 
            SET is_deleted = 1, deleted_at = datetime('now')
            WHERE id = ?
          `).bind(lastPost.id).run();
          
          Logger.info(`删除旧广告成功`, { 
            campaignId: campaign.id,
            postId: lastPost.id,
            messageId: lastPost.sent_message_id,
            targetChannel: lastPost.target_channel_id
          });
          
        } catch (deleteError) {
          Logger.warn(`删除旧广告失败，可能消息已不存在`, { 
            campaignId: campaign.id,
            postId: lastPost.id,
            error: deleteError.message 
          });
          
          // 即使删除失败也标记为已删除，避免重复尝试
          await db.prepare(`
            UPDATE ad_posts 
            SET is_deleted = 1, deleted_at = datetime('now')
            WHERE id = ?
          `).bind(lastPost.id).run();
        }
      } else {
        Logger.debug(`规则 ${campaign.id} 没有找到需要删除的旧广告`);
      }
      
    } catch (error) {
      Logger.error(`查询或删除旧广告时出错 (规则ID: ${campaign.id})`, error);
    }
  }
  //#endregion

  //#region Telegram API 辅助方法
  
  // 转发消息
  static async forwardMessage(fromChatId, messageId, toChatId) {
    const env = globalThis.env || {}; // 临时解决方案，实际使用时需要传入env
    
    const response = await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/forwardMessage`,
      {
        chat_id: toChatId,
        from_chat_id: fromChatId,
        message_id: messageId
      }
    );
    
    if (!response.ok) {
      throw new Error(`转发消息失败: ${response.description}`);
    }
    
    return response.result;
  }
  
  // 删除消息
  static async deleteMessage(chatId, messageId) {
    const env = globalThis.env || {};
    
    const response = await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`,
      {
        chat_id: chatId,
        message_id: messageId
      }
    );
    
    if (!response.ok) {
      throw new Error(`删除消息失败: ${response.description}`);
    }
    
    return response.result;
  }
  
  // 置顶消息
  static async pinMessage(chatId, messageId) {
    const env = globalThis.env || {};
    
    const response = await sendTelegramRequest(
      env,
      `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/pinChatMessage`,
      {
        chat_id: chatId,
        message_id: messageId,
        disable_notification: false
      }
    );
    
    if (!response.ok) {
      throw new Error(`置顶消息失败: ${response.description}`);
    }
    
    return response.result;
  }
  
  //#endregion

  //#region 广告规则管理方法
  
  // 获取广告规则列表
  static async getCampaigns(db, filters = {}) {
    let query = 'SELECT * FROM ad_campaigns';
    const params = [];
    const conditions = [];
    
    if (filters.campaignId !== undefined) {
      conditions.push('id = ?');
      params.push(filters.campaignId);
    }
    
    if (filters.isActive !== undefined) {
      conditions.push('is_active = ?');
      params.push(filters.isActive);
    }
    
    if (filters.targetChannelId) {
      conditions.push('target_channel_id = ?');
      params.push(filters.targetChannelId);
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }
    
    query += ' ORDER BY created_at DESC';
    
    const result = await db.prepare(query).bind(...params).all();
    return result.results || [];
  }
  
  // 更新广告规则
  static async updateCampaign(db, campaignId, updateData) {
    const fields = Object.keys(updateData);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = Object.values(updateData);
    
    await db.prepare(`
      UPDATE ad_campaigns 
      SET ${setClause}, updated_at = datetime('now')
      WHERE id = ?
    `).bind(...values, campaignId).run();
    
    Logger.info('广告规则更新成功', { campaignId, updateData });
  }
  
  // 删除广告规则
  static async deleteCampaign(db, campaignId) {
    // 由于设置了 CASCADE 删除，删除广告规则会自动删除相关发送记录
    await db.prepare('DELETE FROM ad_campaigns WHERE id = ?').bind(campaignId).run();
    
    Logger.info('广告规则删除成功（相关发送记录已自动级联删除）', { campaignId });
  }
  
  // 获取发送记录
  static async getAdPosts(db, filters = {}) {
    let query = `
      SELECT ap.*, ac.name as campaign_name 
      FROM ad_posts ap 
      JOIN ad_campaigns ac ON ap.campaign_id = ac.id
    `;
    const params = [];
    const conditions = [];
    
    if (filters.campaignId) {
      conditions.push('ap.campaign_id = ?');
      params.push(filters.campaignId);
    }
    
    if (filters.targetChannelId) {
      conditions.push('ap.target_channel_id = ?');
      params.push(filters.targetChannelId);
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }
    
    query += ' ORDER BY ap.sent_at DESC';
    
    const result = await db.prepare(query).bind(...params).all();
    return result.results || [];
  }
  
  // 获取广告规则表的完整数据（用于调试和查看）
  static async getAdCampaignsRaw(db) {
    try {
      const result = await db.prepare('SELECT * FROM ad_campaigns ORDER BY created_at DESC').all();
      return result.results || [];
    } catch (error) {
      Logger.error('获取广告规则表数据失败', error);
      throw error;
    }
  }
  
  // 获取广告发送记录表的完整数据（用于调试和查看）
  static async getAdPostsRaw(db) {
    try {
      const result = await db.prepare('SELECT * FROM ad_posts ORDER BY sent_at DESC').all();
      return result.results || [];
    } catch (error) {
      Logger.error('获取广告发送记录表数据失败', error);
      throw error;
    }
  }
  
  // 获取广告统计数据
  static async getAdStats(db) {
    try {
      // 获取总发送次数
      const totalPostsResult = await db.prepare('SELECT COUNT(*) as count FROM ad_posts').first();
      const totalPosts = totalPostsResult?.count || 0;
      
      // 获取今日发送次数
      const todayPostsResult = await db.prepare(`
        SELECT COUNT(*) as count FROM ad_posts 
        WHERE DATE(sent_at) = DATE('now')
      `).first();
      const todayPosts = todayPostsResult?.count || 0;
      
      // 获取活跃规则数量
      const activeCampaignsResult = await db.prepare(`
        SELECT COUNT(*) as count FROM ad_campaigns 
        WHERE is_active = 1
      `).first();
      const activeCampaigns = activeCampaignsResult?.count || 0;
      
      return {
        totalPosts,
        todayPosts,
        activeCampaigns
      };
    } catch (error) {
      Logger.error('获取广告统计数据失败', error);
      throw error;
    }
  }
  
  //#endregion
}
//#endregion