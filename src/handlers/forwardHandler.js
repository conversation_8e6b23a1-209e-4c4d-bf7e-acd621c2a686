/**
 * 消息转发处理模块
 * 独立处理消息转发到投稿群的功能
 */

import { sendTelegramRequest } from '../utils/telegramApi.js';
import { DefaultGroupsBind, TARGET_GROUP_ID, TOPIC_MAPPING } from '../config/constants.js';
import Logger from '../utils/logger.js';
import { safeString, safeCaptionText } from '../utils/stringUtils.js';

/**
 * 检查消息是否包含媒体内容
 * @param {Object} message Telegram消息对象
 * @returns {Boolean} 是否包含媒体
 */
function hasMedia(message) {
	return !!(
		message.photo ||
		message.video ||
		message.document ||
		message.audio ||
		message.voice ||
		message.video_note ||
		message.animation
	);
}

/**
 * 检查消息是否包含#投稿标签
 * @param {Object} message Telegram消息对象
 * @returns {Boolean} 是否包含#投稿标签
 */
function hasSubmissionTag(message) {
	// 检查消息文本中的#投稿标签
	if (message.caption && message.caption.includes('#投稿')) {
		return true;
	}
	
	// 检查文本消息中的#投稿标签
	if (message.text && message.text.includes('#投稿')) {
		return true;
	}

	return false;
}

/**
 * 获取应该转发到的话题ID
 * @param {Object} message Telegram消息对象
 * @param {String} chatId 来源群组ID
 * @param {Boolean} forceWithTag 强制使用带标签的话题（用于#投稿回复）
 * @returns {String|null} 话题ID或null（如果不应该转发）
 */
function getTargetTopicId(message, chatId, forceWithTag = false) {
	// 检查群组是否在配置中
	if (!TOPIC_MAPPING[chatId]) {
		return null;
	}

	// 沙雕英雄群特殊处理
	if (chatId === '-1001143091022') {
		// 如果强制使用带标签话题（#投稿回复情况）
		if (forceWithTag) {
			return TOPIC_MAPPING[chatId].withTag;
		}
		
		// 检查是否有#投稿标签
		if (hasSubmissionTag(message)) {
			return TOPIC_MAPPING[chatId].withTag;
		}

		// 检查是否有媒体内容，转发到默认话题
		if (hasMedia(message)) {
			return TOPIC_MAPPING[chatId].default;
		}

		return null;
	}

	// 其他群组只要有媒体内容就转发到指定话题
	if (hasMedia(message)) {
		return TOPIC_MAPPING[chatId];
	}

	return null;
}

/**
 * 记录转发消息到新的数据库表
 * @param {Object} env 环境变量
 * @param {Object} originalMessage 原始消息
 * @param {Object} forwardedMessage 转发的消息
 * @param {String} topicId 目标话题ID
 */
async function logForwardedSubmission(env, originalMessage, forwardedMessage, topicId) {
	try {
		// 提取消息类型
		let mediaType = 'text';
		if (originalMessage.photo) mediaType = 'photo';
		else if (originalMessage.video) mediaType = 'video';
		else if (originalMessage.animation) mediaType = 'animation';
		else if (originalMessage.document) mediaType = 'document';
		else if (originalMessage.audio) mediaType = 'audio';
		else if (originalMessage.voice) mediaType = 'voice';
		else if (originalMessage.video_note) mediaType = 'video_note';

		// 处理转发来源
		let forwardFromId = null;
		let forwardFromTitle = null;
		if (originalMessage.forward_from_chat) {
			forwardFromId = originalMessage.forward_from_chat.id.toString();
			forwardFromTitle = originalMessage.forward_from_chat.title;
		}

		// 插入记录到新表 - 使用安全字符串处理
		await env.DB.prepare(`
			INSERT INTO tg_log_forwarded_submissions (
				user_id, msg_id, first_name, last_name, username,
				group_id, group_title, media_group_id, media_type, media_content,
				caption, caption_entities, forward_from_id, forward_from_title,
				raw_json, sub_message_id, sub_topic_id, date, forwarded_media_group_id
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`).bind(
			originalMessage.from.id,
			originalMessage.message_id,
			safeString(originalMessage.from.first_name || ''),
			safeString(originalMessage.from.last_name || ''),
			safeString(originalMessage.from.username || ''),
			originalMessage.chat.id,
			safeString(originalMessage.chat.title || ''),
			originalMessage.media_group_id || null,
			mediaType,
			safeString(originalMessage.text || ''),
			safeCaptionText(originalMessage.caption || ''),
			originalMessage.caption_entities ? JSON.stringify(originalMessage.caption_entities) : null,
			forwardFromId,
			safeString(forwardFromTitle || ''),
			JSON.stringify(originalMessage),
			forwardedMessage.message_id,
			parseInt(topicId),
			originalMessage.date,
			forwardedMessage.media_group_id || null  // 记录转发后的媒体组ID
		).run();

		Logger.debug('已记录转发消息到数据库');
	} catch (error) {
		Logger.error('记录转发消息到数据库失败:', error);
	}
}

/**
 * 查询已转发消息的记录
 * @param {Object} env 环境变量
 * @param {Number} msgId 原始消息ID
 * @param {String} groupId 群组ID
 * @returns {Object|null} 数据库记录或null
 */
async function getForwardedSubmissionRecord(env, msgId, groupId) {
	try {
		const result = await env.DB.prepare(`
			SELECT * FROM tg_log_forwarded_submissions 
			WHERE msg_id = ? AND group_id = ?
		`).bind(msgId, groupId).first();
		
		return result;
	} catch (error) {
		Logger.error('查询转发记录失败:', error);
		return null;
	}
}

/**
 * 获取媒体组的所有转发记录（通过原始媒体组ID）
 * @param {Object} env 环境变量
 * @param {String} mediaGroupId 原始媒体组ID
 * @returns {Array} 媒体组的所有记录
 */
async function getMediaGroupForwardedRecords(env, mediaGroupId) {
	try {
		const result = await env.DB.prepare(`
			SELECT * FROM tg_log_forwarded_submissions 
			WHERE media_group_id = ?
			ORDER BY msg_id ASC
		`).bind(mediaGroupId).all();
		
		return result.results || [];
	} catch (error) {
		Logger.error('查询媒体组转发记录失败:', error);
		return [];
	}
}

/**
 * 获取媒体组的所有转发记录（通过转发后的媒体组ID）
 * @param {Object} env 环境变量
 * @param {String} forwardedMediaGroupId 转发后的媒体组ID
 * @returns {Array} 媒体组的所有记录
 */
async function getMediaGroupForwardedRecordsByForwardedId(env, forwardedMediaGroupId) {
	try {
		const result = await env.DB.prepare(`
			SELECT * FROM tg_log_forwarded_submissions 
			WHERE forwarded_media_group_id = ?
			ORDER BY msg_id ASC
		`).bind(forwardedMediaGroupId).all();
		
		return result.results || [];
	} catch (error) {
		Logger.error('通过转发媒体组ID查询记录失败:', error);
		return [];
	}
}

/**
 * 删除转发记录
 * @param {Object} env 环境变量
 * @param {Number} recordId 记录ID
 */
async function deleteForwardedSubmissionRecord(env, recordId) {
	try {
		await env.DB.prepare(`
			DELETE FROM tg_log_forwarded_submissions WHERE id = ?
		`).bind(recordId).run();
		
		Logger.debug('已删除转发记录:', recordId);
	} catch (error) {
		Logger.error('删除转发记录失败:', error);
	}
}

/**
 * 删除消息（单条或按钮消息）
 * @param {Object} env 环境变量
 * @param {String} chatId 聊天ID
 * @param {Number} messageId 消息ID
 * @param {Number} topicId 话题ID
 */
async function deleteMessage(env, chatId, messageId, topicId) {
	try {
		// 删除主消息
		await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
			chat_id: chatId,
			message_id: messageId
		});
		
		// 尝试删除可能的按钮消息（通常是主消息ID+1）
		try {
			await sendTelegramRequest(env, `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/deleteMessage`, {
				chat_id: chatId,
				message_id: messageId + 1
			});
		} catch (error) {
			Logger.debug('删除按钮消息失败或不存在:', error.description);
		}
		
		Logger.debug('已删除消息:', messageId);
	} catch (error) {
		Logger.error('删除消息失败:', error);
	}
}

export {
	hasMedia,
	hasSubmissionTag,
	getTargetTopicId,
	logForwardedSubmission,
	getForwardedSubmissionRecord,
	getMediaGroupForwardedRecords,
	getMediaGroupForwardedRecordsByForwardedId,
	deleteForwardedSubmissionRecord,
	deleteMessage
}; 