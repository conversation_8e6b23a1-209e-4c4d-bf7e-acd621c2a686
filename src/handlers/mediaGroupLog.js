/**
 * 处理并记录媒体组消息
 */

import { transcodeEmoji } from '../utils/transcodeEmoji.js';
import Logger from '../utils/logger.js';
import { safeString, safeCaptionText } from '../utils/stringUtils.js';

export async function mediaGroupLog(ctx) {
	Logger.debug('mediaGroupLog Start');

	// 使用transcodeEmoji处理消息
	let message = JSON.parse(transcodeEmoji.utf16toEntities(JSON.stringify(ctx.message)));
	let chat = message.chat;
	let member = message.from;

	// 确定媒体类型和内容
	let mediaType = null;
	let mediaContent = null;

	if (message.photo) {
		mediaType = 'photo';
		mediaContent = message.photo.pop().file_id;
	}
	if (message.video) {
		mediaType = 'video';
		mediaContent = message.video.file_id;
	}
	if (message.document) {
		mediaType = 'document';
		mediaContent = message.document.file_id;
	}

	// 准备SQL参数，确保所有可能为undefined的值都转换为null - 使用安全字符串处理
	const data = {
		user_id: member.id,
		msg_id: message.message_id,
		first_name: safeString(member.first_name || ''),
		last_name: safeString(member.last_name || ''),
		username: safeString(member.username || ''),
		group_id: chat.id,
		group_title: safeString(chat.title || ''),
		media_group_id: message.media_group_id || null,
		media_type: mediaType || null,
		media_content: safeString(mediaContent || ''),
		caption: safeCaptionText(message.caption || ''),
		caption_entities: message.caption_entities ? JSON.stringify(message.caption_entities) : null,
		forward_from_chat: message.forward_from_chat ? JSON.stringify(message.forward_from_chat) : null,
		raw_json: JSON.stringify(message),
		date: message.date,
	};

	Logger.info('mediaGroupLog data:', data);

	try {
		// 使用D1数据库存储数据
		const stmt = ctx.env.DB.prepare(
			`
      INSERT INTO tg_log_media_group (
        user_id, msg_id, first_name, last_name, username, 
        group_id, group_title, media_group_id, media_type, 
        media_content, caption, caption_entities, 
        forward_from_chat, raw_json, date
      ) 
      VALUES (
        ?, ?, ?, ?, ?, 
        ?, ?, ?, ?, 
        ?, ?, ?, 
        ?, ?, ?
      )
    `
		).bind(
			data.user_id,
			data.msg_id,
			data.first_name,
			data.last_name,
			data.username,
			data.group_id,
			data.group_title,
			data.media_group_id,
			data.media_type,
			data.media_content,
			data.caption,
			data.caption_entities,
			data.forward_from_chat,
			data.raw_json,
			data.date
		);

		await stmt.run();
		Logger.success('数据已成功保存到数据库');
	} catch (error) {
		Logger.error('数据库操作失败:', error);
	}

	Logger.debug('mediaGroupLog End');
}
