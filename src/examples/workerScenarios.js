/**
 * Cloudflare Workers 中使用 DO 解决传统 NodeJS 场景的示例
 * 解决 Workers 无状态、无持久化、无全局变量等问题
 */

import DO from '../utils/objectStore.js';

/**
 * 场景1: 全局配置管理 (替代传统的全局变量)
 * 传统 NodeJS: 可以用全局变量，Workers 中会丢失
 */
export class GlobalConfigManager {
    static async init(env) {
        DO.init(env);
    }

    // 传统 NodeJS 中可能这样写：
    // global.appConfig = { theme: 'dark', version: '1.0' }
    
    // Workers 中用 DO 实现：
    static async setConfig(key, value) {
        return await DO.set(`config:${key}`, value);
    }

    static async getConfig(key, defaultValue = null) {
        return await DO.get(`config:${key}`, defaultValue);
    }

    static async updateConfig(updates) {
        const configKeys = Object.keys(updates).map(k => `config:${k}`);
        const configPairs = Object.fromEntries(
            Object.entries(updates).map(([k, v]) => [`config:${k}`, v])
        );
        return await DO.setMultiple(configPairs);
    }
}

/**
 * 场景2: 多请求间状态同步 (替代传统的内存共享)
 * 传统 NodeJS: 可以用内存变量在请求间共享状态
 */
export class RequestStateManager {
    static async init(env) {
        DO.init(env);
    }

    // 传统 NodeJS 中可能这样写：
    // let activeUsers = new Set()
    // let processingQueue = []
    
    // Workers 中用 DO 实现：
    static async addActiveUser(userId) {
        const activeUsers = await DO.get('state:activeUsers', []);
        if (!activeUsers.includes(userId)) {
            activeUsers.push(userId);
            await DO.set('state:activeUsers', activeUsers);
        }
        return activeUsers.length;
    }

    static async removeActiveUser(userId) {
        const activeUsers = await DO.get('state:activeUsers', []);
        const updated = activeUsers.filter(id => id !== userId);
        await DO.set('state:activeUsers', updated);
        return updated.length;
    }

    static async getActiveUserCount() {
        const activeUsers = await DO.get('state:activeUsers', []);
        return activeUsers.length;
    }

    static async addToProcessingQueue(task) {
        const queue = await DO.get('state:processingQueue', []);
        queue.push({
            id: Date.now(),
            task,
            timestamp: Date.now(),
            status: 'pending'
        });
        await DO.set('state:processingQueue', queue);
        return queue.length;
    }

    static async getNextTask() {
        const queue = await DO.get('state:processingQueue', []);
        const nextTask = queue.find(task => task.status === 'pending');
        
        if (nextTask) {
            nextTask.status = 'processing';
            await DO.set('state:processingQueue', queue);
        }
        
        return nextTask;
    }
}

/**
 * 场景3: 计数器和统计 (替代传统的内存计数)
 * 传统 NodeJS: 可以用内存变量计数，Workers 中会重置
 */
export class StatisticsManager {
    static async init(env) {
        DO.init(env);
    }

    // 传统 NodeJS 中可能这样写：
    // let requestCount = 0
    // let errorCount = 0
    // let dailyStats = {}
    
    // Workers 中用 DO 实现：
    static async incrementRequestCount() {
        const count = await DO.get('stats:requests', 0);
        const newCount = count + 1;
        await DO.set('stats:requests', newCount);
        return newCount;
    }

    static async incrementErrorCount(errorType = 'general') {
        const count = await DO.get(`stats:errors:${errorType}`, 0);
        const newCount = count + 1;
        await DO.set(`stats:errors:${errorType}`, newCount);
        return newCount;
    }

    static async getDailyStats(date = null) {
        if (!date) {
            date = new Date().toISOString().split('T')[0];
        }
        
        return await DO.get(`stats:daily:${date}`, {
            requests: 0,
            errors: 0,
            users: 0,
            date
        });
    }

    static async updateDailyStats(date, updates) {
        if (!date) {
            date = new Date().toISOString().split('T')[0];
        }
        
        const current = await this.getDailyStats(date);
        const updated = { ...current, ...updates };
        
        // 设置过期时间为30天
        await DO.set(`stats:daily:${date}`, updated, 30 * 24 * 60 * 60);
        
        return updated;
    }

    static async getAllStats() {
        const requests = await DO.get('stats:requests', 0);
        const generalErrors = await DO.get('stats:errors:general', 0);
        const today = new Date().toISOString().split('T')[0];
        const dailyStats = await this.getDailyStats(today);
        
        return {
            total: {
                requests,
                errors: generalErrors
            },
            today: dailyStats
        };
    }
}

/**
 * 场景4: 缓存管理 (替代传统的 Redis/内存缓存)
 * 传统 NodeJS: 使用 Redis 或内存缓存，Workers 中需要外部服务
 */
export class CacheManager {
    static async init(env) {
        DO.init(env);
    }

    // 传统 NodeJS 中可能这样写：
    // const redis = require('redis')
    // redis.set('key', 'value', 'EX', 300)
    
    // Workers 中用 DO 实现：
    static async cache(key, value, ttlSeconds = 300) {
        return await DO.set(`cache:${key}`, {
            value,
            timestamp: Date.now(),
            ttl: ttlSeconds
        }, ttlSeconds);
    }

    static async getCached(key) {
        const cached = await DO.get(`cache:${key}`);
        if (!cached) return null;
        
        // 检查是否过期（双重保险）
        const now = Date.now();
        if (cached.timestamp + (cached.ttl * 1000) < now) {
            await DO.delete(`cache:${key}`);
            return null;
        }
        
        return cached.value;
    }

    static async invalidateCache(pattern = null) {
        if (pattern) {
            const keys = await DO.keys(`cache:${pattern}`);
            for (const key of keys) {
                await DO.delete(key);
            }
        } else {
            const allCacheKeys = await DO.keys('cache:');
            for (const key of allCacheKeys) {
                await DO.delete(key);
            }
        }
    }

    // 缓存穿透保护
    static async getOrSet(key, fetchFunction, ttlSeconds = 300) {
        let cached = await this.getCached(key);
        
        if (cached === null) {
            const value = await fetchFunction();
            await this.cache(key, value, ttlSeconds);
            return value;
        }
        
        return cached;
    }
}

/**
 * 场景5: 会话管理 (替代传统的 session 中间件)
 * 传统 NodeJS: 使用 express-session 等中间件
 */
export class SessionManager {
    static async init(env) {
        DO.init(env);
    }

    static generateSessionId() {
        return 'sess_' + Math.random().toString(36).substr(2, 16) + '_' + Date.now();
    }

    // 传统 NodeJS 中可能这样写：
    // app.use(session({ store: new RedisStore() }))
    // req.session.userId = 123
    
    // Workers 中用 DO 实现：
    static async createSession(userId, userData = {}) {
        const sessionId = this.generateSessionId();
        const sessionData = {
            userId,
            ...userData,
            createdAt: Date.now(),
            lastActivity: Date.now()
        };
        
        // 会话30分钟过期
        await DO.set(`session:${sessionId}`, sessionData, 1800);
        return sessionId;
    }

    static async getSession(sessionId) {
        if (!sessionId) return null;
        
        const session = await DO.get(`session:${sessionId}`);
        if (session) {
            // 更新最后活动时间
            session.lastActivity = Date.now();
            await DO.set(`session:${sessionId}`, session, 1800);
        }
        
        return session;
    }

    static async updateSession(sessionId, updates) {
        const session = await this.getSession(sessionId);
        if (!session) return null;
        
        const updated = { ...session, ...updates, lastActivity: Date.now() };
        await DO.set(`session:${sessionId}`, updated, 1800);
        return updated;
    }

    static async destroySession(sessionId) {
        return await DO.delete(`session:${sessionId}`);
    }

    static async cleanupExpiredSessions() {
        const allSessions = await DO.keys('session:');
        let cleaned = 0;
        
        for (const key of allSessions) {
            const session = await DO.get(key);
            if (!session || Date.now() - session.lastActivity > 1800000) {
                await DO.delete(key);
                cleaned++;
            }
        }
        
        return cleaned;
    }
}

/**
 * 场景6: 限流和防护 (替代传统的限流中间件)
 * 传统 NodeJS: 使用 express-rate-limit 等中间件
 */
export class RateLimitManager {
    static async init(env) {
        DO.init(env);
    }

    // 传统 NodeJS 中可能这样写：
    // app.use(rateLimit({ windowMs: 60000, max: 100 }))
    
    // Workers 中用 DO 实现：
    static async checkRateLimit(identifier, limit = 100, windowSeconds = 60) {
        const key = `ratelimit:${identifier}`;
        const now = Date.now();
        const windowStart = now - (windowSeconds * 1000);
        
        const requests = await DO.get(key, []);
        
        // 清理过期请求
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        
        if (validRequests.length >= limit) {
            return {
                allowed: false,
                remaining: 0,
                resetTime: Math.min(...validRequests) + (windowSeconds * 1000)
            };
        }
        
        // 添加当前请求
        validRequests.push(now);
        await DO.set(key, validRequests, windowSeconds);
        
        return {
            allowed: true,
            remaining: limit - validRequests.length,
            resetTime: now + (windowSeconds * 1000)
        };
    }

    static async getRateLimitStatus(identifier, windowSeconds = 60) {
        const key = `ratelimit:${identifier}`;
        const now = Date.now();
        const windowStart = now - (windowSeconds * 1000);
        
        const requests = await DO.get(key, []);
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        
        return {
            requestCount: validRequests.length,
            windowStart: windowStart,
            windowEnd: now
        };
    }
}

/**
 * 完整的使用示例：在 Worker 中集成所有功能
 */
export async function handleWorkerRequest(request, env, ctx) {
    // 初始化所有管理器
    await GlobalConfigManager.init(env);
    await RequestStateManager.init(env);
    await StatisticsManager.init(env);
    await CacheManager.init(env);
    await SessionManager.init(env);
    await RateLimitManager.init(env);
    
    const url = new URL(request.url);
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
    
    // 1. 检查速率限制
    const rateLimitResult = await RateLimitManager.checkRateLimit(clientIP, 100, 60);
    if (!rateLimitResult.allowed) {
        await StatisticsManager.incrementErrorCount('ratelimit');
        return new Response('Rate limit exceeded', { 
            status: 429,
            headers: {
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
            }
        });
    }
    
    // 2. 更新统计
    const requestCount = await StatisticsManager.incrementRequestCount();
    
    // 3. 处理会话
    const sessionId = request.headers.get('X-Session-ID');
    let session = null;
    if (sessionId) {
        session = await SessionManager.getSession(sessionId);
    }
    
    // 4. 使用缓存
    const cacheKey = `response:${url.pathname}`;
    let cachedResponse = await CacheManager.getCached(cacheKey);
    
    if (cachedResponse && !session) {
        // 返回缓存的响应
        return new Response(JSON.stringify(cachedResponse), {
            headers: {
                'Content-Type': 'application/json',
                'X-Cache': 'HIT',
                'X-Request-Count': requestCount.toString()
            }
        });
    }
    
    // 5. 处理实际请求
    const responseData = {
        message: 'Hello from Workers with DO!',
        timestamp: Date.now(),
        requestCount,
        session: session ? { userId: session.userId } : null,
        rateLimitRemaining: rateLimitResult.remaining
    };
    
    // 6. 缓存响应
    if (!session) {
        await CacheManager.cache(cacheKey, responseData, 300);
    }
    
    // 7. 更新日统计
    const today = new Date().toISOString().split('T')[0];
    await StatisticsManager.updateDailyStats(today, {
        requests: requestCount
    });
    
    return new Response(JSON.stringify(responseData), {
        headers: {
            'Content-Type': 'application/json',
            'X-Cache': 'MISS',
            'X-Request-Count': requestCount.toString(),
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString()
        }
    });
} 