/**
 * Durable Objects 键值存储使用示例
 * 展示如何使用封装后的简单接口
 */

import DO, { createObjectStore } from '../utils/objectStore.js';

/**
 * 示例：在 Worker 请求处理函数中使用
 */
export async function exampleUsage(env) {
	// 方法1: 使用全局实例（需要先初始化）
	DO.init(env);
	
	// 基本操作
	await DO.set('user:123', { name: '张三', age: 25 });
	const user = await DO.get('user:123');
	console.log('用户信息:', user); // { name: '张三', age: 25 }
	
	// 带过期时间的设置（60秒后过期）
	await DO.set('temp:session', 'abc123', 60);
	
	// 检查键是否存在
	const exists = await DO.has('user:123');
	console.log('用户存在:', exists); // true
	
	// 获取所有键
	const allKeys = await DO.keys();
	console.log('所有键:', allKeys);
	
	// 获取指定前缀的键
	const userKeys = await DO.keys('user:', 10);
	console.log('用户相关的键:', userKeys);
	
	// 批量操作
	await DO.setMultiple({
		'config:theme': 'dark',
		'config:lang': 'zh-CN',
		'config:debug': true
	});
	
	const configs = await DO.getMultiple(['config:theme', 'config:lang', 'config:debug']);
	console.log('配置项:', configs);
	
	// 删除键
	await DO.delete('temp:session');
	
	// 获取存储大小
	const size = await DO.size();
	console.log('存储中的键数量:', size);
}

/**
 * 示例：创建自定义存储实例
 */
export async function exampleCustomStore(env) {
	// 方法2: 创建自定义实例
	const userStore = createObjectStore(env, 'KEY_VALUE_STORE', 'user-data');
	const cacheStore = createObjectStore(env, 'KEY_VALUE_STORE', 'cache-data');
	
	// 用户数据存储
	await userStore.set('profile', { 
		name: '李四', 
		preferences: { theme: 'light', lang: 'en' } 
	});
	
	// 缓存存储
	await cacheStore.set('api:response:1', { 
		data: 'cached response', 
		timestamp: Date.now() 
	}, 300); // 5分钟过期
	
	const profile = await userStore.get('profile');
	const cachedResponse = await cacheStore.get('api:response:1');
	
	console.log('用户资料:', profile);
	console.log('缓存响应:', cachedResponse);
}

/**
 * 示例：错误处理和默认值
 */
export async function exampleErrorHandling(env) {
	DO.init(env);
	
	// 获取不存在的键，返回默认值
	const nonExistent = await DO.get('non-existent-key', '默认值');
	console.log('不存在的键:', nonExistent); // '默认值'
	
	// 尝试设置，返回布尔值表示成功失败
	const success = await DO.set('test:key', 'test value');
	console.log('设置成功:', success); // true
	
	// 检查网络错误等情况
	try {
		await DO.set('another:key', { complex: 'object', data: [1, 2, 3] });
		console.log('复杂对象存储成功');
	} catch (error) {
		console.error('存储失败:', error);
	}
}

/**
 * 示例：在实际业务场景中的使用
 */
export async function exampleBusinessScenario(env) {
	DO.init(env);
	
	// 用户会话管理
	const sessionId = 'sess_' + Math.random().toString(36).substr(2, 9);
	await DO.set(`session:${sessionId}`, {
		userId: 123,
		loginTime: Date.now(),
		permissions: ['read', 'write']
	}, 1800); // 30分钟会话
	
	// 缓存数据库查询结果
	const cacheKey = 'db:users:recent:page1';
	let recentUsers = await DO.get(cacheKey);
	
	if (!recentUsers) {
		// 模拟数据库查询
		recentUsers = [
			{ id: 1, name: '用户1' },
			{ id: 2, name: '用户2' }
		];
		// 缓存5分钟
		await DO.set(cacheKey, recentUsers, 300);
		console.log('从数据库查询并缓存');
	} else {
		console.log('从缓存获取数据');
	}
	
	// 计数器功能
	const visitKey = 'counter:page:visits';
	let visits = await DO.get(visitKey, 0);
	visits++;
	await DO.set(visitKey, visits);
	console.log(`页面访问次数: ${visits}`);
	
	// 配置管理
	const defaultConfig = {
		maxUploadSize: 10485760, // 10MB
		allowedTypes: ['image/jpeg', 'image/png'],
		rateLimit: 100
	};
	
	let config = await DO.get('app:config', defaultConfig);
	console.log('应用配置:', config);
}

/**
 * 在主 Worker 中的集成示例
 */
export async function integrateIntoWorker(request, env, ctx) {
	// 在 Worker 的 fetch 函数开始处初始化
	DO.init(env);
	
	const url = new URL(request.url);
	
	if (url.pathname === '/api/user-settings') {
		// 用户设置API
		const userId = url.searchParams.get('userId');
		
		if (request.method === 'GET') {
			const settings = await DO.get(`user:${userId}:settings`, {
				theme: 'light',
				notifications: true
			});
			
			return new Response(JSON.stringify(settings), {
				headers: { 'Content-Type': 'application/json' }
			});
		}
		
		if (request.method === 'POST') {
			const newSettings = await request.json();
			const success = await DO.set(`user:${userId}:settings`, newSettings);
			
			return new Response(JSON.stringify({ success }), {
				headers: { 'Content-Type': 'application/json' }
			});
		}
	}
	
	if (url.pathname === '/api/cache-stats') {
		// 缓存统计API
		const cacheKeys = await DO.keys('cache:');
		const size = await DO.size();
		
		return new Response(JSON.stringify({
			totalKeys: size,
			cacheKeys: cacheKeys.length,
			keys: cacheKeys
		}), {
			headers: { 'Content-Type': 'application/json' }
		});
	}
	
	return new Response('Not Found', { status: 404 });
} 