/**
 * 认证中间件
 * 提供统一的API认证验证
 */

import Logger from '../../utils/logger.js';
import { BOT_ADMINS } from '../../config/constants.js';

/**
 * 验证Telegram签名
 * @param {string} dataCheckString 待验证的数据字符串
 * @param {string} receivedHash 接收到的hash值
 * @param {string} botToken 机器人Token
 * @returns {Promise<boolean>} 签名是否有效
 */
async function verifyTelegramSignature(dataCheckString, receivedHash, botToken) {
    try {
        const encoder = new TextEncoder();
        
        // 第一步：使用"WebAppData"作为密钥，对bot token进行HMAC-SHA256
        const webAppDataKey = await crypto.subtle.importKey(
            'raw',
            encoder.encode('WebAppData'),
            { name: 'HMAC', hash: 'SHA-256' },
            false,
            ['sign']
        );
        
        const tokenBuffer = encoder.encode(botToken);
        const secretKeyBuffer = await crypto.subtle.sign('HMAC', webAppDataKey, tokenBuffer);
        
        // 第二步：使用第一步得到的结果作为密钥，对data check string进行HMAC-SHA256
        const secretKey = await crypto.subtle.importKey(
            'raw',
            secretKeyBuffer,
            { name: 'HMAC', hash: 'SHA-256' },
            false,
            ['sign']
        );
        
        const dataBuffer = encoder.encode(dataCheckString);
        const signatureBuffer = await crypto.subtle.sign('HMAC', secretKey, dataBuffer);
        
        // 第三步：将计算出的签名转换为hex字符串
        const calculatedHash = Array.from(new Uint8Array(signatureBuffer))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
        
        // 第四步：比较计算出的hash和接收到的hash
        const isValid = calculatedHash === receivedHash;
        
        Logger.debug('Telegram签名验证', {
            dataCheckString: dataCheckString.substring(0, 100) + '...',
            receivedHash,
            calculatedHash,
            isValid
        });
        
        return isValid;
        
    } catch (error) {
        Logger.error('计算Telegram签名时出错:', error);
        return false;
    }
}

/**
 * 验证并解析Telegram MiniApp数据
 * @param {string} initData Telegram initData字符串
 * @param {string} botToken 机器人Token
 * @returns {Promise<Object|null>} 解析后的用户数据或null
 */
async function validateTelegramMiniAppData(initData, botToken) {
    try {
        // 解析initData参数
        const urlParams = new URLSearchParams(initData);
        const hash = urlParams.get('hash');
        
        if (!hash) {
            Logger.warn('initData中缺少hash参数');
            return null;
        }
        
        // 移除hash参数，构建验证字符串
        urlParams.delete('hash');
        const dataCheckString = Array.from(urlParams.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}=${value}`)
            .join('\n');
        
        // 验证签名
        const isValid = await verifyTelegramSignature(dataCheckString, hash, botToken);
        
        if (!isValid) {
            Logger.warn('Telegram签名验证失败');
            return null;
        }
        
        // 解析用户数据
        const userParam = urlParams.get('user');
        if (!userParam) {
            Logger.warn('initData中缺少user参数');
            return null;
        }
        
        const user = JSON.parse(decodeURIComponent(userParam));
        
        Logger.debug('Telegram MiniApp数据验证成功:', {
            userId: user.id,
            firstName: user.first_name
        });
        
        return user;
        
    } catch (error) {
        Logger.error('验证Telegram MiniApp数据时出错:', error);
        return null;
    }
}

/**
 * 验证 API 请求的认证信息
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @returns {Promise<Object>} 验证结果 { success: boolean, user?: Object, error?: string }
 */
export async function verifyAPIAuth(request, env) {
    try {
        // 获取 Authorization 头
        const authHeader = request.headers.get('Authorization');
        if (!authHeader || !authHeader.startsWith('tma ')) {
            return {
                success: false,
                error: '缺少认证头部'
            };
        }

        // 提取 token（实际上是 initData）
        const token = authHeader.substring(4); // 移除 "tma " 前缀
        
        if (!token) {
            return {
                success: false,
                error: '认证令牌为空'
            };
        }

        // 验证 Telegram MiniApp 数据
        const user = await validateTelegramMiniAppData(token, env.TELEGRAM_BOT_TOKEN);
        
        if (!user || !user.id) {
            return {
                success: false,
                error: '无效的用户认证数据'
            };
        }

        // 检查用户是否为管理员
        const isAdmin = BOT_ADMINS.includes(user.id);
        
        if (!isAdmin) {
            Logger.warn('非管理员用户尝试访问API:', {
                userId: user.id,
                userName: user.first_name
            });
            
            return {
                success: false,
                error: '权限不足'
            };
        }

        Logger.debug('API认证成功:', {
            userId: user.id,
            userName: user.first_name
        });

        return {
            success: true,
            user: {
                id: user.id,
                first_name: user.first_name,
                last_name: user.last_name,
                username: user.username,
                isAdmin: true
            }
        };

    } catch (error) {
        Logger.error('API认证验证失败:', error);
        return {
            success: false,
            error: '认证验证失败'
        };
    }
}

/**
 * 创建认证失败响应
 * @param {string} error 错误信息
 * @param {Object} corsHeaders CORS头部
 * @returns {Response} 错误响应
 */
export function createAuthFailureResponse(error, corsHeaders = {}) {
    return new Response(JSON.stringify({
        success: false,
        error
    }), {
        status: 401,
        headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
        }
    });
}

// 导出验证函数供其他模块使用
export { validateTelegramMiniAppData };