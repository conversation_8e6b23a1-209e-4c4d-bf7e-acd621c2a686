/**
 * CORS 中间件
 * 提供统一的跨域资源共享处理
 */

/**
 * 应用 CORS 头部
 * @param {Request} request 请求对象
 * @returns {Object} CORS 头部对象
 */
export function applyCorsHeaders(request) {
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400'
    };

    // 如果请求来自特定域名，可以更精确地设置 Origin
    const origin = request.headers.get('Origin');
    if (origin) {
        // 可以在这里添加域名白名单验证
        corsHeaders['Access-Control-Allow-Origin'] = origin;
    }

    return corsHeaders;
}

/**
 * 处理 OPTIONS 预检请求
 * @param {Request} request 请求对象
 * @returns {Response} 预检响应
 */
export function handleCorsPreflightRequest(request) {
    const corsHeaders = applyCorsHeaders(request);
    
    return new Response(null, {
        status: 204,
        headers: corsHeaders
    });
} 