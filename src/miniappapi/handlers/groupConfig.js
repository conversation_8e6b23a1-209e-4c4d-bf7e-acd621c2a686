/**
 * 群配置管理 API 处理器
 * 为 miniapp 群配置页面提供后端API支持
 */

import Logger from '../../utils/logger.js';
import { DefaultGroupsBind } from '../../config/constants.js';
import { 
	getGroupConfig, 
	createOrUpdateGroupConfig, 
	getAllGroupConfigs,
	getReportStats,
	validateGroupConfig
} from '../../utils/groupConfigUtils.js';
import {
	getUserManagedGroups,
	verifyTelegramAdminPermission,
	verifyBotAdminPermission,
	addGroupSuperAdmin,
	addGroupAdmin,
	removeGroupAdmin,
	getGroupAdmins,
	getChatInfo,
	isValidGroupId,
	isGroupSuperAdmin
} from '../../utils/groupPermissionUtils.js';

/**
 * 处理群配置 API 请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleGroupConfigAPI(request, env, ctx) {
	const url = new URL(request.url);
	const pathname = url.pathname;
	const { corsHeaders } = ctx;
	
	try {
		Logger.debug('群配置API请求:', {
			method: request.method,
			pathname,
			hasUser: !!ctx.user
		});
		
		// 路由分发
		if (pathname === '/api/group-config/groups' && request.method === 'GET') {
			return await handleGetGroups(request, env, ctx, corsHeaders);
		}
		
		if (pathname === '/api/group-config/add-group' && request.method === 'POST') {
			return await handleAddGroup(request, env, ctx, corsHeaders);
		}
		
		if (pathname.startsWith('/api/group-config/config/') && request.method === 'GET') {
			const chatId = decodeURIComponent(pathname.split('/').pop());
			return await handleGetConfig(request, env, chatId, corsHeaders);
		}
		
		if (pathname.startsWith('/api/group-config/stats/') && request.method === 'GET') {
			const chatId = decodeURIComponent(pathname.split('/').pop());
			return await handleGetStats(request, env, chatId, corsHeaders);
		}
		
		if (pathname === '/api/group-config/save' && request.method === 'POST') {
			return await handleSaveConfig(request, env, corsHeaders);
		}
		
		if (pathname === '/api/group-config/verify-rules' && request.method === 'POST') {
			return await handleVerifyRules(request, env, corsHeaders);
		}
		
		if (pathname.startsWith('/api/group-config/admins/') && request.method === 'GET') {
			const chatId = decodeURIComponent(pathname.split('/').pop());
			return await handleGetGroupAdmins(request, env, ctx, chatId, corsHeaders);
		}
		
		if (pathname === '/api/group-config/add-admin' && request.method === 'POST') {
			return await handleAddGroupAdmin(request, env, ctx, corsHeaders);
		}
		
		if (pathname === '/api/group-config/remove-admin' && request.method === 'POST') {
			return await handleRemoveGroupAdmin(request, env, ctx, corsHeaders);
		}
		
		if (pathname === '/api/group-config/delete-group' && request.method === 'POST') {
			return await handleDeleteGroup(request, env, ctx, corsHeaders);
		}
		
		// 未找到匹配的路由
		return new Response(JSON.stringify({ error: '接口不存在' }), {
			status: 404,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('群配置API处理失败:', error);
		return new Response(JSON.stringify({ error: '服务器内部错误' }), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}

// #region 📊 获取群组列表
async function handleGetGroups(request, env, ctx, corsHeaders) {
	try {
		Logger.debug('获取群组列表API调用');
		
		const userId = ctx.user?.id;
		if (!userId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '用户未认证' 
			}), {
				status: 401,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 获取用户有权限管理的群组
		const managedGroups = await getUserManagedGroups(env, userId);
		
		// 转换格式
		const groups = managedGroups.map(group => ({
				chat_id: group.chat_id,
			chat_title: group.chat_title || group.chat_id,
			permission_level: group.permission_level
		}));
		
		Logger.debug(`用户 ${userId} 有权限管理 ${groups.length} 个群组`);
		
		return new Response(JSON.stringify({
			success: true,
			groups: groups
		}), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('获取群组列表失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}
// #endregion

// #region ⚙️ 获取群组配置
async function handleGetConfig(request, env, chatId, corsHeaders) {
	try {
		Logger.debug('获取群组配置API调用:', chatId);
		
		if (!chatId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '群组ID不能为空' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		const config = await getGroupConfig(env, chatId);
		
		// 获取群规配置
		const { getGroupRules } = await import('../../utils/groupRulesUtils.js');
		const rulesConfig = await getGroupRules(env, chatId);
		
		// 如果没有配置，返回默认配置
		const defaultConfig = {
			chat_id: chatId,
			report_mode: 'disabled',
			report_target_groups: [],
			new_member_verification: false,
			cloud_filter_enabled: false,
			auto_forward_enabled: false,
			rules: []
		};
		
		// 合并配置和群规
		const finalConfig = config ? {
			...config,
			rules: rulesConfig?.rule_urls || []
		} : defaultConfig;
		
		return new Response(JSON.stringify({
			success: true,
			config: finalConfig
		}), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('获取群组配置失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}
// #endregion

// #region 📊 获取举报统计
async function handleGetStats(request, env, chatId, corsHeaders) {
	try {
		Logger.debug('获取举报统计API调用:', chatId);
		
		if (!chatId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '群组ID不能为空' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		const stats = await getReportStats(env, chatId, 30);
		
		return new Response(JSON.stringify({
			success: true,
			...stats
		}), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('获取举报统计失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}
// #endregion

// #region 💾 保存群组配置
async function handleSaveConfig(request, env, corsHeaders) {
	try {
		const body = await request.json();
		Logger.debug('保存群组配置API调用:', body.chat_id);
		
		// 验证数据
		const validation = validateGroupConfig(body);
		if (!validation.valid) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: validation.errors.join(', ') 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 获取群组标题（如果有的话）
		let chatTitle = body.chat_title;
		if (!chatTitle) {
			const defaultGroup = DefaultGroupsBind.find(group => group.id === body.chat_id);
			chatTitle = defaultGroup?.name || body.chat_id;
		}
		
		// 保存配置
		const success = await createOrUpdateGroupConfig(env, {
			...body,
			chat_title: chatTitle
		});
		
		if (!success) {
			throw new Error('群组配置保存失败');
		}
		
		// 保存群规配置（包括空数组的情况，用于删除所有群规）
		let rulesSaveResult = null;
		if (body.rules && Array.isArray(body.rules)) {
			const { saveGroupRules } = await import('../../utils/groupRulesUtils.js');

			rulesSaveResult = await saveGroupRules(
				env,
				body.chat_id,
				body.rules,
				1, // 默认管理员ID，实际应该从认证信息获取
				'管理员', // 默认管理员名称，实际应该从认证信息获取
				false // 不跳过验证
			);

			if (!rulesSaveResult.success) {
				Logger.warn('群规保存失败，但群组配置已保存成功');
			}
		}
		
		// 构建响应消息
		let responseMessage = '配置保存成功';
		const responseData = {
			success: true,
			message: responseMessage
		};
		
		// 包含群规验证结果
		if (rulesSaveResult) {
			responseData.rulesResult = {
				success: rulesSaveResult.success,
				summary: rulesSaveResult.summary,
				errors: rulesSaveResult.errors || []
			};
			
			if (rulesSaveResult.summary) {
				const { total, accessible, failed } = rulesSaveResult.summary;
				if (failed > 0) {
					responseMessage += ` (群规: ${accessible}/${total} 可访问)`;
				} else {
					responseMessage += ` (群规: 全部可访问)`;
				}
				responseData.message = responseMessage;
			}
		}
		
		return new Response(JSON.stringify(responseData), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('保存群组配置失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}
// #endregion

// #region 🔍 验证群规链接
async function handleVerifyRules(request, env, corsHeaders) {
	try {
		const body = await request.json();
		Logger.debug('验证群规链接API调用:', { ruleCount: body.rules?.length });
		
		if (!body.rules || !Array.isArray(body.rules) || body.rules.length === 0) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '请提供要验证的群规链接' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		if (body.rules.length > 10) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '一次最多只能验证10个链接' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		const { 
			parseTelegramUrl, 
			verifyMessageAccess, 
			validateRuleUrls 
		} = await import('../../utils/groupRulesUtils.js');
		
		// 先进行基础格式验证
		const validation = validateRuleUrls(body.rules);
		if (!validation.valid) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: validation.errors.join(', ') 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 执行详细验证
		const results = [];
		let accessibleCount = 0;
		
		for (let i = 0; i < body.rules.length; i++) {
			const rule = body.rules[i];
			const result = {
				index: i + 1,
				url: rule.url,
				title: rule.title || '群规',
				status: 'pending'
			};
			
			try {
				// 解析URL
				const parsedUrl = parseTelegramUrl(rule.url);
				if (!parsedUrl) {
					result.status = 'invalid';
					result.error = 'URL格式无效';
					results.push(result);
					continue;
				}
				
				// 验证访问权限
				const accessResult = await verifyMessageAccess(env, parsedUrl.sourceChat, parsedUrl.messageId);
				if (accessResult.canAccess) {
					result.status = 'accessible';
					result.chatInfo = accessResult.chatInfo;
					accessibleCount++;
				} else {
					result.status = 'inaccessible';
					result.error = accessResult.error;
				}
			} catch (error) {
				result.status = 'error';
				result.error = error.message;
			}
			
			results.push(result);
		}
		
		return new Response(JSON.stringify({
			success: true,
			results,
			summary: {
				total: body.rules.length,
				accessible: accessibleCount,
				failed: body.rules.length - accessibleCount
			}
		}), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('验证群规链接失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}
// #endregion 

// #region ➕ 添加群组功能

/**
 * 处理添加群组请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @param {Object} corsHeaders CORS头信息
 * @returns {Promise<Response>} 响应对象
 */
async function handleAddGroup(request, env, ctx, corsHeaders) {
	try {
		const { chatId, chatTitle } = await request.json();
		const userId = ctx.user?.id;
		const userName = ctx.user?.first_name || '未知用户';
		
		Logger.debug('添加群组API调用:', { chatId, chatTitle, userId, userName });
		
		if (!userId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '用户未认证' 
			}), {
				status: 401,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 验证群组ID格式
		if (!isValidGroupId(chatId)) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '群组ID格式无效，应以-100开头' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 验证用户在Telegram群组中的权限
		const userPermission = await verifyTelegramAdminPermission(env, chatId, userId);
		if (!userPermission.canAddAdmins) {
			let errorMessage = '您没有权限管理此群组';
			
			if (userPermission.error) {
				errorMessage += `：${userPermission.error}`;
			} else if (!userPermission.isAdmin) {
				errorMessage += '：您不是该群组的管理员';
			} else if (userPermission.isAdmin && !userPermission.canAddAdmins) {
				errorMessage += '：您没有添加管理员的权限';
			}
			
			Logger.warn('用户权限验证失败:', {
				chatId,
				userId,
				userPermission
			});
			
			return new Response(JSON.stringify({ 
				success: false, 
				error: errorMessage
			}), {
				status: 403,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 验证bot在群组中的管理权限
		const botIsAdmin = await verifyBotAdminPermission(env, chatId);
		if (!botIsAdmin) {
			Logger.warn('Bot权限验证失败:', {
				chatId,
				botIsAdmin
			});
			
			return new Response(JSON.stringify({ 
				success: false, 
				error: 'Bot不是此群组的管理员，无法管理群组配置。请先将Bot添加为群组管理员。' 
			}), {
				status: 403,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 获取群组信息
		const groupInfo = await getChatInfo(env, chatId);
		const finalChatTitle = chatTitle || groupInfo?.title || chatId;
		
		// 创建群组配置记录
		const configSuccess = await createOrUpdateGroupConfig(env, {
			chat_id: chatId,
			chat_title: finalChatTitle,
			report_mode: 'disabled',
			report_target_groups: [],
			new_member_verification: true,  // ✅ 使用默认值true
			cloud_filter_enabled: true,     // ✅ 使用默认值true
			auto_forward_enabled: true      // ✅ 使用默认值true
		});
		
		if (!configSuccess) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '创建群组配置失败' 
			}), {
				status: 500,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 添加用户为超级管理员
		const adminSuccess = await addGroupSuperAdmin(env, chatId, userId, userName);
		if (!adminSuccess) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '添加超级管理员权限失败' 
			}), {
				status: 500,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		Logger.info('群组添加成功:', {
			chatId,
			chatTitle: finalChatTitle,
			userId,
			userName,
			userPermission
		});
		
		return new Response(JSON.stringify({
			success: true,
			message: '群组添加成功',
			group: {
				chat_id: chatId,
				chat_title: finalChatTitle,
				permission_level: 'super_admin'
			}
		}), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('添加群组失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}

// #endregion ➕ 添加群组功能

// #region 👥 管理员管理功能

/**
 * 获取群组管理员列表
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @param {string} chatId 群组ID
 * @param {Object} corsHeaders CORS头信息
 * @returns {Promise<Response>} 响应对象
 */
async function handleGetGroupAdmins(request, env, ctx, chatId, corsHeaders) {
	try {
		const userId = ctx.user?.id;
		
		if (!userId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '用户未认证' 
			}), {
				status: 401,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 检查用户权限
		const userIsSuperAdmin = await isGroupSuperAdmin(env, chatId, userId);
		if (!userIsSuperAdmin) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '只有超级管理员可以查看管理员列表' 
			}), {
				status: 403,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		const admins = await getGroupAdmins(env, chatId);
		
		return new Response(JSON.stringify({
			success: true,
			admins: admins
		}), {
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
		
	} catch (error) {
		Logger.error('获取群组管理员列表失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}

/**
 * 添加群组管理员
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @param {Object} corsHeaders CORS头信息
 * @returns {Promise<Response>} 响应对象
 */
async function handleAddGroupAdmin(request, env, ctx, corsHeaders) {
	try {
		const { chatId, targetUserId, targetUserName } = await request.json();
		const userId = ctx.user?.id;
		const userName = ctx.user?.first_name || '未知用户';
		
		if (!userId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '用户未认证' 
			}), {
				status: 401,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 检查操作者权限
		const userIsSuperAdmin = await isGroupSuperAdmin(env, chatId, userId);
		if (!userIsSuperAdmin) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '只有超级管理员可以添加管理员' 
			}), {
				status: 403,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 添加普通管理员
		const success = await addGroupAdmin(env, chatId, targetUserId, targetUserName, userId, userName);
		
		if (success) {
			return new Response(JSON.stringify({
				success: true,
				message: '管理员添加成功'
			}), {
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		} else {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '添加管理员失败' 
			}), {
				status: 500,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
	} catch (error) {
		Logger.error('添加群组管理员失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}

/**
 * 删除群组管理员
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @param {Object} corsHeaders CORS头信息
 * @returns {Promise<Response>} 响应对象
 */
async function handleRemoveGroupAdmin(request, env, ctx, corsHeaders) {
	try {
		const { chatId, targetUserId } = await request.json();
		const userId = ctx.user?.id;
		
		if (!userId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '用户未认证' 
			}), {
				status: 401,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 删除管理员（内部会验证权限）
		const success = await removeGroupAdmin(env, chatId, targetUserId, userId);
		
		if (success) {
			return new Response(JSON.stringify({
				success: true,
				message: '管理员删除成功'
			}), {
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		} else {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '删除管理员失败' 
			}), {
				status: 500,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
	} catch (error) {
		Logger.error('删除群组管理员失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}

// #endregion 👥 管理员管理功能

// #region 🗑️ 删除群组功能

/**
 * 删除群组及其所有关联数据
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @param {Object} corsHeaders CORS头信息
 * @returns {Promise<Response>} 响应对象
 */
async function handleDeleteGroup(request, env, ctx, corsHeaders) {
	try {
		const { chatId, confirmName } = await request.json();
		const userId = ctx.user?.id;
		
		if (!userId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '用户未认证' 
			}), {
				status: 401,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		if (!chatId) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '缺少群组ID' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 检查用户是否为该群组的超级管理员
		const userIsSuperAdmin = await isGroupSuperAdmin(env, chatId, userId);
		if (!userIsSuperAdmin) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '只有超级管理员可以删除群组' 
			}), {
				status: 403,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 获取群组信息进行确认
		const groupConfig = await getGroupConfig(env, chatId);
		if (!groupConfig) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '群组不存在' 
			}), {
				status: 404,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 验证确认名称
		if (!confirmName || confirmName.trim() !== (groupConfig.chat_title || chatId)) {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '确认名称不匹配' 
			}), {
				status: 400,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
		// 执行删除操作 - 由于外键级联删除，删除group_configs会自动删除关联数据
		const result = await env.DB.prepare(`
			DELETE FROM group_configs WHERE chat_id = ?
		`).bind(chatId).run();
		
		if (result.success) {
			Logger.info('群组删除成功:', {
				chatId,
				userId,
				groupTitle: groupConfig.chat_title
			});
			
			return new Response(JSON.stringify({
				success: true,
				message: '群组删除成功'
			}), {
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		} else {
			return new Response(JSON.stringify({ 
				success: false, 
				error: '删除群组失败' 
			}), {
				status: 500,
				headers: { ...corsHeaders, 'Content-Type': 'application/json' }
			});
		}
		
	} catch (error) {
		Logger.error('删除群组失败:', error);
		return new Response(JSON.stringify({ 
			success: false, 
			error: error.message 
		}), {
			status: 500,
			headers: { ...corsHeaders, 'Content-Type': 'application/json' }
		});
	}
}

// #endregion 🗑️ 删除群组功能 