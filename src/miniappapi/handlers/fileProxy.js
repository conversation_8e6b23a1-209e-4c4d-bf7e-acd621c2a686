/**
 * 文件代理API处理器
 * 处理Telegram文件的代理访问
 */

import Logger from '../../utils/logger.js';
import { sendTelegramRequest } from '../../utils/telegramApi.js';

/**
 * 处理文件代理API请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @param {string} fileId 文件ID（从URL路径中提取）
 * @returns {Promise<Response>} 响应对象
 */
export async function handleFileProxyAPI(request, env, ctx, fileId) {
    const { corsHeaders } = ctx;
    
    try {
        if (request.method === 'GET') {
            Logger.debug('文件代理请求', { fileId });
            
            // 获取文件信息
            const fileInfo = await sendTelegramRequest(
                env,
                `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/getFile`,
                { file_id: fileId }
            );
            
            if (fileInfo.ok) {
                const fileUrl = `https://api.telegram.org/file/bot${env.TELEGRAM_BOT_TOKEN}/${fileInfo.result.file_path}`;
                
                Logger.debug('代理文件', {
                    fileId,
                    filePath: fileInfo.result.file_path,
                    fileUrl
                });
                
                // 代理文件内容
                const fileResponse = await fetch(fileUrl);
                
                if (!fileResponse.ok) {
                    Logger.error('下载Telegram文件失败', {
                        fileId,
                        status: fileResponse.status,
                        statusText: fileResponse.statusText
                    });
                    
                    return new Response('File download failed', { 
                        status: 500, 
                        headers: corsHeaders 
                    });
                }
                
                return new Response(fileResponse.body, {
                    headers: {
                        ...corsHeaders,
                        'Content-Type': fileResponse.headers.get('Content-Type') || 'image/jpeg',
                        'Cache-Control': 'public, max-age=3600',
                        'Content-Length': fileResponse.headers.get('Content-Length') || '',
                        'Content-Disposition': fileResponse.headers.get('Content-Disposition') || ''
                    }
                });
            } else {
                Logger.error('获取Telegram文件信息失败', {
                    fileId,
                    error: fileInfo.description
                });
                
                return new Response('File not found', { 
                    status: 404, 
                    headers: corsHeaders 
                });
            }
        }
        
        return new Response('Method not allowed', { 
            status: 405, 
            headers: corsHeaders 
        });
        
    } catch (error) {
        Logger.error('文件代理API错误:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
} 