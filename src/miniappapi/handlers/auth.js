/**
 * Mini App 认证处理器
 * 处理用户认证和权限验证相关的API
 */

import Logger from '../../utils/logger.js';
import { BOT_ADMINS } from '../../config/constants.js';
import { validateTelegramMiniAppData } from '../middleware/auth.js';

/**
 * 处理认证请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleAuthRequest(request, env, ctx) {
    const { corsHeaders } = ctx;
    
    try {
        // 解析请求体
        const requestBody = await request.json();
        const { initData } = requestBody;
        
        if (!initData) {
            return new Response(JSON.stringify({ 
                success: false, 
                error: 'Missing initData' 
            }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        // 验证并解析用户数据
        const user = await validateTelegramMiniAppData(initData, env.TELEGRAM_BOT_TOKEN);
        
        if (!user || !user.id) {
            return new Response(JSON.stringify({ 
                success: false, 
                error: 'Invalid user data' 
            }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        // 检查用户是否在管理员列表中
        const isAdmin = BOT_ADMINS.includes(user.id);
        
        Logger.debug('认证请求处理:', {
            userId: user.id,
            userName: user.first_name,
            isAdmin
        });
        
        if (isAdmin) {
            return new Response(JSON.stringify({ 
                success: true, 
                user: {
                    id: user.id,
                    first_name: user.first_name,
                    last_name: user.last_name,
                    username: user.username
                },
                isAdmin: true
            }), {
                status: 200,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        } else {
            return new Response(JSON.stringify({ 
                success: false, 
                error: 'Access denied',
                user: {
                    id: user.id,
                    first_name: user.first_name
                },
                isAdmin: false
            }), {
                status: 403,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
    } catch (error) {
        Logger.error('认证API错误:', error);
        return new Response(JSON.stringify({ 
            success: false, 
            error: 'Internal server error' 
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
} 