/**
 * 投稿管理API处理器
 * 处理投稿列表、发布、拒绝等功能
 */

import Logger from '../../utils/logger.js';
import { sendTelegramRequest } from '../../utils/telegramApi.js';

/**
 * 处理投稿列表API请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleSubmissionsAPI(request, env, ctx) {
    const { corsHeaders } = ctx;
    
    try {
        if (request.method === 'GET') {
            const submissions = await env.DB.prepare(`
                SELECT id, first_name as user_name, group_title as chat_name, 
                       sub_message_id as file_id, caption, 'pending' as status, created_at
                FROM tg_log_forwarded_submissions 
                ORDER BY created_at DESC
                LIMIT 50
            `).all();
            
            Logger.debug('投稿列表查询成功', {
                count: (submissions.results || []).length
            });
            
            return new Response(JSON.stringify(submissions.results || []), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        return new Response('Method not allowed', { 
            status: 405, 
            headers: corsHeaders 
        });
        
    } catch (error) {
        Logger.error('投稿列表API错误:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

/**
 * 处理发布投稿API请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handlePublishAPI(request, env, ctx) {
    const { corsHeaders } = ctx;
    
    try {
        if (request.method === 'POST') {
            const { submissionId, channelName, channelId } = await request.json();
            
            // 获取投稿信息
            const submission = await env.DB.prepare(`
                SELECT * FROM tg_log_forwarded_submissions WHERE id = ?
            `).bind(submissionId).first();
            
            if (!submission) {
                return new Response(JSON.stringify({ error: '投稿不存在' }), {
                    status: 404,
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            }
            
            // 发送到频道
            let apiMethod = 'sendPhoto';
            const params = {
                chat_id: channelId,
                caption: submission.caption || `来自 ${submission.first_name} (${submission.group_title})`
            };
            
            // 根据媒体类型选择不同的方法
            if (submission.media_type === 'photo') {
                params.photo = submission.sub_message_id;
            } else if (submission.media_type === 'video') {
                apiMethod = 'sendVideo';
                params.video = submission.sub_message_id;
            } else if (submission.media_type === 'document') {
                apiMethod = 'sendDocument';
                params.document = submission.sub_message_id;
            } else {
                params.photo = submission.sub_message_id; // 默认按图片处理
            }
            
            const result = await sendTelegramRequest(
                env,
                `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/${apiMethod}`,
                params
            );
            
            if (result.ok) {
                Logger.debug('投稿发布成功', {
                    submissionId,
                    channelId,
                    messageId: result.result?.message_id
                });
                
                return new Response(JSON.stringify({ success: true }), {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            } else {
                Logger.error('投稿发布失败', {
                    submissionId,
                    channelId,
                    error: result.description
                });
                
                return new Response(JSON.stringify({ error: result.description || '发送失败' }), {
                    status: 500,
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            }
        }
        
        return new Response('Method not allowed', { 
            status: 405, 
            headers: corsHeaders 
        });
        
    } catch (error) {
        Logger.error('发布投稿API错误:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

/**
 * 处理拒绝投稿API请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleRejectAPI(request, env, ctx) {
    const { corsHeaders } = ctx;
    
    try {
        if (request.method === 'POST') {
            const { submissionId } = await request.json();
            
            Logger.debug('投稿拒绝请求', { submissionId });
            
            // 注意：新表没有status字段，可以考虑添加或使用其他方式标记
            // 这里暂时只记录日志，实际可能需要修改数据库表结构
            
            return new Response(JSON.stringify({ success: true }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        return new Response('Method not allowed', { 
            status: 405, 
            headers: corsHeaders 
        });
        
    } catch (error) {
        Logger.error('拒绝投稿API错误:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
} 