/**
 * Mini App 静态资源处理器
 * 处理 HTML、CSS、JS 等静态文件的请求
 */

import Logger from '../../utils/logger.js';

/**
 * 处理静态资源请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleStaticAssets(request, env, ctx) {
    const url = new URL(request.url);
    
    try {
        Logger.debug('处理静态资源:', url.pathname);

        // 处理主页重定向
        if (url.pathname === '/miniapp' || url.pathname === '/miniapp/') {
            const assetRequest = new Request(`${url.origin}/miniapp/index.html`);
            return env.ASSETS.fetch(assetRequest);
        }
        
        // 处理群配置页面
        if (url.pathname === '/miniapp/group-config' || url.pathname === '/miniapp/group-config.html') {
            const assetRequest = new Request(`${url.origin}/miniapp/group-config.html`);
            return env.ASSETS.fetch(assetRequest);
        }
        
        // 处理miniapp目录下的其他静态文件（JS、CSS等）
        if (url.pathname.startsWith('/miniapp/')) {
            return env.ASSETS.fetch(request);
        }
        
        return new Response('Static asset not found', { status: 404 });
        
    } catch (error) {
        Logger.error('静态资源处理错误:', error);
        return new Response('Internal Server Error', { status: 500 });
    }
} 