/**
 * 广告管理API处理器
 * 处理广告规则、发送记录等管理功能
 */

import Logger from '../../utils/logger.js';

/**
 * 处理广告管理API请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleAdsAPI(request, env, ctx) {
    const { corsHeaders } = ctx;
    const url = new URL(request.url);
    const path = url.pathname.replace('/api/ads', '');
    
    Logger.debug('广告管理API请求:', {
        method: request.method,
        originalPath: url.pathname,
        processedPath: path,
        hasUser: !!ctx.user
    });
    
    try {
        Logger.debug('开始导入AdManager...');
        // 动态导入广告管理器
        const { AdManager } = await import('../../handlers/adManager.js');
        Logger.debug('AdManager导入成功');
        
        // 设置全局env供AdManager使用
        globalThis.env = env;

        // 广告规则列表路由
        if (path === '' || path === '/') {
            if (request.method === 'GET') {
                // 获取广告规则列表
                const campaigns = await AdManager.getCampaigns(env.DB);
                
                Logger.debug('获取广告规则列表成功', {
                    count: campaigns.length
                });
                
                return new Response(JSON.stringify({ campaigns }), {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            } else if (request.method === 'POST') {
                // 创建新广告规则
                const campaignData = await request.json();
                const result = await AdManager.createCampaign(env.DB, campaignData);
                
                Logger.debug('创建广告规则', {
                    campaignData,
                    result
                });
                
                return new Response(JSON.stringify(result), {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            }
        }

        // 广告规则操作路由
        if (path.startsWith('/campaigns/')) {
            const campaignId = path.split('/')[2];
            
            if (request.method === 'PUT') {
                // 更新广告规则
                const updateData = await request.json();
                await AdManager.updateCampaign(env.DB, campaignId, updateData);
                
                Logger.debug('更新广告规则', {
                    campaignId,
                    updateData
                });
                
                return new Response(JSON.stringify({ success: true }), {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            } else if (request.method === 'DELETE') {
                // 删除广告规则
                await AdManager.deleteCampaign(env.DB, campaignId);
                
                Logger.debug('删除广告规则', { campaignId });
                
                return new Response(JSON.stringify({ success: true }), {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            }
        }

        // 广告发送记录路由
        if (path === '/posts' && request.method === 'GET') {
            const params = new URLSearchParams(url.search);
            const filters = {
                campaignId: params.get('campaign_id'),
                targetChannelId: params.get('target_channel_id')
            };
            const posts = await AdManager.getAdPosts(env.DB, filters);
            
            Logger.debug('获取广告发送记录', {
                filters,
                count: posts.length
            });
            
            return new Response(JSON.stringify({ posts }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        // 获取广告规则表原始数据
        if (path === '/tables/campaigns' && request.method === 'GET') {
            const campaigns = await AdManager.getAdCampaignsRaw(env.DB);
            
            Logger.debug('获取广告规则表原始数据', {
                count: campaigns.length
            });
            
            return new Response(JSON.stringify({ campaigns }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        // 获取广告发送记录表原始数据
        if (path === '/tables/posts' && request.method === 'GET') {
            const posts = await AdManager.getAdPostsRaw(env.DB);
            
            Logger.debug('获取广告发送记录表原始数据', {
                count: posts.length
            });
            
            return new Response(JSON.stringify({ posts }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        // 获取广告统计数据
        if (path === '/stats' && request.method === 'GET') {
            const stats = await AdManager.getAdStats(env.DB);
            
            Logger.debug('获取广告统计数据', stats);
            
            return new Response(JSON.stringify(stats), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        // 手动触发广告发送
        if (path === '/trigger' && request.method === 'POST') {
            const { campaignId } = await request.json();
            
            // 获取广告规则
            const campaigns = await AdManager.getCampaigns(env.DB, { campaignId });
            if (campaigns.length === 0) {
                return new Response(JSON.stringify({ error: '广告规则不存在' }), {
                    status: 404,
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            }
            
            // 强制执行广告发送
            await AdManager.executeAdPost(env.DB, campaigns[0]);
            
            Logger.debug('手动触发广告发送', { campaignId });
            
            return new Response(JSON.stringify({ success: true, message: '广告发送成功' }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        return new Response(JSON.stringify({ error: 'API endpoint not found' }), {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        Logger.error('广告管理API错误:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
} 