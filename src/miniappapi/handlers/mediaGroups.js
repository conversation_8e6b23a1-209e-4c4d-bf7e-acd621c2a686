/**
 * 媒体组API处理器
 * 处理媒体组数据的查询和管理
 */

import Logger from '../../utils/logger.js';

/**
 * 处理媒体组API请求
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleMediaGroupsAPI(request, env, ctx) {
    const { corsHeaders } = ctx;
    const url = new URL(request.url);
    
    try {
        if (request.method === 'GET') {
            // 获取查询参数
            const searchParams = url.searchParams;
            const search = searchParams.get('search') || '';
            const mediaType = searchParams.get('media_type') || '';
            const dateFrom = searchParams.get('date_from') || '';
            const dateTo = searchParams.get('date_to') || '';
            const limit = parseInt(searchParams.get('limit') || '50');
            const offset = parseInt(searchParams.get('offset') || '0');
            
            // 构建基础SQL查询
            let query = `
                SELECT 
                    id, user_id, msg_id, first_name, last_name, username,
                    group_id, group_title, media_group_id, media_type,
                    caption, date, created_at, raw_json
                FROM tg_log_media_group
                WHERE 1=1
            `;
            
            const params = [];
            
            // 添加搜索条件
            if (search) {
                query += ` AND (
                    first_name LIKE ? OR 
                    last_name LIKE ? OR 
                    username LIKE ? OR 
                    group_title LIKE ? OR 
                    caption LIKE ?
                )`;
                const searchPattern = `%${search}%`;
                params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
            }
            
            // 添加媒体类型过滤
            if (mediaType) {
                query += ` AND media_type = ?`;
                params.push(mediaType);
            }
            
            // 添加日期范围过滤
            if (dateFrom) {
                const fromTimestamp = Math.floor(new Date(dateFrom).getTime() / 1000);
                query += ` AND date >= ?`;
                params.push(fromTimestamp);
            }
            
            if (dateTo) {
                const toTimestamp = Math.floor(new Date(dateTo + ' 23:59:59').getTime() / 1000);
                query += ` AND date <= ?`;
                params.push(toTimestamp);
            }
            
            // 添加排序和分页
            query += ` ORDER BY date DESC, id DESC LIMIT ? OFFSET ?`;
            params.push(limit, offset);
            
            // 执行查询
            const records = await env.DB.prepare(query).bind(...params).all();
            
            // 获取统计信息
            const statsQuery = `
                SELECT 
                    COUNT(*) as total,
                    COUNT(DISTINCT media_group_id) as groups,
                    COUNT(CASE WHEN date >= ? THEN 1 END) as today
                FROM tg_log_media_group
            `;
            const todayTimestamp = Math.floor(new Date().setHours(0,0,0,0) / 1000);
            const stats = await env.DB.prepare(statsQuery).bind(todayTimestamp).first();
            
            // 返回数据
            const responseData = {
                records: records.results || [],
                stats: stats || { total: 0, groups: 0, today: 0 },
                pagination: {
                    limit,
                    offset,
                    hasMore: (records.results || []).length === limit
                }
            };
            
            Logger.debug('媒体组API查询成功', {
                recordCount: (records.results || []).length,
                searchParams: { search, mediaType, dateFrom, dateTo, limit, offset }
            });
            
            return new Response(JSON.stringify(responseData), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        return new Response('Method not allowed', { 
            status: 405, 
            headers: corsHeaders 
        });
        
    } catch (error) {
        Logger.error('媒体组API错误:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
} 