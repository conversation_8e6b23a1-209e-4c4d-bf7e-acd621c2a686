// #region 📦 依赖导入
import Logger from '../utils/logger.js';
import { handleStaticAssets } from './handlers/static.js';
import { handleAuthRequest } from './handlers/auth.js';
import { handleGroupConfigAPI } from './handlers/groupConfig.js';
import { handleMediaGroupsAPI } from './handlers/mediaGroups.js';
import { handleSubmissionsAPI, handlePublishAPI, handleRejectAPI } from './handlers/submissions.js';
import { handleFileProxyAPI } from './handlers/fileProxy.js';
import { handleAdsAPI } from './handlers/ads.js';
import { applyCorsHeaders } from './middleware/cors.js';
import { verifyAPIAuth } from './middleware/auth.js';
// #endregion 📦 依赖导入

// #region 🎯 主要处理函数

/**
 * 处理 Mini App 路由 - 统一入口
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleMiniApp(request, env, ctx) {
    const url = new URL(request.url);
    
    try {
        Logger.debug('Mini App 请求:', {
            method: request.method,
            pathname: url.pathname,
            origin: request.headers.get('origin')
        });

        // 处理静态资源
        if (url.pathname.startsWith('/miniapp/') && !url.pathname.startsWith('/miniapp/api/')) {
            return await handleStaticAssets(request, env, ctx);
        }

        // 处理 API 请求
        if (url.pathname.startsWith('/api/')) {
            return await handleApiRequest(request, env, ctx);
        }

        // 处理根目录图标
        if (url.pathname === '/icon.svg') {
            const assetRequest = new Request(`${url.origin}/icon.svg`);
            return env.ASSETS.fetch(assetRequest);
        }
        
        return new Response('Not Found', { status: 404 });
        
    } catch (error) {
        Logger.error('Mini App 处理错误:', error);
        return new Response('Internal Server Error', { 
            status: 500,
            headers: applyCorsHeaders(request)
        });
    }
}

/**
 * 处理 API 请求路由
 * @param {Request} request 请求对象
 * @param {Object} env 环境变量
 * @param {Object} ctx 上下文对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleApiRequest(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
        // 应用 CORS 头
        const corsHeaders = applyCorsHeaders(request);

        // 处理 OPTIONS 预检请求
        if (request.method === 'OPTIONS') {
            return new Response(null, {
                status: 204,
                headers: corsHeaders
            });
        }

        Logger.debug('API 请求路由:', {
            method: request.method,
            path,
            hasAuth: request.headers.has('Authorization')
        });

        // 认证相关 API（无需预认证）
        if (path === '/api/auth/verify' && request.method === 'POST') {
            return await handleAuthRequest(request, env, { ...ctx, corsHeaders });
        }

        // 需要认证的API路由
        const protectedRoutes = [
            '/api/group-config/',
            '/api/media-groups',
            '/api/submissions',
            '/api/publish',
            '/api/reject',
            '/api/file/',
            '/api/ads'
        ];

        const isProtectedRoute = protectedRoutes.some(route => 
            route.endsWith('/') ? path.startsWith(route) : (path === route || path.startsWith(route + '/'))
        );

        if (isProtectedRoute) {
            Logger.debug('开始认证验证:', { path, method: request.method });
            
            // 验证认证
            const authResult = await verifyAPIAuth(request, env);
            
            Logger.debug('认证验证结果:', { 
                success: authResult.success, 
                error: authResult.error,
                hasUser: !!authResult.user 
            });
            
            if (!authResult.success) {
                Logger.warn('API认证失败:', { path, error: authResult.error });
                return new Response(JSON.stringify({ 
                    success: false, 
                    error: '认证失败' 
                }), {
                    status: 404, // 返回404而不是401，隐藏API的存在
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                });
            }

            // 群组配置 API
            if (path.startsWith('/api/group-config/')) {
                return await handleGroupConfigAPI(request, env, { ...ctx, corsHeaders, user: authResult.user });
            }

            // 媒体组 API
            if (path === '/api/media-groups') {
                return await handleMediaGroupsAPI(request, env, { ...ctx, corsHeaders, user: authResult.user });
            }

            // 投稿管理 API
            if (path === '/api/submissions') {
                return await handleSubmissionsAPI(request, env, { ...ctx, corsHeaders, user: authResult.user });
            }

            if (path === '/api/publish') {
                return await handlePublishAPI(request, env, { ...ctx, corsHeaders, user: authResult.user });
            }

            if (path === '/api/reject') {
                return await handleRejectAPI(request, env, { ...ctx, corsHeaders, user: authResult.user });
            }

            // 文件代理 API
            if (path.startsWith('/api/file/')) {
                const fileId = path.replace('/api/file/', '');
                return await handleFileProxyAPI(request, env, { ...ctx, corsHeaders, user: authResult.user }, fileId);
            }

            // 广告管理 API
            if (path.startsWith('/api/ads/') || path === '/api/ads') {
                Logger.debug('调用广告管理API处理器:', { path });
                return await handleAdsAPI(request, env, { ...ctx, corsHeaders, user: authResult.user });
            }
            
            Logger.warn('未匹配到受保护的API路由:', { path });
        }

        Logger.warn('API端点未找到:', { 
            path, 
            method: request.method,
            isProtectedRoute: protectedRoutes.some(route => path.startsWith(route))
        });
        
        return new Response('API endpoint not found', { 
            status: 404, 
            headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
        });
        
    } catch (error) {
        Logger.error('API 请求处理错误:', error);
        const corsHeaders = applyCorsHeaders(request);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

// #endregion 🎯 主要处理函数

// #region 📤 导出函数
export { 
    handleStaticAssets, 
    handleAuthRequest, 
    handleGroupConfigAPI,
    handleMediaGroupsAPI,
    handleSubmissionsAPI,
    handlePublishAPI,
    handleRejectAPI,
    handleFileProxyAPI,
    handleAdsAPI
};
// #endregion 📤 导出函数 