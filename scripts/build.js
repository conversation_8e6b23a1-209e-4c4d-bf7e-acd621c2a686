#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 简单的版本号自动递增构建脚本
 * 实现版本号自动+0.0.1，每满10进位
 */

const VERSION_FILE = path.join(__dirname, '../version.json');
const ASSETS_DIR = path.join(__dirname, '../assets');

// 读取当前版本号
function readCurrentVersion() {
    try {
        if (fs.existsSync(VERSION_FILE)) {
            const versionData = JSON.parse(fs.readFileSync(VERSION_FILE, 'utf8'));
            return versionData.version || '0.1.0';
        }
    } catch (error) {
        console.log('版本文件读取失败，使用默认版本');
    }
    return '0.1.0';
}

// 版本号递增逻辑
function incrementVersion(currentVersion) {
    const parts = currentVersion.split('.').map(Number);
    let [major, minor, patch] = parts;
    
    // 补位版本号（确保是三位数）
    patch = patch || 0;
    minor = minor || 1;
    major = major || 0;
    
    // 递增补丁版本
    patch += 1;
    
    // 每满10进位
    if (patch >= 10) {
        patch = 0;
        minor += 1;
        
        if (minor >= 10) {
            minor = 0;
            major += 1;
        }
    }
    
    return `${major}.${minor}.${patch}`;
}

// 生成URL版本号（去掉点号）
function generateUrlVersion(version) {
    return version.replace(/\./g, '');
}

// 保存版本信息
function saveVersion(version) {
    const versionData = {
        version: version,
        buildTime: new Date().toISOString(),
        urlVersion: generateUrlVersion(version)
    };
    
    fs.writeFileSync(VERSION_FILE, JSON.stringify(versionData, null, 2), 'utf8');
    return versionData;
}

// 更新HTML文件中的版本号
function updateHtmlFiles(version, urlVersion) {
    const updatedFiles = [];
    
    function processDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                processDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                if (updateHtmlFile(fullPath, version, urlVersion)) {
                    updatedFiles.push(path.relative(ASSETS_DIR, fullPath));
                }
            }
        }
    }
    
    function updateHtmlFile(filePath, version, urlVersion) {
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;
        
        // 更新版本号显示（在version-badge中）
        content = content.replace(
            /(<div class="version-badge"[^>]*>[\s\S]*?)v[\d.]+/g,
            `$1v${version}`
        );
        
        // 更新CSS和JS文件的版本参数
        content = content
            .replace(/(<link[^>]*href="[^"]*\.css)(\?v=[^"'\s&]+)?(")/g, `$1?v=${urlVersion}$3`)
            .replace(/(<script[^>]*src="[^"]*\.js)(\?v=[^"'\s&]+)?(")/g, `$1?v=${urlVersion}$3`);
        
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            return true;
        }
        return false;
    }
    
    if (fs.existsSync(ASSETS_DIR)) {
        processDirectory(ASSETS_DIR);
    }
    
    return updatedFiles;
}

// 主要构建函数
function build() {
    console.log('🔄 开始构建...');
    
    // 读取当前版本
    const currentVersion = readCurrentVersion();
    console.log(`📖 当前版本: ${currentVersion}`);
    
    // 递增版本号
    const newVersion = incrementVersion(currentVersion);
    const urlVersion = generateUrlVersion(newVersion);
    console.log(`🆙 新版本: ${newVersion} (URL版本: ${urlVersion})`);
    
    // 保存版本信息
    const versionData = saveVersion(newVersion);
    console.log(`💾 版本信息已保存到 ${path.relative(process.cwd(), VERSION_FILE)}`);
    
    // 更新HTML文件
    const updatedFiles = updateHtmlFiles(newVersion, urlVersion);
    
    if (updatedFiles.length > 0) {
        console.log('✅ 已更新的文件:');
        updatedFiles.forEach(file => console.log(`   - ${file}`));
    } else {
        console.log('ℹ️  没有找到需要更新的HTML文件');
    }
    
    console.log(`🚀 构建完成！版本: v${newVersion}`);
    console.log(`📅 构建时间: ${new Date().toLocaleString()}`);
    
    return versionData;
}

// 如果直接运行此脚本
if (require.main === module) {
    try {
        build();
    } catch (error) {
        console.error('❌ 构建失败:', error.message);
        process.exit(1);
    }
}

module.exports = { build, incrementVersion, generateUrlVersion }; 