/**
 * 将 SVG 文件转换为 JavaScript 模块
 * 这样可以在 CF Workers 中直接导入使用
 */

import fs from 'fs';
import path from 'path';

const SVG_DIR = 'src/svg';
const OUTPUT_DIR = 'src/icons';

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 从 SVG 文件提取路径数据
 */
function extractPathFromSVG(svgContent) {
    // 提取 <path d="..."> 中的路径数据
    const pathRegex = /<path[^>]*d="([^"]*)"[^>]*>/g;
    const paths = [];
    let match;
    
    while ((match = pathRegex.exec(svgContent)) !== null) {
        // 跳过空路径和变换路径
        if (match[1] && !match[1].includes('M0 0h24v24H0z')) {
            paths.push(match[1]);
        }
    }
    
    // 检查是否有 stroke 属性
    const hasStroke = svgContent.includes('stroke="currentColor"');
    const hasFill = svgContent.includes('fill="currentColor"');
    
    return {
        paths,
        type: hasStroke ? 'stroke' : 'fill'
    };
}

/**
 * 生成数字图标模块
 */
function generateDigitIcons() {
    const digits = {};
    
    for (let i = 0; i <= 9; i++) {
        const filename = `hexagon-number-${i}.svg`;
        const filepath = path.join(SVG_DIR, filename);
        
        if (fs.existsSync(filepath)) {
            const content = fs.readFileSync(filepath, 'utf8');
            const { paths } = extractPathFromSVG(content);
            digits[i.toString()] = paths[0]; // 取第一个主要路径
        }
    }
    
    const moduleContent = `/**
 * 六边形数字图标路径 (来自 Tabler Icons)
 * 自动生成，请勿手动修改
 */

export const DIGIT_PATHS = ${JSON.stringify(digits, null, 4)};

export function generateDigitPath(digit, x, y, scale = 1, color = '#0066cc') {
    const pathData = DIGIT_PATHS[digit];
    if (!pathData) return '';
    
    return \`<g transform="translate(\${x}, \${y}) scale(\${scale})">
        <path d="\${pathData}" fill="\${color}"/>
    </g>\`;
}
`;
    
    fs.writeFileSync(path.join(OUTPUT_DIR, 'digits.js'), moduleContent);
    console.log('✅ 生成数字图标模块: src/icons/digits.js');
}

/**
 * 生成字母图标模块
 */
function generateLetterIcons() {
    const letters = {};
    const letterList = ['a', 'b', 'c', 'd'];
    
    letterList.forEach(letter => {
        const filename = `square-rounded-letter-${letter}.svg`;
        const filepath = path.join(SVG_DIR, filename);
        
        if (fs.existsSync(filepath)) {
            const content = fs.readFileSync(filepath, 'utf8');
            const { paths } = extractPathFromSVG(content);
            letters[letter.toUpperCase()] = paths[0];
        }
    });
    
    const moduleContent = `/**
 * 圆角方形字母图标路径 (来自 Tabler Icons)
 * 自动生成，请勿手动修改
 */

export const LETTER_PATHS = ${JSON.stringify(letters, null, 4)};

export function generateLetterPath(letter, x, y, scale = 1, color = '#28a745') {
    const pathData = LETTER_PATHS[letter];
    if (!pathData) return '';
    
    return \`<g transform="translate(\${x}, \${y}) scale(\${scale})">
        <path d="\${pathData}" fill="\${color}"/>
    </g>\`;
}
`;
    
    fs.writeFileSync(path.join(OUTPUT_DIR, 'letters.js'), moduleContent);
    console.log('✅ 生成字母图标模块: src/icons/letters.js');
}

/**
 * 生成操作符图标模块
 */
function generateOperatorIcons() {
    const operators = {};
    const operatorFiles = {
        '+': 'plus.svg',
        '-': 'minus.svg',
        '=': 'equal.svg',
        '?': 'question-mark.svg',
        '×': 'x.svg',
        '÷': 'divide.svg'
    };
    
    Object.entries(operatorFiles).forEach(([symbol, filename]) => {
        const filepath = path.join(SVG_DIR, filename);
        
        if (fs.existsSync(filepath)) {
            const content = fs.readFileSync(filepath, 'utf8');
            const { paths, type } = extractPathFromSVG(content);
            operators[symbol] = { paths, type };
        }
    });
    
    const moduleContent = `/**
 * 操作符图标路径 (来自 Tabler Icons)
 * 自动生成，请勿手动修改
 */

export const OPERATOR_PATHS = ${JSON.stringify(operators, null, 4)};

export function generateOperatorPath(operator, x, y, scale = 1, color = '#0066cc') {
    const opData = OPERATOR_PATHS[operator];
    if (!opData) return '';
    
    let paths = '';
    opData.paths.forEach(path => {
        if (opData.type === 'stroke') {
            paths += \`<path d="\${path}" stroke="\${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>\`;
        } else {
            paths += \`<path d="\${path}" fill="\${color}"/>\`;
        }
    });
    
    return \`<g transform="translate(\${x}, \${y}) scale(\${scale})">
        \${paths}
    </g>\`;
}
`;
    
    fs.writeFileSync(path.join(OUTPUT_DIR, 'operators.js'), moduleContent);
    console.log('✅ 生成操作符图标模块: src/icons/operators.js');
}

/**
 * 生成统一的入口模块
 */
function generateIndexModule() {
    const moduleContent = `/**
 * Tabler Icons 统一入口模块
 * 自动生成，请勿手动修改
 */

export { DIGIT_PATHS, generateDigitPath } from './digits.js';
export { LETTER_PATHS, generateLetterPath } from './letters.js';
export { OPERATOR_PATHS, generateOperatorPath } from './operators.js';

/**
 * 生成任意字符的路径
 */
export function generateCharacterPath(char, x, y, scale = 1, color = '#0066cc') {
    // 数字处理
    if (/[0-9]/.test(char)) {
        return generateDigitPath(char, x, y, scale, color);
    }
    
    // 字母处理
    if (/[A-D]/.test(char)) {
        return generateLetterPath(char, x, y, scale, color);
    }
    
    // 操作符处理
    if (/[+\\-=?×÷]/.test(char)) {
        return generateOperatorPath(char, x, y, scale, color);
    }
    
    // 未找到字符，返回占位符
    return \`<g transform="translate(\${x}, \${y}) scale(\${scale})">
        <circle cx="12" cy="12" r="8" fill="none" stroke="\${color}" stroke-width="2"/>
        <text x="12" y="16" text-anchor="middle" fill="\${color}" font-size="10">\${char}</text>
    </g>\`;
}
`;
    
    fs.writeFileSync(path.join(OUTPUT_DIR, 'index.js'), moduleContent);
    console.log('✅ 生成统一入口模块: src/icons/index.js');
}

// 执行转换
console.log('🔄 开始转换 SVG 文件为 JavaScript 模块...\n');

generateDigitIcons();
generateLetterIcons();
generateOperatorIcons();
generateIndexModule();

console.log('\n✨ 转换完成！现在可以使用 ES 模块导入：');
console.log('  import { generateCharacterPath } from "./icons/index.js";'); 