{"name": "tgbot", "version": "0.0.0", "private": true, "scripts": {"triggers": "wrangler triggers deploy", "tail": "wrangler tail --format pretty > wrangler-logs.txt", "build": "node scripts/build.js", "prod": "npm run build && wrangler deploy", "dev": "wrangler dev --ip=0.0.0.0 --inspector-port=9229", "start": "wrangler dev", "test": "vitest"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "vitest": "~3.0.7", "wrangler": "^4.16.1"}, "dependencies": {"@cf-wasm/resvg": "^0.1.24"}}