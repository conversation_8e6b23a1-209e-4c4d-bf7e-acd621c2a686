-- 性能优化索引迁移
-- 创建时间：2025-01-20
-- 用途：解决 D1 数据库配额消耗过大问题，优化查询性能

-- #region 📊 tg_log_media_group 表索引优化
-- 1. 主要查询优化：按日期和ID排序的复合索引（解决全表扫描）
CREATE INDEX IF NOT EXISTS idx_media_group_date_id ON tg_log_media_group (date DESC, id DESC);

-- 2. 媒体组查询优化：媒体组ID + 日期复合索引
CREATE INDEX IF NOT EXISTS idx_media_group_combined ON tg_log_media_group (media_group_id, date DESC);

-- 3. 用户查询优化：用户ID + 日期复合索引
CREATE INDEX IF NOT EXISTS idx_media_group_user_date ON tg_log_media_group (user_id, date DESC);

-- 4. 群组查询优化：群组ID + 日期复合索引  
CREATE INDEX IF NOT EXISTS idx_media_group_group_date ON tg_log_media_group (group_id, date DESC);
-- #endregion

-- #region 📝 tg_log_forwarded_submissions 表索引优化
-- 5. 转发记录查询优化：媒体组ID + 话题ID + 日期复合索引
CREATE INDEX IF NOT EXISTS idx_forwarded_media_group_enhanced ON tg_log_forwarded_submissions (media_group_id, sub_topic_id, date);

-- 6. 清理查询专用索引：话题6的清理操作优化
CREATE INDEX IF NOT EXISTS idx_forwarded_cleanup_topic6 ON tg_log_forwarded_submissions (sub_topic_id, date) WHERE sub_topic_id = 6;

-- 7. 投稿消息查询优化：投稿消息ID + 话题ID复合索引
CREATE INDEX IF NOT EXISTS idx_forwarded_sub_msg_topic ON tg_log_forwarded_submissions (sub_message_id, sub_topic_id);

-- 8. 原始消息查询优化：原始消息ID + 群组ID + 日期复合索引
CREATE INDEX IF NOT EXISTS idx_forwarded_original_msg ON tg_log_forwarded_submissions (msg_id, group_id, date);
-- #endregion

-- #region 🚀 tg_forward_media_queue 表索引优化
-- 9. 队列处理优化：状态 + 处理时间复合索引
CREATE INDEX IF NOT EXISTS idx_queue_status_process_time ON tg_forward_media_queue (status, process_after);

-- 10. 队列清理优化：状态 + 创建时间复合索引
CREATE INDEX IF NOT EXISTS idx_queue_cleanup ON tg_forward_media_queue (status, created_at);
-- #endregion

-- #region 📊 性能统计说明
-- 预期效果：
-- - 全表扫描查询：22.86k → ~50 rows (节省 99.8%)
-- - media_group_id 查询：22.8k → ~500 rows (节省 97.8%)  
-- - DELETE 操作：7.06k → ~200 rows (节省 97.2%)
-- - 总体节省：约 98.6% 的读取量，释放约 2.6M 配额空间
-- #endregion 