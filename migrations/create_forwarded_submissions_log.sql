-- #region 📝 投稿转发系统数据库架构
  -- #region 🗃️ 主表创建 - 投稿转发消息记录表
  -- 创建已转发到投稿处理群的消息记录表
  CREATE TABLE IF NOT EXISTS tg_log_forwarded_submissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL, -- 原信息发送者id
    msg_id INTEGER NOT NULL, -- 原信息的信息id
    first_name TEXT, -- 原信息发送者的名字
    last_name TEXT,
    username TEXT,
    group_id INTEGER NOT NULL, -- 原信息来源group id
    group_title TEXT, -- 原信息来源group名字
    media_group_id TEXT, -- 原信息媒体组id（判断如果是媒体组的话）
    media_type TEXT, -- 原信息类型
    media_content TEXT, -- 原信息内容
    caption TEXT, -- 原信息说明
    caption_entities TEXT,
    forward_from_id TEXT, -- 原信息转发来源id（判断有的话）
    forward_from_title TEXT, -- 原信息转发来源名字（判断有的话）
    raw_json TEXT NOT NULL, -- 原始数据
    sub_message_id INTEGER, -- 该消息在投稿群里的ID
    sub_topic_id INTEGER, -- 投稿话题ID
    date INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  -- #endregion 🗃️ 主表创建

  -- #region 🚀 性能优化 - 索引创建
    -- #region 🔑 唯一性约束
    -- 创建唯一约束和索引
    CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_forwarded_msg_group 
    ON tg_log_forwarded_submissions(msg_id, group_id);
    -- #endregion 🔑 唯一性约束

    -- #region 🔍 查询优化索引
    CREATE INDEX IF NOT EXISTS idx_forwarded_media_group_id 
    ON tg_log_forwarded_submissions(media_group_id);

    CREATE INDEX IF NOT EXISTS idx_forwarded_sub_message_id 
    ON tg_log_forwarded_submissions(sub_message_id);

    CREATE INDEX IF NOT EXISTS idx_forwarded_sub_topic_id 
    ON tg_log_forwarded_submissions(sub_topic_id);

    CREATE INDEX IF NOT EXISTS idx_forwarded_user_msg 
    ON tg_log_forwarded_submissions(user_id, msg_id);

    CREATE INDEX IF NOT EXISTS idx_forwarded_group_id 
    ON tg_log_forwarded_submissions(group_id);

    CREATE INDEX IF NOT EXISTS idx_forwarded_user_id 
    ON tg_log_forwarded_submissions(user_id);
    -- #endregion 🔍 查询优化索引
  -- #endregion 🚀 性能优化
-- #endregion 📝 投稿转发系统数据库架构 