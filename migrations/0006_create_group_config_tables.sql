-- 群配置和举报功能迁移
-- 创建时间：2025-01-21
-- 用途：支持群组配置管理和举报功能

-- #region 📊 群组配置主表
-- 群组配置主表
CREATE TABLE IF NOT EXISTS group_configs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chat_id TEXT NOT NULL UNIQUE,           -- 群组ID，唯一索引
  chat_title TEXT,                        -- 群组标题（用于显示）
  
  -- 举报功能配置
  report_mode TEXT NOT NULL DEFAULT 'disabled',  -- 举报模式: 'disabled'|'private'|'group'
  report_target_groups TEXT DEFAULT '[]', -- JSON数组存储目标群组 [{chat_id, title, is_active, added_at}]
  
  -- 功能开关
  new_member_verification BOOLEAN DEFAULT TRUE,  -- 新人验证开关
  cloud_filter_enabled BOOLEAN DEFAULT TRUE,     -- 云过滤开关
  auto_forward_enabled BOOLEAN DEFAULT TRUE,     -- 自动转发开关
  
  -- 元数据
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX IF NOT EXISTS idx_group_configs_chat_id ON group_configs (chat_id);
CREATE INDEX IF NOT EXISTS idx_group_configs_report_mode ON group_configs (report_mode) WHERE report_mode != 'disabled';
-- #endregion 📊 群组配置主表

-- #region 📝 群规配置表
-- 群规配置表 - 存储群规链接和设置信息
CREATE TABLE IF NOT EXISTS group_rules (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chat_id TEXT NOT NULL,                  -- 群组ID
  rule_urls TEXT NOT NULL DEFAULT '[]',   -- 群规URL数组 JSON格式: [{"url": "https://t.me/c/1143091022/1", "title": "群规"}]
  set_by_user_id INTEGER NOT NULL,        -- 设置人用户ID
  set_by_user_name TEXT,                  -- 设置人姓名
  is_active BOOLEAN DEFAULT TRUE,         -- 是否启用
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (chat_id) REFERENCES group_configs(chat_id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_group_rules_chat ON group_rules (chat_id, is_active);
CREATE INDEX IF NOT EXISTS idx_group_rules_setter ON group_rules (set_by_user_id);
-- #endregion 📝 群规配置表

-- #region 📋 举报记录表
-- 举报记录表（工单跟踪系统）
CREATE TABLE IF NOT EXISTS report_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chat_id TEXT NOT NULL,                  -- 源群组ID
  reporter_id INTEGER NOT NULL,           -- 举报者ID
  reporter_name TEXT,                     -- 举报者姓名
  target_message_id INTEGER,              -- 被举报的消息ID（可选）
  report_reason TEXT,                     -- 举报原因
  report_content TEXT,                    -- 举报内容快照
  
  -- 分发状态
  report_mode TEXT NOT NULL,              -- 处理方式: 'private'|'group'
  notification_targets TEXT,              -- 通知目标（JSON格式）
  notification_status TEXT DEFAULT 'pending', -- 通知状态: 'pending'|'sent'|'failed'
  
  -- 处理状态（管理员操作）
  handle_status TEXT DEFAULT 'pending',   -- 处理状态: 'pending'|'handled'|'ignored'|'escalated'
  handled_by INTEGER,                     -- 处理人ID
  handled_by_name TEXT,                   -- 处理人姓名
  handle_action TEXT,                     -- 处理动作: 'warned'|'muted'|'banned'|'dismissed'
  handle_comment TEXT,                    -- 处理备注
  handled_at TIMESTAMP,                   -- 处理时间
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX IF NOT EXISTS idx_report_logs_chat ON report_logs (chat_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_report_logs_reporter ON report_logs (reporter_id);
CREATE INDEX IF NOT EXISTS idx_report_logs_status ON report_logs (handle_status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_report_logs_notification ON report_logs (notification_status, report_mode);
-- #endregion 📋 举报记录表

-- #region 🛡️ 真人验证相关表
-- 用户全局验证记录表（一次验证，全局生效）
CREATE TABLE IF NOT EXISTS user_verification_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL UNIQUE,        -- 用户ID（全局唯一）
  user_name TEXT,                         -- 用户姓名
  first_verified_in_chat TEXT,            -- 首次验证的群组ID（用于记录）
  is_verified BOOLEAN DEFAULT FALSE,      -- 是否已通过真人验证
  failed_attempts INTEGER DEFAULT 0,      -- 历史总失败次数
  last_attempt_at TIMESTAMP,              -- 最后尝试时间
  verified_at TIMESTAMP,                  -- 验证通过时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_user_verification_user_id ON user_verification_records (user_id);
CREATE INDEX IF NOT EXISTS idx_user_verification_verified ON user_verification_records (is_verified);
-- #endregion 🛡️ 真人验证相关表

-- #region 📊 性能优化说明
-- 预期效果：
-- - JSON数组存储节省75%读取配额（相比传统关联表方案）
-- - 单次查询获取完整群组配置
-- - 索引设计支持高效的状态筛选和时间范围查询
-- - 外键级联删除保证数据一致性
-- - 全局真人验证：一次验证全群生效，避免重复验证
-- - 验证会话完全基于内存：利用CF Worker wall time最新的无限制特性，节省D1配额
-- - 28秒验证时限设计：即使在极端30秒限制情况下也有充足时间缓冲
-- #endregion 📊 性能优化说明 