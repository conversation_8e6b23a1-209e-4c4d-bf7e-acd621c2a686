-- 为ad_posts表添加publish_time字段
-- 创建时间：2025-01-20
-- 用途：支持同一规则在不同时间段的独立发送频率控制

-- 添加publish_time字段，用于记录每次发送时对应的设定时间
ALTER TABLE ad_posts ADD COLUMN publish_time TEXT;

-- 为新字段添加索引，提高查询性能
CREATE INDEX IF NOT EXISTS idx_ad_posts_campaign_time ON ad_posts (campaign_id, publish_time, sent_at DESC);

-- 更新现有记录，将publish_time设置为对应规则的发送时间
-- 这样可以保证历史数据的兼容性
UPDATE ad_posts 
SET publish_time = (
  SELECT publish_time 
  FROM ad_campaigns 
  WHERE ad_campaigns.id = ad_posts.campaign_id
) 
WHERE publish_time IS NULL; 