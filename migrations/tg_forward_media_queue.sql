-- 媒体组转发队列表
CREATE TABLE IF NOT EXISTS "tg_forward_media_queue" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "media_group_id" TEXT NOT NULL,
    "chat_id" TEXT NOT NULL,
    "from_id" INTEGER,
    "created_at" INTEGER NOT NULL,
    "updated_at" INTEGER NOT NULL,
    "status" TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed')),
    "process_after" INTEGER NOT NULL
);

-- 创建索引以加速查询
CREATE INDEX IF NOT EXISTS "idx_forward_media_queue_media_group_id" ON "tg_forward_media_queue" ("media_group_id");
CREATE INDEX IF NOT EXISTS "idx_forward_media_queue_status" ON "tg_forward_media_queue" ("status");
CREATE INDEX IF NOT EXISTS "idx_forward_media_queue_process_after" ON "tg_forward_media_queue" ("process_after");