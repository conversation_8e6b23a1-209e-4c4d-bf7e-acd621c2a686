-- 群组管理员权限表迁移
-- 创建时间：2025-01-21
-- 用途：管理群组管理员权限和层级关系

-- #region 👥 群组管理员权限表
-- 群组管理员权限表 - 存储用户在各群组中的管理权限
CREATE TABLE IF NOT EXISTS group_admins (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chat_id TEXT NOT NULL,                  -- 群组ID
  user_id INTEGER NOT NULL,               -- 用户ID  
  user_name TEXT,                         -- 用户姓名（用于显示）
  permission_level TEXT NOT NULL,         -- 权限级别: 'super_admin'(超级管理) | 'admin'(普通管理)
  granted_by INTEGER,                     -- 授权人用户ID（超级管理员授权的普通管理员时使用）
  granted_by_name TEXT,                   -- 授权人姓名
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- 复合唯一索引：同一群组同一用户只能有一条记录
  UNIQUE(chat_id, user_id),
  
  -- 外键约束
  FOREIGN KEY (chat_id) REFERENCES group_configs(chat_id) ON DELETE CASCADE
);

-- 索引优化
CREATE INDEX IF NOT EXISTS idx_group_admins_chat_id ON group_admins (chat_id);
CREATE INDEX IF NOT EXISTS idx_group_admins_user_id ON group_admins (user_id);
CREATE INDEX IF NOT EXISTS idx_group_admins_permission ON group_admins (permission_level);
CREATE INDEX IF NOT EXISTS idx_group_admins_granted_by ON group_admins (granted_by);
CREATE INDEX IF NOT EXISTS idx_group_admins_chat_permission ON group_admins (chat_id, permission_level);
-- #endregion 👥 群组管理员权限表

-- #region 📊 权限说明
-- 权限级别说明：
-- - super_admin: 超级管理员（群组创建者或有添加管理员权限的用户）
--   * 可以添加、删除群组配置
--   * 可以添加其他用户为普通管理员
--   * 可以修改所有群组设置
-- - admin: 普通管理员（由超级管理员添加）
--   * 可以修改群组配置
--   * 不能添加其他管理员
--   * 不能删除群组配置
-- #endregion 📊 权限说明 