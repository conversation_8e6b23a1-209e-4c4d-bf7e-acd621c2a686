-- 添加级联删除功能
-- 创建时间：2025-01-20
-- 用途：删除广告规则时自动删除相关的发送记录

-- 由于SQLite不支持直接修改外键约束，需要重建表

-- 1. 创建新的ad_posts表，带有CASCADE删除
CREATE TABLE ad_posts_new (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  campaign_id INTEGER NOT NULL,         -- 关联的广告规则ID
  target_channel_id TEXT NOT NULL,      -- 目标频道ID
  source_message_id INTEGER NOT NULL,   -- 源消息ID
  sent_message_id INTEGER NOT NULL,     -- 发送后的消息ID
  sent_at TIMESTAMP NOT NULL,           -- 发送时间
  publish_time TEXT,                    -- 发送时对应的设定时间
  is_deleted BOOLEAN DEFAULT FALSE,     -- 是否已删除
  deleted_at TIMESTAMP,                 -- 删除时间
  FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id) ON DELETE CASCADE
);

-- 2. 复制数据
INSERT INTO ad_posts_new 
SELECT id, campaign_id, target_channel_id, source_message_id, sent_message_id, 
       sent_at, publish_time, is_deleted, deleted_at 
FROM ad_posts;

-- 3. 删除旧表
DROP TABLE ad_posts;

-- 4. 重命名新表
ALTER TABLE ad_posts_new RENAME TO ad_posts;

-- 5. 重建索引
CREATE INDEX IF NOT EXISTS idx_ad_posts_campaign ON ad_posts (campaign_id, sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_ad_posts_target_channel ON ad_posts (target_channel_id, source_message_id, is_deleted);
CREATE INDEX IF NOT EXISTS idx_ad_posts_campaign_time ON ad_posts (campaign_id, publish_time, sent_at DESC); 