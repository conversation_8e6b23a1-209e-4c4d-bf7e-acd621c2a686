-- 创建媒体组记录表
CREATE TABLE IF NOT EXISTS tg_log_media_group (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  msg_id INTEGER NOT NULL,
  first_name TEXT,
  last_name TEXT,
  username TEXT,
  group_id INTEGER NOT NULL,
  group_title TEXT,
  media_group_id TEXT,
  media_type TEXT,
  media_content TEXT,
  caption TEXT,
  caption_entities TEXT,
  forward_from_chat TEXT,
  raw_json TEXT NOT NULL,
  date INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_media_group_id ON tg_log_media_group (media_group_id);
CREATE INDEX IF NOT EXISTS idx_group_id ON tg_log_media_group (group_id);
CREATE INDEX IF NOT EXISTS idx_user_id ON tg_log_media_group (user_id);