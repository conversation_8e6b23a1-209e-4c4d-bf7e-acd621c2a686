-- 广告投放功能相关表
-- 创建时间：2025-01-20

-- 广告规则表
CREATE TABLE IF NOT EXISTS ad_campaigns (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                    -- 广告规则名称
  target_channel_id TEXT NOT NULL,       -- 目标频道ID
  source_channel_id TEXT DEFAULT '-1001549390517', -- 广告源频道ID（默认为指定的广告频道）
  source_message_id INTEGER NOT NULL,    -- 源消息ID
  start_date DATE NOT NULL,              -- 开始日期
  end_date DATE NOT NULL,                -- 结束日期
  frequency_days INTEGER NOT NULL,       -- 发布频率间隔天数（0=每天，1=隔1天，2=隔2天...）
  publish_time TEXT NOT NULL,            -- 发布时间 (HH:MM)
  is_pin BOOLEAN DEFAULT FALSE,          -- 是否置顶
  is_active BOOLEAN DEFAULT TRUE,        -- 是否启用
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 广告发送记录表
CREATE TABLE IF NOT EXISTS ad_posts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  campaign_id INTEGER NOT NULL,         -- 关联的广告规则ID
  target_channel_id TEXT NOT NULL,      -- 目标频道ID
  source_message_id INTEGER NOT NULL,   -- 源消息ID
  sent_message_id INTEGER NOT NULL,     -- 发送后的消息ID
  sent_at TIMESTAMP NOT NULL,           -- 发送时间
  is_deleted BOOLEAN DEFAULT FALSE,     -- 是否已删除
  deleted_at TIMESTAMP,                 -- 删除时间
  FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id)
);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_active ON ad_campaigns (is_active, start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_target_channel ON ad_campaigns (target_channel_id);
CREATE INDEX IF NOT EXISTS idx_ad_posts_campaign ON ad_posts (campaign_id, sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_ad_posts_target_channel ON ad_posts (target_channel_id, source_message_id, is_deleted); 