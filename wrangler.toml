# Cloudflare Workers配置文件
# 详细文档: https://developers.cloudflare.com/workers/wrangler/configuration/

name = "tgbot"
main = "src/index.js"
compatibility_date = "2025-05-25"

# 可观测性配置
[observability]
enabled = true

# Workers Assets配置 - 用于静态资源
[assets]
directory = "./assets"
binding = "ASSETS"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "telegram-db-eu"
database_id = "24da0704-79b8-4bde-8328-dae0ad5323d0"

# KV 命名空间配置
[[kv_namespaces]]
binding = "VERIFICATION_KV"
id = "3fcceeebd42c4d1093a8e8619149d560"
preview_id = "85791f366d9d4aec97cb9d419c0a2aa7"

# 队列配置（需要付费计划，暂时禁用，使用降级方案）
# [[queues.producers]]
# queue = "verification-cleanup-queue"
# binding = "VERIFICATION_CLEANUP_QUEUE"

# [[queues.consumers]]
# queue = "verification-cleanup-queue"
# max_batch_size = 10
# max_batch_timeout = 5

# Durable Objects 配置
[[durable_objects.bindings]]
name = "TELEGRAM_RATE_LIMITER"
class_name = "TelegramRateLimiter"

[[durable_objects.bindings]]
name = "KEY_VALUE_STORE"
class_name = "KeyValueStore"

[[durable_objects.bindings]]
name = "VERIFICATION_TIMER"
class_name = "VerificationTimer"

[[durable_objects.bindings]]
name = "MESSAGE_SCHEDULER"
class_name = "MessageScheduler"

# 迁移配置
[[migrations]]
tag = "v1"
new_sqlite_classes = ["TelegramRateLimiter"]

[[migrations]]
tag = "v2"
new_sqlite_classes = ["KeyValueStore"]

[[migrations]]
tag = "v3"
new_sqlite_classes = ["VerificationTimer"]

[[migrations]]
tag = "v4"
new_sqlite_classes = ["MessageScheduler"]

# 环境变量配置
# 本地开发：通过 .dev.vars 文件提供
# 生产环境：通过 wrangler secret put 命令设置 secrets
[vars]
# 这里不要定义 TELEGRAM_BOT_TOKEN 和 TELEGRAM_BOT_USERNAME
# 避免与 secrets 冲突
LOG_LEVEL = "DEBUG"

[env.production]
[env.production.vars]
LOG_LEVEL = "DEBUG"

# 开发环境配置
[dev]
port = 8787
host = "0.0.0.0"
local_protocol = "http"

# 定时任务触发器 - 广告投放管理和投稿记录清理
[triggers]
crons = ["*/15 * * * *"]

# 自定义域名路由（如果需要）
# [[routes]]
# pattern = "your-custom-domain.com/*"
# zone_name = "your-custom-domain.com"

# Smart Placement配置（可选）
# [placement]
# mode = "smart"

# Service Bindings（如果需要与其他Workers通信）
# [[services]]
# binding = "MY_SERVICE"
# service = "my-service" 