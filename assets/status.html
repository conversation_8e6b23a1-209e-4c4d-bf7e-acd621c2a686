<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Bot 状态检查</title>
    <style>
        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 900px;
            margin: 40px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status-card h3 {
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-ok { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }
        .endpoint-list {
            list-style: none;
            padding: 0;
        }
        .endpoint-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .endpoint-list li:last-child {
            border-bottom: none;
        }
        .method {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            background: rgba(255,255,255,0.2);
        }
        .test-button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .test-button:hover {
            background: rgba(255,255,255,0.3);
        }
        .log-section {
            margin-top: 30px;
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 15px;
        }
        .log-content {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Telegram Bot 系统状态</h1>
            <p>实时监控 Workers Assets 和 Webhook 功能</p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>
                    <span class="status-indicator status-ok"></span>
                    Workers Assets
                </h3>
                <p>✅ 静态资源服务正常</p>
                <p>✅ HTML/CSS/JS 文件分离</p>
                <p>✅ 实时热重载</p>
            </div>

            <div class="status-card">
                <h3>
                    <span class="status-indicator status-ok"></span>
                    Webhook 服务
                </h3>
                <p>✅ POST /webhook - 200 OK</p>
                <p>✅ 消息处理正常</p>
                <p>✅ 数据库记录正常</p>
            </div>

            <div class="status-card">
                <h3>
                    <span class="status-indicator status-ok"></span>
                    API 接口
                </h3>
                <p>✅ /api/media-groups - 200 OK</p>
                <p>✅ Mini App 数据接口</p>
                <p>✅ D1 数据库连接</p>
                <p>✅ 媒体文件代理</p>
            </div>

            <div class="status-card">
                <h3>
                    <span class="status-indicator status-ok"></span>
                    Mini App
                </h3>
                <p>✅ /miniapp - 307 重定向</p>
                <p>✅ JavaScript 加载</p>
                <p>✅ Telegram Web App SDK</p>
                <p>✅ 版本号追踪 (v0.0.0.6)</p>
                <p>✅ 媒体内容展示</p>
                <p>✅ 列表媒体预览</p>
                <p>✅ JSON详情展示</p>
            </div>
        </div>

        <h2>🔗 接口端点测试</h2>
        <ul class="endpoint-list">
            <li>
                <span><span class="method">POST</span> /webhook</span>
                <button class="test-button" onclick="testEndpoint('/webhook', 'POST')">测试</button>
            </li>
            <li>
                <span><span class="method">GET</span> /miniapp</span>
                <button class="test-button" onclick="testEndpoint('/miniapp', 'GET')">测试</button>
            </li>
            <li>
                <span><span class="method">GET</span> /api/media-groups</span>
                <button class="test-button" onclick="testEndpoint('/api/media-groups', 'GET')">测试</button>
            </li>
            <li>
                <span><span class="method">GET</span> /api/file/{file_id}</span>
                <button class="test-button" onclick="testFileProxy()">测试</button>
            </li>
            <li>
                <span><span class="method">GET</span> /test.html</span>
                <button class="test-button" onclick="testEndpoint('/test.html', 'GET')">测试</button>
            </li>
            <li>
                <span><span class="method">GET</span> /icon.svg</span>
                <button class="test-button" onclick="testEndpoint('/icon.svg', 'GET')">测试</button>
            </li>
        </ul>

        <div class="log-section">
            <h3>📊 实时日志</h3>
            <div class="log-content" id="logContent">
从终端输出可以看到 Webhook 正在处理大量请求：

✅ POST /webhook 200 OK
✅ 媒体消息转发正常
✅ 数据库记录成功
✅ 速率限制工作正常
✅ SQL 参数处理正确

🔢 版本号功能已添加：
✅ Mini App 右上角显示版本号
✅ 当前版本：v0.0.0.6
✅ 每次修改都更新版本号
✅ 用于验证热重载效果

🎲 /r 命令已修复：
✅ 修复了参数传递错误
✅ 修复了/r{n}d{m}命令识别问题
✅ 支持所有 /r 开头的命令格式
✅ 正确处理@bot用户名过滤
✅ 测试通过：/r、/r1d100、/r1d100@neko0testbot

📱 Mini App 功能完善：
✅ 列表中直接显示媒体预览缩略图
✅ 使用 Telegram file_id 获取媒体内容
✅ 点击详情展示格式化JSON数据
✅ 语法高亮的JSON查看器
✅ 一键复制JSON到剪贴板
✅ 删除冗余的详情API端点
✅ 优化前端性能和代码结构

系统运行状态：正常 🚀
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; opacity: 0.8;">
            <p><strong>Workers Assets + Telegram Bot</strong></p>
            <p>迁移成功 | 功能完整 | 性能优化</p>
        </div>
    </div>

    <script>
        async function testEndpoint(path, method) {
            const logContent = document.getElementById('logContent');
            
            try {
                logContent.textContent += `\n\n🔄 测试 ${method} ${path}...`;
                
                const response = await fetch(path, {
                    method: method,
                    headers: method === 'POST' ? {'Content-Type': 'application/json'} : {}
                });
                
                const status = response.status;
                const statusText = response.statusText;
                
                if (status >= 200 && status < 400) {
                    logContent.textContent += `\n✅ ${method} ${path} - ${status} ${statusText}`;
                } else {
                    logContent.textContent += `\n❌ ${method} ${path} - ${status} ${statusText}`;
                }
                
            } catch (error) {
                logContent.textContent += `\n❌ ${method} ${path} - 错误: ${error.message}`;
            }
            
            logContent.scrollTop = logContent.scrollHeight;
        }

        async function testFileProxy() {
            const logContent = document.getElementById('logContent');
            const testFileId = 'AgACAgUAAx0CRCIvTgABQhNFaDqmw1eGzjV0yQ-izEqhQGP415IAAszEMRuj5NhVoHYw3x9J-H8BAAMCAAN4AAM2BA';
            
            try {
                logContent.textContent += `\n\n🔄 测试文件代理功能...`;
                
                const response = await fetch(`/api/file/${testFileId}`);
                const status = response.status;
                const statusText = response.statusText;
                const contentType = response.headers.get('Content-Type');
                
                if (status >= 200 && status < 400) {
                    logContent.textContent += `\n✅ GET /api/file/{file_id} - ${status} ${statusText} (${contentType})`;
                } else {
                    logContent.textContent += `\n❌ GET /api/file/{file_id} - ${status} ${statusText}`;
                }
                
            } catch (error) {
                logContent.textContent += `\n❌ GET /api/file/{file_id} - 错误: ${error.message}`;
            }
            
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 自动刷新时间戳
        setInterval(() => {
            const now = new Date().toLocaleTimeString('zh-CN');
            document.querySelector('.header p').textContent = `实时监控 Workers Assets 和 Webhook 功能 | ${now}`;
        }, 1000);
    </script>
</body>
</html> 