/**
 * Telegram Mini App 通用鉴权模块
 * 用于保护所有需要管理员权限的页面和功能
 */

// 鉴权状态管理
const AuthManager = {
	// 用户信息缓存
	user: null,
	isAuthenticated: false,
	authPromise: null,

	// 调试日志
	debug: (message, data = null) => {
		const timestamp = new Date().toLocaleTimeString();
		console.log(`[AuthManager ${timestamp}] ${message}`, data || '');
	},

	// 显示错误信息
	showError: (message) => {
		console.error('[AuthManager] Error:', message);
		// 可以在这里添加UI错误显示
	},

	// 检查Telegram环境
	checkTelegramEnvironment: () => {
		if (!window.Telegram?.WebApp) {
			AuthManager.debug('❌ 不在Telegram环境中');
			return false;
		}
		AuthManager.debug('✅ Telegram环境检测通过');
		return true;
	},

	// 获取Telegram initData
	getTelegramInitData: () => {
		if (!window.Telegram?.WebApp) {
			throw new Error('Telegram WebApp not available');
		}

		const initData = window.Telegram.WebApp.initData;
		if (!initData) {
			throw new Error('No initData available');
		}

		AuthManager.debug('获取到 initData', { length: initData.length });
		return initData;
	},

	// 通过API验证权限
	verifyPermissionViaAPI: async (initData) => {
		try {
			AuthManager.debug('开始API权限验证...');

			const response = await fetch('/api/auth/verify', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					initData: initData,
				}),
			});

			const result = await response.json();

			AuthManager.debug('API权限验证响应', {
				status: response.status,
				success: result.success,
				isAdmin: result.isAdmin,
			});

			if (!response.ok) {
				throw new Error(result.error || `HTTP ${response.status}`);
			}

			return result;
		} catch (error) {
			AuthManager.debug('API权限验证错误', error.message);
			throw error;
		}
	},

	// 主要的权限验证函数
	authenticate: async () => {
		// 如果已经在认证过程中，返回现有的Promise
		if (AuthManager.authPromise) {
			return AuthManager.authPromise;
		}

		// 如果已经认证过且有效，直接返回
		if (AuthManager.isAuthenticated && AuthManager.user) {
			AuthManager.debug('使用缓存的认证信息');
			return { success: true, user: AuthManager.user };
		}

		// 创建新的认证Promise
		AuthManager.authPromise = (async () => {
			try {
				AuthManager.debug('开始权限验证流程...');

				// 1. 检查Telegram环境
				if (!AuthManager.checkTelegramEnvironment()) {
					throw new Error('需要在Telegram中打开');
				}

				// 2. 初始化Telegram WebApp
				const tg = window.Telegram.WebApp;
				tg.ready();
				tg.expand();

				// 等待初始化完成
				await new Promise((resolve) => setTimeout(resolve, 500));

				// 3. 获取initData
				const initData = AuthManager.getTelegramInitData();

				// 4. API验证
				const authResult = await AuthManager.verifyPermissionViaAPI(initData);

				if (!authResult.success || !authResult.isAdmin) {
					throw new Error('权限验证失败：' + (authResult.error || '不是管理员'));
				}

				// 5. 保存认证状态
				AuthManager.user = authResult.user;
				AuthManager.isAuthenticated = true;

				AuthManager.debug('✅ 权限验证成功', AuthManager.user);

				return { success: true, user: AuthManager.user };
			} catch (error) {
				AuthManager.debug('❌ 权限验证失败', error.message);
				AuthManager.isAuthenticated = false;
				AuthManager.user = null;
				throw error;
			} finally {
				// 清除authPromise，允许重新认证
				AuthManager.authPromise = null;
			}
		})();

		return AuthManager.authPromise;
	},

	// 跳转到未授权页面
	redirectToUnauthorized: (reason) => {
		AuthManager.debug('跳转到未授权页面', reason);
		window.location.href = '/miniapp/unauthorized.html';
	},

	// 跳转到登录页面
	redirectToLogin: (reason) => {
		AuthManager.debug('跳转到登录页面', reason);
		window.location.href = '/miniapp/';
	},

	// 保护页面的主函数
	protectPage: async () => {
		try {
			// 显示加载状态
			document.body.style.visibility = 'hidden';

			AuthManager.debug('🔒 开始页面保护验证...');

			const result = await AuthManager.authenticate();

			if (result.success) {
				AuthManager.debug('✅ 页面访问授权成功');
				// 显示页面内容
				document.body.style.visibility = 'visible';

				// 触发认证完成事件
				window.dispatchEvent(
					new CustomEvent('authenticationComplete', {
						detail: { user: result.user },
					})
				);

				return result.user;
			} else {
				throw new Error('认证失败');
			}
		} catch (error) {
			AuthManager.debug('❌ 页面保护验证失败', error.message);

			// 根据错误类型选择跳转页面
			if (error.message.includes('需要在Telegram中打开')) {
				AuthManager.redirectToLogin('需要在Telegram中打开');
			} else {
				AuthManager.redirectToUnauthorized('权限验证失败: ' + error.message);
			}
		}
	},

	// 获取认证的API请求头
	getAuthHeaders: () => {
		if (!AuthManager.isAuthenticated) {
			throw new Error('未认证，无法获取请求头');
		}

		try {
			const initData = AuthManager.getTelegramInitData();
			return {
				Authorization: `tma ${initData}`,
				'Content-Type': 'application/json',
			};
		} catch (error) {
			throw new Error('无法获取认证信息: ' + error.message);
		}
	},

	// 安全的API请求方法
	secureApiRequest: async (url, options = {}) => {
		if (!AuthManager.isAuthenticated) {
			throw new Error('未认证，无法进行API请求');
		}

		try {
			const authHeaders = AuthManager.getAuthHeaders();

			const requestOptions = {
				...options,
				headers: {
					...authHeaders,
					...options.headers,
				},
			};

			AuthManager.debug('发起安全API请求', { url, method: requestOptions.method || 'GET' });

			const response = await fetch(url, requestOptions);

			// 检查认证失败
			if (response.status === 401 || response.status === 403) {
				AuthManager.debug('API请求认证失败，清除本地认证状态');
				AuthManager.isAuthenticated = false;
				AuthManager.user = null;
				throw new Error('认证已过期，请重新登录');
			}

			return response;
		} catch (error) {
			AuthManager.debug('安全API请求失败', error.message);
			throw error;
		}
	},

	// 清除认证状态
	logout: () => {
		AuthManager.debug('清除认证状态');
		AuthManager.isAuthenticated = false;
		AuthManager.user = null;
		AuthManager.authPromise = null;
	},
};

// 导出到全局作用域
window.AuthManager = AuthManager;

// 自动保护页面（如果不在登录页面和未授权页面）
document.addEventListener('DOMContentLoaded', () => {
	const currentPath = window.location.pathname;
	const unprotectedPages = ['/miniapp/', '/miniapp/index.html', '/miniapp/unauthorized.html'];

	// 检查是否是需要保护的页面
	if (!unprotectedPages.includes(currentPath) && currentPath.startsWith('/miniapp/')) {
		AuthManager.debug('检测到需要保护的页面，启动自动保护', currentPath);
		AuthManager.protectPage();
	}
});
