<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta
			name="viewport"
			content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
		/>
		<title>访问受限</title>
		<script src="telegram-web-app.js?v=071"></script>
		<link rel="stylesheet" href="styles/common.css?v=071" />
		<link rel="stylesheet" href="styles/unauthorized.css?v=071" />
	</head>
	<body class="bg-decoration">
		<div class="container">
			<div class="error-card card">
				<div class="icon">🔒</div>
				<h1 class="title">访问受限</h1>
				<p class="message">
					抱歉，您没有权限访问此管理界面。<br />
					此功能仅限管理员使用。
				</p>
				<div class="user-info" id="userInfo">
					<div class="user-info-item">
						<span class="user-info-label">当前用户：</span>
						<span class="user-info-value" id="userName">加载中...</span>
					</div>
					<div class="user-info-item">
						<span class="user-info-label">用户ID：</span>
						<span class="user-info-value" id="userId">加载中...</span>
					</div>
				</div>
				<button class="close-button" onclick="closeApp()">关闭应用</button>
			</div>
		</div>

		<script>
			// 初始化 Telegram Web App
			const tg = window.Telegram.WebApp;
			tg.ready();
			tg.expand();

			// 设置主题颜色
			document.documentElement.style.setProperty('--tg-color-scheme', window.Telegram.WebApp.colorScheme);

			// 显示用户信息
			function displayUserInfo() {
				const user = window.Telegram.WebApp.initDataUnsafe?.user;

				if (user) {
					document.getElementById('userName').textContent = user.first_name + (user.last_name ? ' ' + user.last_name : '');
					document.getElementById('userId').textContent = user.id;
				} else {
					document.getElementById('userInfo').style.display = 'none';
				}
			}

			// 关闭应用
			function closeApp() {
				window.Telegram.WebApp.close();
			}

			// 基础安全区域处理 (与index.html保持一致)
			function initBasicSafeArea() {
				const root = document.documentElement;

				// 设置视口高度变量
				function updateViewportHeight() {
					const vh = window.innerHeight * 0.01;
					root.style.setProperty('--vh', vh + 'px');

					// 尝试设置稳定视口高度
					if (window.screen && window.screen.height) {
						const svh = window.screen.height * 0.01;
						root.style.setProperty('--svh', svh + 'px');
					}
				}

				// 初始设置
				updateViewportHeight();

				// 监听变化
				window.addEventListener('resize', updateViewportHeight);
				window.addEventListener('orientationchange', () => {
					setTimeout(updateViewportHeight, 100);
				});

				// Telegram WebApp 特定处理
				if (tg && tg.viewportHeight) {
					root.style.setProperty('--tg-viewport-height', tg.viewportHeight + 'px');
				}

				if (tg && tg.viewportStableHeight) {
					root.style.setProperty('--tg-viewport-stable-height', tg.viewportStableHeight + 'px');
				}
			}

			// 初始化安全区域处理
			initBasicSafeArea();

			// 页面加载完成后显示用户信息
			displayUserInfo();

			// 设置关闭确认
			window.Telegram.WebApp.enableClosingConfirmation();
		</script>
	</body>
</html>
