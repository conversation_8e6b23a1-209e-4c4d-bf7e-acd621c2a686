<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta
			name="viewport"
			content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
		/>
		<title>Telegram Bot 管理后台 - 认证中</title>
		<link rel="stylesheet" href="styles/common.css?v=071" />
		<link rel="stylesheet" href="styles/index.css?v=071" />
	</head>
	<body class="bg-decoration">
		<div class="auth-container">
			<div class="auth-card card">
				<div class="logo">🤖</div>
				<h1 class="title title-lg">Telegram Bot 管理后台</h1>
				<p class="subtitle">正在验证您的访问权限...</p>

				<div class="loading">
					<div class="spinner"></div>
					<div>
						<div class="status" id="statusText">初始化中...</div>
					</div>
				</div>

				<div class="status-detail" id="statusDetail">请稍候，正在检查您的管理员权限</div>

				<div class="error status-error" id="errorMessage"></div>
				<div class="success status-success" id="successMessage"></div>
			</div>
		</div>

		<script type="module">
			import Logger from './logger.js';
			window.Logger = Logger;
		</script>
		<script src="telegram-web-app.js?v=071"></script>
		<script src="index.js?v=071"></script>
	</body>
</html>
