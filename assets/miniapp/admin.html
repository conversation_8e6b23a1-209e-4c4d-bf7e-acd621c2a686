<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta
			name="viewport"
			content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
		/>
		<title>BOT 管理后台</title>
		<link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAQAAEABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA/4QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEREQAAAAAAEAAAEAAAAAEAAAEAAAAAEAAAEAAAAAEAAAEAAAAAEAAAEAAAAAEAAAEAAAAAEAAAEAAAAAEREQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAD//wAA//8AAP//AAA=">
		<link rel="stylesheet" href="styles/common.css?v=071" />
		<link rel="stylesheet" href="styles/admin.css?v=071" />
	</head>
	<body class="bg-decoration-light">
		<!-- TG状态栏显示徽章 -->
		<div class="tg-status-badge">砂糖♥爱丽丝</div>

		<div class="user-header">
			<div class="user-avatar" id="userAvatar" onclick="showUserInfoModal()">?</div>
			<div class="user-info">
				<div class="user-name" id="userName">加载中...</div>
				<div class="user-username" id="userUsername">@username</div>
			</div>
			<div class="badges-container">
				<div class="timezone-badge" id="timezoneInfo" title="当前时区信息">时区加载中...</div>
				<div class="version-badge" title="Mini App 版本号 - 每次修改都会更新">v0.7.1</div>
			</div>
		</div>
		<div class="container">
			<!-- 媒体组管理页面 -->
			<div class="tab-content hidden" id="mediaTab">
				<div class="stats-cards">
					<div class="stat-card">
						<div class="stat-number" id="totalCount">-</div>
						<div class="stat-label">总记录数</div>
					</div>
					<div class="stat-card">
						<div class="stat-number" id="groupCount">-</div>
						<div class="stat-label">媒体组数</div>
					</div>
					<div class="stat-card">
						<div class="stat-number" id="todayCount">-</div>
						<div class="stat-label">今日新增</div>
					</div>
				</div>
				<div class="filters">
					<div class="filter-group">
						<label class="filter-label">搜索</label>
						<input type="text" class="filter-input" id="searchInput" placeholder="搜索用户名、群组名或内容..." />
					</div>
					<div class="filter-group">
						<label class="filter-label">媒体类型</label>
						<select class="filter-select" id="mediaTypeFilter">
							<option value="">全部类型</option>
							<option value="photo">图片</option>
							<option value="video">视频</option>
							<option value="document">文档</option>
						</select>
					</div>
					<div class="filter-group">
						<label class="filter-label">日期范围</label>
						<input type="date" class="filter-input" id="dateFrom" />
					</div>
					<div class="filter-group">
						<label class="filter-label">至</label>
						<input type="date" class="filter-input" id="dateTo" />
					</div>
					<!-- 桌面端视图切换按钮 -->
					<div class="filter-group desktop-only">
						<label class="filter-label">视图模式</label>
						<button id="viewToggleBtn" class="btn btn-secondary btn-sm" onclick="toggleView()">📊 切换到表格视图</button>
					</div>
				</div>
				<div class="error" id="errorMessage" style="display: none"></div>
				<div class="table-container">
					<div class="loading" id="loadingIndicator">
						<div class="loading-spinner"></div>
						正在加载数据...
					</div>
					<div class="empty-state" id="emptyState" style="display: none"><p>暂无数据</p></div>
					<table class="table" id="mediaTable" style="display: none">
						<thead>
							<tr>
								<th>ID</th>
								<th>用户</th>
								<th>群组</th>
								<th>媒体类型</th>
								<th>媒体预览</th>
								<th>时间</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody id="tableBody"></tbody>
					</table>
				</div>
			</div>

			<!-- 广告管理页面 -->
			<div class="tab-content" id="adsTab">
				<div class="stats-cards">
					<div class="stat-card">
						<div class="stat-number" id="activeCampaigns">-</div>
						<div class="stat-label">活跃规则</div>
					</div>
					<div class="stat-card">
						<div class="stat-number" id="totalPosts">-</div>
						<div class="stat-label">总发送次数</div>
					</div>
					<div class="stat-card">
						<div class="stat-number" id="todayPosts">-</div>
						<div class="stat-label">今日发送</div>
					</div>
				</div>

				<!-- 添加新广告规则按钮 -->
				<div class="action-buttons-container">
					<button class="primary-button" id="addCampaignBtn">+ 新建广告规则</button>
					<div class="secondary-buttons-group">
						<button class="secondary-button" id="viewCampaignsTableBtn">📊 查看规则表</button>
						<button class="secondary-button" id="viewPostsTableBtn">📋 查看发送记录表</button>
					</div>
				</div>

				<!-- 广告规则列表 -->
				<div class="campaigns-container">
					<div class="loading" id="adsLoadingIndicator">
						<div class="loading-spinner"></div>
						正在加载广告规则...
					</div>
					<div class="empty-state" id="adsEmptyState" style="display: none">
						<p>暂无广告规则</p>
					</div>
					<div class="campaigns-list" id="campaignsList" style="display: none"></div>
				</div>
			</div>

			<!-- 群组管理页面 -->
			<div class="tab-content hidden" id="groupsTab">
				<div class="stats-cards">
					<div class="stat-card">
						<div class="stat-number" id="configuredGroupsCount">-</div>
						<div class="stat-label">已配置群组</div>
					</div>
					<div class="stat-card">
						<div class="stat-number" id="activeVerificationCount">-</div>
						<div class="stat-label">启用验证</div>
					</div>
					<div class="stat-card">
						<div class="stat-number" id="totalRulesCount">-</div>
						<div class="stat-label">群规总数</div>
					</div>
				</div>

				<!-- 群组选择和添加 -->
				<div class="filter-group">
					<label class="filter-label">选择群组</label>
					<div class="group-select-container">
						<select id="groupSelect" class="filter-select">
							<option value="">请选择要配置的群组...</option>
						</select>
						<div class="group-action-buttons">
							<button id="addGroupBtn" class="btn btn-secondary btn-sm">
								➕ 添加群组
							</button>
							<button id="deleteGroupBtn" class="btn btn-danger btn-sm" style="display: none;" title="删除群组">
								🗑️ 删除群组
							</button>
						</div>
					</div>
				</div>

				<!-- 配置面板 -->
				<div id="configPanel" style="display: none;">
					<!-- 举报功能配置 -->
					<div class="section">
						<h3 class="section-title">📢 举报功能配置</h3>
						<div class="radio-group">
							<label class="radio-option">
								<input type="radio" name="reportMode" value="disabled">
								<span class="radio-custom"></span>
								<div class="option-content">
									<div class="option-title">🚫 关闭举报功能</div>
									<div class="option-desc">群员无法使用/jb命令进行举报</div>
								</div>
							</label>
							
							<label class="radio-option">
								<input type="radio" name="reportMode" value="private">
								<span class="radio-custom"></span>
								<div class="option-content">
									<div class="option-title">👥 私聊通知所有管理员</div>
									<div class="option-desc">举报将通过私聊发送给所有群管理员</div>
								</div>
							</label>
							
							<label class="radio-option">
								<input type="radio" name="reportMode" value="group">
								<span class="radio-custom"></span>
								<div class="option-content">
									<div class="option-title">📢 推送到指定管理群</div>
									<div class="option-desc">举报将发送到预设的管理群组</div>
								</div>
							</label>
						</div>
						
						<!-- 推送目标群组配置 -->
						<div id="reportTargetsSection" class="sub-section" style="display: none;">
							<h4 class="sub-title">📋 推送目标群组</h4>
							<div id="targetsList" class="targets-list">
								<!-- 动态生成的目标群组列表 -->
							</div>
							<button id="addTargetBtn" class="btn btn-secondary">
								➕ 添加管理群
							</button>
						</div>
					</div>
					
					<!-- 功能开关 -->
					<div class="section">
						<h3 class="section-title">🔧 功能开关</h3>
						<div class="toggles-grid">
							<div class="toggle-item">
								<div class="toggle-content">
									<div class="toggle-title">🛡️ 新人验证</div>
									<div class="toggle-desc">新成员加群是否需要验证</div>
								</div>
								<label class="toggle-switch">
									<input type="checkbox" id="newMemberVerification">
									<span class="slider"></span>
								</label>
							</div>
							
							<div class="toggle-item">
								<div class="toggle-content">
									<div class="toggle-title">☁️ 防恶俗云过滤</div>
									<div class="toggle-desc">启用AI智能内容过滤</div>
								</div>
								<label class="toggle-switch">
									<input type="checkbox" id="cloudFilterEnabled">
									<span class="slider"></span>
								</label>
							</div>
							
							<div class="toggle-item">
								<div class="toggle-content">
									<div class="toggle-title">📤 自动转发</div>
									<div class="toggle-desc">启用媒体内容自动转发</div>
								</div>
								<label class="toggle-switch">
									<input type="checkbox" id="autoForwardEnabled">
									<span class="slider"></span>
								</label>
							</div>
						</div>
					</div>
					
					<!-- 群规管理 -->
					<div class="section">
						<h3 class="section-title">📝 群规管理</h3>
						<div class="section-desc">配置群规链接，新成员验证通过后将自动发送</div>
						
						<div id="rulesContainer" class="rules-container">
							<div id="rulesList" class="rules-list">
								<!-- 动态生成的群规列表 -->
							</div>
							
							<button id="addRuleBtn" class="btn btn-secondary">
								➕ 添加群规
							</button>
						</div>
					</div>

					<!-- 管理员管理（只有超级管理员可见） -->
					<div id="adminManagementSection" class="section" style="display: none;">
						<h3 class="section-title">👥 管理员管理</h3>
						<div class="section-desc">管理群组的超级管理员和普通管理员</div>
						
						<div id="adminsList" class="admins-list">
							<!-- 动态生成的管理员列表 -->
						</div>
						
						<button id="addAdminBtn" class="btn btn-secondary">
							➕ 添加管理员
						</button>
					</div>

					<!-- 操作按钮 -->
					<div class="action-buttons">
						<button id="saveConfigBtn" class="btn btn-primary" disabled>
							💾 保存配置
						</button>
						<button id="resetConfigBtn" class="btn btn-secondary">
							🔄 重置
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- 底部标签栏 -->
		<div class="tab-navigation">
			<button class="tab-button active" data-tab="ads">
				<div class="tab-icon">📢</div>
				<div class="tab-label">广告管理</div>
			</button>
			<button class="tab-button" data-tab="media">
				<div class="tab-icon">📊</div>
				<div class="tab-label">媒体组</div>
			</button>
			<button class="tab-button" data-tab="groups">
				<div class="tab-icon">⚙️</div>
				<div class="tab-label">群管理</div>
			</button>
		</div>

		<!-- 广告规则编辑模态框 -->
		<div class="modal" id="campaignModal" style="display: none">
			<div class="modal-content campaign-modal-content">
				<div class="modal-header">
					<h2 class="modal-title" id="modalTitle">新建广告规则</h2>
					<button class="modal-close" id="modalClose">&times;</button>
				</div>
				<div class="modal-body campaign-modal-body">
					<form id="campaignForm">
						<div class="form-group">
							<label class="form-label" for="campaignName">规则名称</label>
							<input type="text" class="form-input" id="campaignName" required />
						</div>
						<div class="form-group">
							<label class="form-label" for="targetChannelId">目标频道ID</label>
							<div class="input-group">
								<input type="text" class="form-input" id="targetChannelId" required placeholder="例如: -1001234567890" />
								<button type="button" class="paste-button" id="pasteChannelId" title="粘贴剪切板内容">📋</button>
							</div>
						</div>
						<div class="form-group">
							<label class="form-label" for="sourceMessageId">广告消息ID</label>
							<input type="number" class="form-input" id="sourceMessageId" required placeholder="广告频道中的消息ID" />
						</div>
						<div class="form-group">
							<label class="form-label" for="startDate">开始日期</label>
							<input type="date" class="form-input" id="startDate" required />
						</div>
						<div class="form-group">
							<label class="form-label" for="endDate">结束日期</label>
							<input type="date" class="form-input" id="endDate" required />
						</div>
						<div class="form-group">
							<label class="form-label" for="frequencyDays"
								>发布频率 <small style="color: var(--text-light)">(每隔多少天发一次)</small></label
							>
							<input
								type="number"
								class="form-input"
								id="frequencyDays"
								required
								min="0"
								max="365"
								value="0"
								step="1"
								placeholder="输入间隔天数"
							/>
							<small style="color: var(--text-muted); font-size: 11px; margin-top: 4px; display: block">
								💡 0 = 每天发送，1 = 隔一天发送，7 = 每周发送
							</small>
						</div>
						<div class="form-group">
							<label class="form-label" for="publishTime">发布时间 <small style="color: var(--text-light)">(北京时间)</small></label>
							<input type="time" class="form-input" id="publishTime" required value="14:00" />
							<small style="color: var(--text-muted); font-size: 11px; margin-top: 4px; display: block">
								🕐 直接输入北京时间，系统将按此时间执行
							</small>
						</div>
						<div class="form-group">
							<label for="isPin">
								<input type="checkbox" class="form-checkbox" id="isPin" />
								<span class="form-label">📌 置顶消息</span>
								<small style="color: var(--text-muted); font-size: 11px; margin-left: auto"> 发送后自动置顶 </small>
							</label>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="secondary-button" id="cancelBtn">取消</button>
					<button type="submit" class="primary-button" id="saveBtn" form="campaignForm">保存</button>
				</div>
			</div>
		</div>

		<!-- 数据表查看模态框 -->
		<div class="modal" id="dataTableModal" style="display: none">
			<div class="modal-content" style="max-width: 95vw; max-height: 90vh">
				<div class="modal-header">
					<h2 class="modal-title" id="dataTableTitle">数据表查看</h2>
					<button class="modal-close" id="dataTableClose">&times;</button>
				</div>
				<div class="modal-body" style="max-height: 70vh; overflow: auto">
					<div class="loading" id="dataTableLoading">
						<div class="loading-spinner"></div>
						正在加载数据...
					</div>
					<div class="error" id="dataTableError" style="display: none"></div>
					<div class="table-container" id="dataTableContainer" style="display: none">
						<table class="table" id="dataTable">
							<thead id="dataTableHead"></thead>
							<tbody id="dataTableBody"></tbody>
						</table>
					</div>
				</div>
				<div class="modal-footer">
					<button class="secondary-button" id="copyTableDataBtn">📋 复制JSON数据</button>
					<button class="secondary-button" id="refreshTableBtn">🔄 刷新</button>
					<button class="primary-button" id="closeTableBtn">关闭</button>
				</div>
			</div>
		</div>

		<!-- 添加管理群模态框 -->
		<div class="modal" id="addTargetModal" style="display: none">
			<div class="modal-content">
				<div class="modal-header">
					<h3>添加管理群</h3>
					<button class="modal-close" id="closeAddTargetModal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="form-group">
						<label for="targetChatId">群组ID</label>
						<input type="text" id="targetChatId" placeholder="-1001234567890" class="form-input">
						<div class="input-help">请输入群组ID，通常以-100开头</div>
					</div>
					<div class="form-group">
						<label for="targetTitle">群组名称</label>
						<input type="text" id="targetTitle" placeholder="管理群" class="form-input" maxlength="50">
						<div class="input-help">群组的显示名称</div>
					</div>
				</div>
				<div class="modal-footer">
					<button id="saveTargetBtn" class="btn btn-primary">保存</button>
					<button id="cancelTargetBtn" class="btn btn-secondary">取消</button>
				</div>
			</div>
		</div>

		<!-- 添加/编辑群规模态框 -->
		<div class="modal" id="ruleModal" style="display: none">
			<div class="modal-content">
				<div class="modal-header">
					<h3 id="ruleModalTitle">添加群规</h3>
					<button class="modal-close" id="closeRuleModal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="form-group">
						<label for="ruleUrl">群规链接</label>
						<input type="url" id="ruleUrl" placeholder="https://t.me/c/1234567890/123" class="form-input">
						<div class="input-help">请输入Telegram消息链接</div>
					</div>
					<div class="form-group">
						<label for="ruleTitle">标题（可选）</label>
						<input type="text" id="ruleTitle" placeholder="群规" class="form-input" maxlength="50">
						<div class="input-help">留空则使用默认标题"群规"</div>
					</div>
				</div>
				<div class="modal-footer">
					<button id="saveRuleBtn" class="btn btn-primary">保存</button>
					<button id="cancelRuleBtn" class="btn btn-secondary">取消</button>
				</div>
			</div>
		</div>

		<!-- Toast 提示框 -->
		<div class="toast" id="toast">
			<div class="toast-message" id="toastMessage"></div>
		</div>

		<!-- 添加群组模态框 -->
		<div class="modal" id="addGroupModal" style="display: none">
			<div class="modal-content">
				<div class="modal-header">
					<h3>添加群组</h3>
					<button class="modal-close" id="closeAddGroupModal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="form-group">
						<label for="groupChatId">群组ID</label>
						<input type="text" id="groupChatId" placeholder="-1001234567890" class="form-input">
						<div class="input-help">请输入群组ID，通常以-100开头</div>
					</div>
					<div class="form-group">
						<label for="groupChatTitle">群组名称（可选）</label>
						<input type="text" id="groupChatTitle" placeholder="我的群组" class="form-input" maxlength="100">
						<div class="input-help">留空则自动获取群组标题</div>
					</div>
					<div class="alert alert-info">
						<strong>权限要求：</strong>
						<ul>
							<li>您必须是群组创建者或具有添加管理员权限的管理员</li>
							<li>Bot必须是群组管理员</li>
						</ul>
					</div>
				</div>
				<div class="modal-footer">
					<button id="saveGroupBtn" class="btn btn-primary">添加群组</button>
					<button id="cancelGroupBtn" class="btn btn-secondary">取消</button>
				</div>
			</div>
		</div>

		<!-- 添加管理员模态框 -->
		<div class="modal" id="addAdminModal" style="display: none">
			<div class="modal-content">
				<div class="modal-header">
					<h3>添加管理员</h3>
					<button class="modal-close" id="closeAddAdminModal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="form-group">
						<label for="adminUserId">用户ID</label>
						<input type="number" id="adminUserId" placeholder="123456789" class="form-input">
						<div class="input-help">请输入要添加为管理员的用户ID</div>
					</div>
					<div class="form-group">
						<label for="adminUserName">用户名</label>
						<input type="text" id="adminUserName" placeholder="张三" class="form-input" maxlength="50">
						<div class="input-help">用户的显示名称</div>
					</div>
					<div class="alert alert-info">
						<strong>说明：</strong>
						<ul>
							<li>只能添加普通管理员，不能添加超级管理员</li>
							<li>普通管理员可以修改群组配置，但不能管理其他管理员</li>
						</ul>
					</div>
				</div>
				<div class="modal-footer">
					<button id="saveAdminBtn" class="btn btn-primary">添加管理员</button>
					<button id="cancelAdminBtn" class="btn btn-secondary">取消</button>
				</div>
			</div>
		</div>

		<!-- 权限要求提示模态框 -->
		<div class="modal" id="permissionRequirementModal" style="display: none">
			<div class="modal-content">
				<div class="modal-header">
					<h3>❌ 权限验证失败</h3>
					<button class="modal-close" id="closePermissionModal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="alert alert-error">
						<strong id="permissionErrorTitle">添加群组失败</strong>
						<div id="permissionErrorMessage"></div>
					</div>
					
					<div class="requirements-section">
						<h4>📋 添加群组的必备条件：</h4>
						
						<div class="requirement-group">
							<h5>👤 您的权限要求：</h5>
							<ul class="requirement-list">
								<li>✅ 您必须是群组的<strong>创建者</strong>，或</li>
								<li>✅ 您是群组<strong>管理员</strong>且具有<strong>"添加新管理员"</strong>权限</li>
							</ul>
						</div>
						
						<div class="requirement-group">
							<h5>🤖 Bot权限要求：</h5>
							<ul class="requirement-list">
								<li>✅ Bot必须已加入该群组</li>
								<li>✅ Bot必须是群组的<strong>管理员</strong></li>
								<li>✅ Bot需要有基本的管理权限</li>
							</ul>
						</div>
						
						<div class="requirement-group">
							<h5>🆔 群组ID格式：</h5>
							<ul class="requirement-list">
								<li>✅ 必须以 <code>-100</code> 开头</li>
								<li>✅ 例如：<code>-1001234567890</code></li>
							</ul>
						</div>
					</div>
					
					<div class="help-section">
						<h4>🛠️ 如何获取群组ID？</h4>
						<ol class="help-list">
							<li>方法1：转发群组消息到 <strong>@userinfobot</strong></li>
							<li>方法2：添加 <strong>@RawDataBot</strong> 到群组查看</li>
							<li>方法3：使用 <strong>@username_to_id_bot</strong></li>
						</ol>
					</div>
				</div>
				<div class="modal-footer">
					<button id="retryAddGroupBtn" class="btn btn-primary">重新尝试</button>
					<button id="closePermissionBtn" class="btn btn-secondary">关闭</button>
				</div>
			</div>
		</div>

		<!-- 删除群组确认模态框 -->
		<div class="modal" id="deleteGroupModal" style="display: none">
			<div class="modal-content">
				<div class="modal-header">
					<h3>⚠️ 删除群组确认</h3>
					<button class="modal-close" id="closeDeleteGroupModal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="alert alert-error">
						<strong>⚠️ 警告：此操作不可撤销</strong>
						<div>您即将删除群组及其所有关联数据</div>
					</div>
					
					<div class="delete-group-info">
						<h4>将要删除的群组：</h4>
						<div class="group-info-card">
							<div id="deleteGroupName" class="group-name"></div>
							<div id="deleteGroupId" class="group-id"></div>
						</div>
					</div>
					
					<div class="delete-consequences">
						<h4>🔥 删除后将清除：</h4>
						<ul class="consequence-list">
							<li>❌ 该群组的所有配置信息</li>
							<li>❌ 该群组的所有管理员权限记录</li>
							<li>❌ 该群组的群规设置</li>
							<li>❌ 该群组的举报配置</li>
							<li>❌ 相关的所有历史数据</li>
						</ul>
					</div>
					
					<div class="confirm-text">
						<p><strong>请输入群组名称来确认删除：</strong></p>
						<input type="text" id="confirmGroupName" placeholder="请输入群组名称" class="form-input">
						<div class="input-help">输入群组名称以确认删除操作</div>
					</div>
				</div>
				<div class="modal-footer">
					<button id="confirmDeleteGroupBtn" class="btn btn-danger" disabled>确认删除</button>
					<button id="cancelDeleteGroupBtn" class="btn btn-secondary">取消</button>
				</div>
			</div>
		</div>

		<script src="telegram-web-app.js?v=071"></script>
		<script src="auth.js?v=071"></script>
		<script src="/miniapp/admin.js?v=071"></script>
	</body>
</html>
