const tg = window.Telegram.WebApp;

// 检测是否为移动设备
function isMobileDevice() {
	const userAgent = navigator.userAgent || navigator.vendor || window.opera;

	// 检测移动设备的用户代理
	const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/i;
	const isMobileUA = mobileRegex.test(userAgent.toLowerCase());

	// 检测屏幕尺寸作为辅助判断
	const isMobileScreen = window.innerWidth <= 768 && window.innerHeight <= 1024;

	// Telegram WebApp 平台检测
	const isMobilePlatform = tg.platform === 'android' || tg.platform === 'ios';

	// 综合判断
	const isMobile = isMobileUA || isMobilePlatform || (isMobileScreen && 'ontouchstart' in window);

	Logger.debug(`移动端检测: UA=${isMobileUA}, Screen=${isMobileScreen}, Platform=${isMobilePlatform}, Final=${isMobile}`);
	Logger.debug(`用户代理: ${userAgent}`);
	Logger.debug(`屏幕尺寸: ${window.innerWidth}x${window.innerHeight}`);
	Logger.debug(`Telegram平台: ${tg.platform}`);

	return isMobile;
}

// 更新状态显示
function updateStatus(text, detail = '') {
	document.getElementById('statusText').textContent = text;
	document.getElementById('statusDetail').textContent = detail;
	Logger.debug(`状态更新: ${text} - ${detail}`);
}

// 显示错误
function showError(message) {
	document.querySelector('.loading').style.display = 'none';
	document.getElementById('errorMessage').style.display = 'block';
	document.getElementById('errorMessage').textContent = message;
	Logger.debug(`错误: ${message}`);
}

// 显示成功并跳转
function showSuccessAndRedirect(user) {
	document.querySelector('.loading').style.display = 'none';
	document.getElementById('successMessage').style.display = 'block';
	document.getElementById('successMessage').textContent = `欢迎，${user.first_name}！正在跳转到管理界面...`;
	Logger.debug(`认证成功: ${user.first_name} (${user.id})`);

	// 跳转到管理界面
	setTimeout(() => {
		window.location.href = '/miniapp/admin.html';
	}, 1000);
}

// 权限检查失败，跳转到未授权页面
function redirectToUnauthorized(reason, user = null) {
	Logger.debug(`权限检查失败: ${reason}`);
	if (user) {
		Logger.debug(`用户信息: ${user.first_name} (${user.id})`);
	}

	// 立即跳转到未授权页面
	window.location.href = '/miniapp/unauthorized.html';
}

// 通过API验证用户权限
async function verifyPermissionViaAPI(initData) {
	try {
		Logger.debug('开始API权限验证...');

		const response = await fetch('/api/auth/verify', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				initData: initData,
			}),
		});

		const result = await response.json();

		Logger.debug(`API权限验证响应: ${JSON.stringify(result)}`);

		if (!response.ok) {
			Logger.debug(`API请求失败: HTTP ${response.status}`);
			return { success: false, error: result.error || 'API request failed' };
		}

		return result;
	} catch (error) {
		Logger.debug(`API权限验证错误: ${error.message}`);
		return { success: false, error: error.message };
	}
}

// 主要的权限检查函数
async function checkPermission() {
	try {
		updateStatus('初始化 Telegram WebApp...', '正在连接 Telegram 服务');

		// 初始化 Telegram WebApp
		tg.ready();
		tg.expand();

		// 检测设备类型并按需请求全屏模式
		const isOnMobile = isMobileDevice();

		if (isOnMobile && tg.requestFullscreen && tg.isVersionAtLeast('8.0')) {
			try {
				await tg.requestFullscreen();
				Logger.debug('已在移动设备上请求全屏模式');
			} catch (error) {
				Logger.debug(`移动设备全屏模式请求失败: ${error.message || error}`);
			}
		} else if (!isOnMobile) {
			Logger.debug('检测到桌面设备，跳过全屏模式请求');
		} else {
			Logger.debug('不支持全屏模式或版本不足，跳过全屏模式请求');
		}

		Logger.debug('Telegram WebApp 初始化完成');
		Logger.debug(`平台: ${tg.platform}, 版本: ${tg.version}`);
		Logger.debug(`主题: ${tg.colorScheme}, 是否展开: ${tg.isExpanded}`);

		// 等待一小段时间确保初始化完成
		await new Promise((resolve) => setTimeout(resolve, 500));

		updateStatus('获取用户信息...', '正在验证您的身份');

		// 获取用户信息
		const user = tg.initDataUnsafe?.user;
		Logger.debug(`initDataUnsafe: ${JSON.stringify(tg.initDataUnsafe)}`);

		if (!user || !user.id) {
			redirectToUnauthorized('无法获取用户信息，可能不是通过 Telegram 打开');
			return;
		}

		Logger.debug(`用户信息: ${user.first_name} ${user.last_name || ''} (@${user.username || 'N/A'}) ID:${user.id}`);

		updateStatus('检查管理员权限...', `验证用户 ${user.first_name} 的权限`);

		// 通过API验证管理员权限
		const authResult = await verifyPermissionViaAPI(tg.initData);

		if (!authResult.success) {
			redirectToUnauthorized(authResult.error || '权限验证失败', user);
			return;
		}

		if (!authResult.isAdmin) {
			redirectToUnauthorized(`用户 ${user.first_name} (${user.id}) 不是管理员`, user);
			return;
		}

		// 权限检查通过
		Logger.debug('权限检查通过！');
		showSuccessAndRedirect(authResult.user);
	} catch (error) {
		Logger.debug(`权限检查过程中发生错误: ${error.message}`);
		showError(`认证过程中发生错误: ${error.message}`);
	}
}

// 基础安全区域处理 (简化版本)
function initBasicSafeArea() {
	const root = document.documentElement;

	// 设置视口高度变量
	function updateViewportHeight() {
		const vh = window.innerHeight * 0.01;
		root.style.setProperty('--vh', vh + 'px');

		// 尝试设置稳定视口高度
		if (window.screen && window.screen.height) {
			const svh = window.screen.height * 0.01;
			root.style.setProperty('--svh', svh + 'px');
		}
	}

	// 初始设置
	updateViewportHeight();

	// 监听变化
	window.addEventListener('resize', updateViewportHeight);
	window.addEventListener('orientationchange', () => {
		setTimeout(updateViewportHeight, 100);
	});

	// Telegram WebApp 特定处理
	if (tg && tg.viewportHeight) {
		root.style.setProperty('--tg-viewport-height', tg.viewportHeight + 'px');
	}

	if (tg && tg.viewportStableHeight) {
		root.style.setProperty('--tg-viewport-stable-height', tg.viewportStableHeight + 'px');
	}
}

// 在页面加载后开始检查权限
document.addEventListener('DOMContentLoaded', () => {
	// 初始化基础安全区域处理
	initBasicSafeArea();

	// 等待一小段时间让动画播放完成
	setTimeout(checkPermission, 300);
});
