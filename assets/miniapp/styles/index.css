/* ==========================================================================
   Index页面专用样式 - Telegram Bot 管理后台认证页面
   ========================================================================== */

/* 页面布局 - 使用最新的安全区域处理方案 */
body {
    background: var(--bg-gradient-primary);
    /* 使用现代视口单位和智能回退 */
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    
    /* 智能安全区域处理 - 基于common.css的新变量系统 */
    padding: var(--spacing-lg);
    padding-top: calc(var(--spacing-lg) + var(--content-safe-area-inset-top));
    /* 使用键盘感知的底部安全区域 */
    padding-bottom: calc(var(--spacing-lg) + var(--dynamic-safe-area-bottom, var(--content-safe-area-inset-bottom)));
    padding-left: calc(var(--spacing-lg) + var(--content-safe-area-inset-left));
    padding-right: calc(var(--spacing-lg) + var(--content-safe-area-inset-right));
}

/* 认证容器 */
.auth-container {
    text-align: center;
    max-width: 400px;
    width: 100%;
    position: relative;
    z-index: 1;
}

/* 认证卡片 */
.auth-card {
    animation: slideUp 0.6s ease-out;
}

/* Logo */
.logo {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: var(--radius-round);
    margin: 0 auto var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    box-shadow: var(--shadow-primary);
    animation: pulse 2s ease-in-out infinite;
}

/* 标题和副标题 */
.subtitle {
    font-size: 16px;
    color: var(--text-muted);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.5;
}

/* 加载状态 */
.loading {
    margin-bottom: var(--spacing-xl);
}

.status {
    font-size: 14px;
    color: var(--primary-color);
    font-weight: 500;
}

/* 状态详情 */
.status-detail {
    font-size: 13px;
    color: var(--text-light);
    margin-top: var(--spacing-xs);
    background: var(--bg-light-gray);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    border: 3px solid var(--primary-color);
}

/* 错误和成功状态 */
.error {
    margin-top: var(--spacing-md);
    display: none;
}

.success {
    margin-top: var(--spacing-md);
    display: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .logo {
        width: 64px;
        height: 64px;
        font-size: 28px;
    }
} 