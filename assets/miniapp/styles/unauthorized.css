

/* ==========================================================================
   Unauthorized页面样式 - Telegram Bot 管理后台未授权页面
   ========================================================================== */

body {
    background: var(--bg-gradient-primary);
    /* 使用现代视口单位和智能回退 */
    height: calc(var(--tg-viewport-height, 100vh));
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    
    /* 智能安全区域处理 - 基于common.css的新变量系统 */
    padding: var(--spacing-lg);
    padding-top: calc(var(--spacing-lg) + var(--content-safe-area-inset-top));
    /* 使用键盘感知的底部安全区域 */
    padding-bottom: calc(var(--spacing-lg) + var(--dynamic-safe-area-bottom, var(--content-safe-area-inset-bottom)));
    padding-left: calc(var(--spacing-lg) + var(--content-safe-area-inset-left));
    padding-right: calc(var(--spacing-lg) + var(--content-safe-area-inset-right));
}

.container {
    text-align: center;
    max-width: 420px;
    width: 100%;
    position: relative;
    z-index: 1;
}

.error-card {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl) var(--spacing-2xl);
    box-shadow: var(--shadow-heavy), 
                var(--shadow-card);
    border: 1px solid var(--border-white);
    animation: slideUp 0.6s ease-out;
}

.icon {
    font-size: 80px;
    margin-bottom: var(--spacing-xl);
    opacity: 0.7;
    animation: pulse 2s ease-in-out infinite;
}

.title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--error-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.message {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-muted);
}

.user-info {
    background: var(--bg-gradient-card);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    font-size: 14px;
    border: 1px solid var(--border-color);
    text-align: left;
}

.user-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.user-info-item:last-child {
    margin-bottom: 0;
}

.user-info-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.user-info-value {
    color: var(--text-primary);
    font-weight: 600;
}

.close-button {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-medium);
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-primary);
    width: 100%;
}

.close-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary-hover);
}

.close-button:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .error-card {
        padding: var(--spacing-2xl) var(--spacing-xl);
        margin: var(--spacing-md);
    }
    
    .title {
        font-size: 24px;
    }
    
    .message {
        font-size: 14px;
    }

    .icon {
        font-size: 64px;
    }
}
