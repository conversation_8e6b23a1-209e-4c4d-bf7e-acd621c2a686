/* 群配置页面样式 */

/* #region 🎨 基础布局 */
.container {
	max-width: 800px;
	margin: 0 auto;
	padding: 20px;
	background: var(--bg-color);
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
	padding-bottom: 20px;
	border-bottom: 2px solid var(--border-color);
}

.header h1 {
	margin: 0 0 10px 0;
	color: var(--text-color);
	font-size: 28px;
	font-weight: 600;
}

.subtitle {
	margin: 0;
	color: var(--hint-color);
	font-size: 14px;
}

.main-content {
	margin-bottom: 100px; /* 为固定按钮留空间 */
}
/* #endregion */

/* #region 📋 区块样式 */
.section {
	background: var(--secondary-bg-color);
	border-radius: 12px;
	padding: 24px;
	margin-bottom: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	border: 1px solid var(--border-color);
}

.section-title {
	display: flex;
	align-items: center;
	margin: 0 0 16px 0;
	font-size: 18px;
	font-weight: 600;
	color: var(--text-color);
}

.sub-section {
	margin-top: 20px;
	padding: 16px;
	background: var(--bg-color);
	border-radius: 8px;
	border: 1px solid var(--border-color);
}

.sub-title {
	margin: 0 0 12px 0;
	font-size: 16px;
	font-weight: 500;
	color: var(--text-color);
}
/* #endregion */

/* #region 🔘 选择器样式 */
.select-input {
	width: 100%;
	padding: 12px 16px;
	border: 2px solid var(--border-color);
	border-radius: 8px;
	background: var(--bg-color);
	color: var(--text-color);
	font-size: 16px;
	transition: border-color 0.2s ease;
}

.select-input:focus {
	outline: none;
	border-color: var(--button-color);
}

.text-input {
	width: 100%;
	padding: 12px 16px;
	border: 2px solid var(--border-color);
	border-radius: 8px;
	background: var(--bg-color);
	color: var(--text-color);
	font-size: 16px;
	transition: border-color 0.2s ease;
}

.text-input:focus {
	outline: none;
	border-color: var(--button-color);
}

.help-text {
	margin: 8px 0 0 0;
	font-size: 12px;
	color: var(--hint-color);
}
/* #endregion */

/* #region 🔘 单选按钮组 */
.radio-group {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.radio-option {
	display: flex;
	align-items: flex-start;
	padding: 16px;
	border: 2px solid var(--border-color);
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	background: var(--bg-color);
}

.radio-option:hover {
	border-color: var(--button-color);
	transform: translateY(-1px);
}

.radio-option input[type="radio"] {
	display: none;
}

.radio-option input[type="radio"]:checked + .radio-custom {
	background: var(--button-color);
	border-color: var(--button-color);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
	opacity: 1;
}

.radio-option input[type="radio"]:checked ~ .option-content .option-title {
	color: var(--button-color);
}

.radio-custom {
	width: 20px;
	height: 20px;
	border: 2px solid var(--border-color);
	border-radius: 50%;
	margin-right: 12px;
	flex-shrink: 0;
	position: relative;
	transition: all 0.2s ease;
	margin-top: 2px;
}

.radio-custom::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8px;
	height: 8px;
	background: white;
	border-radius: 50%;
	transform: translate(-50%, -50%);
	opacity: 0;
	transition: opacity 0.2s ease;
}

.option-content {
	flex: 1;
}

.option-title {
	font-weight: 500;
	color: var(--text-color);
	margin-bottom: 4px;
	transition: color 0.2s ease;
}

.option-desc {
	font-size: 14px;
	color: var(--hint-color);
	line-height: 1.4;
}
/* #endregion */

/* #region 🎛️ 开关样式 */
.toggles-grid {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.toggle-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px;
	background: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	transition: all 0.2s ease;
}

.toggle-item:hover {
	border-color: var(--button-color);
}

.toggle-content {
	flex: 1;
}

.toggle-title {
	font-weight: 500;
	color: var(--text-color);
	margin-bottom: 4px;
}

.toggle-desc {
	font-size: 14px;
	color: var(--hint-color);
}

.toggle-switch {
	position: relative;
	display: inline-block;
	width: 60px;
	height: 34px;
}

.toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: var(--border-color);
	transition: 0.3s;
	border-radius: 34px;
}

.slider:before {
	position: absolute;
	content: "";
	height: 26px;
	width: 26px;
	left: 4px;
	bottom: 4px;
	background-color: white;
	transition: 0.3s;
	border-radius: 50%;
}

input:checked + .slider {
	background-color: var(--button-color);
}

input:checked + .slider:before {
	transform: translateX(26px);
}
/* #endregion */

/* #region 📊 目标群组列表 */
.targets-list {
	margin-bottom: 16px;
}

.target-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 16px;
	background: var(--secondary-bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	margin-bottom: 8px;
}

.target-info {
	flex: 1;
}

.target-title {
	font-weight: 500;
	color: var(--text-color);
	margin-bottom: 2px;
}

.target-id {
	font-size: 12px;
	color: var(--hint-color);
	font-family: monospace;
}

.remove-target {
	background: var(--destructive-text-color);
	color: white;
	border: none;
	padding: 6px 12px;
	border-radius: 6px;
	cursor: pointer;
	font-size: 12px;
	transition: background 0.2s ease;
}

.remove-target:hover {
	background: #c53030;
}

.empty-targets {
	text-align: center;
	padding: 24px;
	color: var(--hint-color);
}

.empty-targets .empty-icon {
	font-size: 32px;
	margin-bottom: 8px;
}
/* #endregion */

/* #region 🎯 操作按钮 */
.action-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: var(--secondary-bg-color);
	padding: 16px 20px;
	border-top: 1px solid var(--border-color);
	display: flex;
	gap: 12px;
	z-index: 100;
}

.btn-primary {
	flex: 1;
	background: var(--button-color);
	color: var(--button-text-color);
	border: none;
	padding: 14px 24px;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
	background: var(--button-color);
	transform: translateY(-1px);
}

.btn-primary:disabled {
	opacity: 0.5;
	cursor: not-allowed;
	transform: none;
}

.btn-secondary {
	background: var(--secondary-bg-color);
	color: var(--text-color);
	border: 2px solid var(--border-color);
	padding: 12px 24px;
	border-radius: 8px;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-secondary:hover {
	border-color: var(--button-color);
	transform: translateY(-1px);
}
/* #endregion */

/* #region 📊 统计面板 */
.stats-panel {
	background: linear-gradient(135deg, var(--button-color), #667eea);
	border-radius: 12px;
	padding: 24px;
	margin-bottom: 20px;
	color: white;
}

.stats-title {
	margin: 0 0 16px 0;
	font-size: 18px;
	font-weight: 600;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
	gap: 16px;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 32px;
	font-weight: 700;
	margin-bottom: 4px;
}

.stat-label {
	font-size: 14px;
	opacity: 0.9;
}
/* #endregion */

/* #region 🎪 占位符内容 */
.placeholder-content {
	text-align: center;
	padding: 32px 24px;
	color: var(--hint-color);
}

.placeholder-icon {
	font-size: 48px;
	margin-bottom: 16px;
}

.placeholder-text {
	font-size: 18px;
	font-weight: 500;
	margin: 0 0 8px 0;
	color: var(--text-color);
}

.placeholder-desc {
	margin: 0;
	font-size: 14px;
}
/* #endregion */

/* #region 🪟 模态框 */
.modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: var(--bg-color);
	border-radius: 12px;
	width: 90%;
	max-width: 400px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 24px 0 24px;
}

.modal-header h3 {
	margin: 0;
	color: var(--text-color);
}

.close-btn {
	background: none;
	border: none;
	font-size: 24px;
	cursor: pointer;
	color: var(--hint-color);
	padding: 0;
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-body {
	padding: 20px 24px;
}

.input-group {
	margin-bottom: 16px;
}

.input-group label {
	display: block;
	margin-bottom: 8px;
	font-weight: 500;
	color: var(--text-color);
}

.modal-footer {
	display: flex;
	gap: 12px;
	padding: 0 24px 24px 24px;
}

.modal-footer .btn-primary,
.modal-footer .btn-secondary {
	flex: 1;
	padding: 12px 16px;
}
/* #endregion */

/* #region 🍞 Toast 通知 */
.toast {
	position: fixed;
	top: 20px;
	left: 50%;
	transform: translateX(-50%);
	background: var(--secondary-bg-color);
	color: var(--text-color);
	padding: 12px 24px;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	z-index: 1001;
	display: none;
	border: 1px solid var(--border-color);
}

.toast.show {
	display: block;
	animation: slideDown 0.3s ease;
}

.toast.success {
	background: #48bb78;
	color: white;
	border-color: #38a169;
}

.toast.error {
	background: #f56565;
	color: white;
	border-color: #e53e3e;
}

.toast.warning {
	background: #f59e0b;
	color: white;
	border-color: #d97706;
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(-10px);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}
/* #endregion */

/* #region 🔄 加载状态 */
.loading {
	text-align: center;
	padding: 60px 20px;
	color: var(--hint-color);
}

.spinner {
	width: 40px;
	height: 40px;
	border: 4px solid var(--border-color);
	border-top: 4px solid var(--button-color);
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto 20px auto;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
/* #endregion */

/* #region 📝 群规管理样式 */
.rules-container {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.rules-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.rule-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px;
	background: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	transition: all 0.2s ease;
}

.rule-item:hover {
	border-color: var(--button-color);
}

.rule-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.rule-title {
	font-weight: 500;
	color: var(--text-color);
}

.rule-url {
	font-size: 14px;
	color: var(--hint-color);
	text-decoration: none;
	word-break: break-all;
}

.rule-url:hover {
	color: var(--button-color);
}

.rule-status {
	display: flex;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
	font-size: 12px;
}

.status-icon {
	font-size: 14px;
}

.status-accessible .status-text {
	color: #22c55e;
}

.status-inaccessible .status-text {
	color: #ef4444;
}

.status-unknown .status-text {
	color: var(--hint-color);
}

.rule-actions {
	display: flex;
	gap: 8px;
}

.rule-actions button {
	padding: 6px 12px;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-size: 12px;
	transition: all 0.2s ease;
}

.edit-rule {
	background: var(--button-color);
	color: white;
}

.edit-rule:hover {
	background: var(--button-hover-color);
}

.remove-rule {
	background: #dc2626;
	color: white;
}

.remove-rule:hover {
	background: #b91c1c;
}

.empty-rules {
	text-align: center;
	padding: 40px 20px;
	color: var(--hint-color);
}

.empty-rules-icon {
	font-size: 48px;
	margin-bottom: 16px;
}

/* 模态框样式 */
.modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: var(--card-bg);
	border-radius: 12px;
	width: 90%;
	max-width: 500px;
	max-height: 90vh;
	overflow-y: auto;
	border: 1px solid var(--border-color);
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px;
	border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
	margin: 0;
	color: var(--text-color);
}

.close-btn {
	background: none;
	border: none;
	font-size: 24px;
	cursor: pointer;
	color: var(--hint-color);
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	transition: all 0.2s ease;
}

.close-btn:hover {
	background: var(--hover-color);
	color: var(--text-color);
}

.modal-body {
	padding: 20px;
}

.form-group {
	margin-bottom: 20px;
}

.form-group label {
	display: block;
	margin-bottom: 8px;
	font-weight: 500;
	color: var(--text-color);
}

.input-field {
	width: 100%;
	padding: 12px;
	border: 1px solid var(--border-color);
	border-radius: 8px;
	background: var(--bg-color);
	color: var(--text-color);
	font-size: 16px;
	transition: all 0.2s ease;
	box-sizing: border-box;
}

.input-field:focus {
	outline: none;
	border-color: var(--button-color);
	box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-help {
	font-size: 12px;
	color: var(--hint-color);
	margin-top: 4px;
}

.modal-footer {
	display: flex;
	gap: 12px;
	padding: 20px;
	border-top: 1px solid var(--border-color);
	justify-content: flex-end;
}
/* #endregion 📝 群规管理样式 */

/* #region 📱 响应式设计 */
@media (max-width: 768px) {
	.container {
		padding: 16px;
	}
	
	.section {
		padding: 20px;
	}
	
	.action-buttons {
		flex-direction: column;
	}
	
	.modal-content {
		width: 95%;
	}
	
	.stats-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media (max-width: 480px) {
	.radio-option {
		padding: 12px;
	}
	
	.radio-custom {
		margin-right: 8px;
	}
	
	.toggle-item {
		padding: 12px;
	}
	
	.target-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;
	}
	
	.remove-target {
		align-self: flex-end;
	}
}
/* #endregion */ 