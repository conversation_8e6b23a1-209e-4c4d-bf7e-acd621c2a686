/* ==========================================================================
   通用样式文件 - Telegram Bot 管理后台
   包含主题色、基础样式、通用组件和动画
   ========================================================================== */

/* CSS 变量定义 - 主题色和基础颜色 */
:root {
    /* 
     * 安全区域变量 - 2024年最新方案
     * 基于Telegram Mini Apps官方SDK和社区最佳实践
     * 优先级: TG官方SDK > JavaScript计算 > iOS env() > 0
     */
    
    /* Telegram官方SDK变量 (推荐) */
    --tg-viewport-height: 100vh;
    --tg-viewport-stable-height: 100vh;
    --tg-viewport-width: 100vw;
    
    /* Telegram安全区域变量 - 官方命名约定 */
    --tg-safe-area-inset-top: 0px;
    --tg-safe-area-inset-bottom: 0px;
    --tg-safe-area-inset-left: 0px;
    --tg-safe-area-inset-right: 0px;
    
    /* Telegram内容安全区域变量 - 官方API 8.0+特性 */
    --tg-content-safe-area-inset-top: 0px;
    --tg-content-safe-area-inset-bottom: 0px;
    --tg-content-safe-area-inset-left: 0px;
    --tg-content-safe-area-inset-right: 0px;
    
    /* 标准安全区域变量 - 多层回退机制 */
    --safe-area-inset-top: var(--tg-safe-area-inset-top, var(--tg-content-safe-area-inset-top, env(safe-area-inset-top, 0px)));
    --safe-area-inset-bottom: var(--dynamic-safe-area-bottom, var(--tg-safe-area-inset-bottom, var(--tg-content-safe-area-inset-bottom, env(safe-area-inset-bottom, 0px))));
    --safe-area-inset-left: var(--tg-safe-area-inset-left, var(--tg-content-safe-area-inset-left, env(safe-area-inset-left, 0px)));
    --safe-area-inset-right: var(--tg-safe-area-inset-right, var(--tg-content-safe-area-inset-right, env(safe-area-inset-right, 0px)));
    
    /* 内容区域安全变量 - 基于官方SDK */
    --content-safe-area-inset-top: var(--tg-content-safe-area-inset-top, var(--safe-area-inset-top));
    --content-safe-area-inset-bottom: var(--tg-content-safe-area-inset-bottom, var(--safe-area-inset-bottom));
    --content-safe-area-inset-left: var(--tg-content-safe-area-inset-left, var(--safe-area-inset-left));
    --content-safe-area-inset-right: var(--tg-content-safe-area-inset-right, var(--safe-area-inset-right));
    
    /* 键盘感知的动态安全区域 - 社区最佳实践 */
    --dynamic-safe-area-bottom: var(--tg-safe-area-inset-bottom, var(--tg-content-safe-area-inset-bottom, env(safe-area-inset-bottom, 0px)));
    --keyboard-height: 0px;
    
    /* 现代视口单位 - 支持最新CSS规范 */
    --vh: 1vh;
    --svh: 1vh;
    --dvh: 1dvh;
    --lvh: 1lvh;
    
    /* 主题色 - 白桃粉&樱花粉系 (加深版) */
    --primary-gradient: linear-gradient(135deg, #FF91A4 0%, #FF69B4 100%);
    --primary-color: #FF91A4;
    --primary-dark: #FF69B4;
    
    /* 状态色 - 柔和可爱色系 */
    --success-gradient: linear-gradient(135deg, #d4edda 0%, #a8d8a8 100%);
    --success-color: #155724;
    --success-border: #6bcf7f;
    
    --error-gradient: linear-gradient(135deg, #ffe6e6 0%, #ffb3b3 100%);
    --error-color: #d63384;
    --error-border: #ff8fa3;
    
    --warning-gradient: linear-gradient(135deg, #fff3cd 0%, #ffe69c 100%);
    --warning-color: #856404;
    --warning-border: #ffd93d;
    
    /* 特殊状态色 - 薄荷绿&橙黄色 */
    --active-gradient: linear-gradient(135deg, #d1f2eb 0%, #a3e4d7 100%);
    --active-color: #0e6655;
    --active-border: #52c79a;
    
    --inactive-gradient: linear-gradient(135deg, #fff2e6 0%, #ffe0b3 100%);
    --inactive-color: #8b4513;
    --inactive-border: #ff9900;
    
    /* 媒体类型徽章色 */
    --badge-photo-bg: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    --badge-photo-color: #1e40af;
    --badge-video-bg: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
    --badge-video-color: #be185d;
    --badge-document-bg: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    --badge-document-color: #059669;
    
    /* 背景色 - 白桃粉&樱花粉系 */
    --bg-gradient-primary: linear-gradient(135deg, #FF91A4 0%, #FF69B4 100%);
    --bg-gradient-light: linear-gradient(135deg, #fffff5 0%, #fffff7 100%);
    --bg-gradient-dark: linear-gradient(135deg, #FF6B9D 0%, #FF1493 100%);
    --bg-gradient-card: linear-gradient(135deg, #fff 100%, #fff 100%);
    /* 文字色 */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-light: #a0aec0;
    --text-white: #e2e8f0;
    
    /* 边框和背景 */
    --border-color: #e2e8f0;
    --border-light: rgba(238, 240, 226, 0.6);
    --border-lighter: #f1f5f9;
    --border-secondary: #cbd5e0;
    --border-modal: rgba(226, 232, 240, 0.8);
    --border-white: rgba(255, 255, 255, 0.2);
    --bg-white: rgba(255, 255, 255, 0.95);
    --bg-white-transparent: rgba(255, 255, 255, 0.9);
    --bg-light-gray: #f7fafc;
    --bg-neutral: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --bg-modal-overlay: rgba(0, 0, 0, 0.7);
    --bg-hover-light: rgba(113, 128, 150, 0.05);
    --bg-hover-modal: rgba(160, 174, 192, 0.1);
    
    /* 阴影 - 白桃粉&樱花粉系 (加深版) */
    --shadow-light: 0 4px 20px rgba(255, 145, 164, 0.15);
    --shadow-medium: 0 8px 25px rgba(255, 145, 164, 0.25);
    --shadow-heavy: 0 20px 40px rgba(255, 105, 180, 0.18);
    --shadow-primary: 0 4px 12px rgba(255, 145, 164, 0.35);
    --shadow-primary-hover: 0 6px 16px rgba(255, 105, 180, 0.45);
    --shadow-debug: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-table: 0 8px 32px rgba(0, 0, 0, 0.08);
    --shadow-tab: 0 -4px 20px rgba(0, 0, 0, 0.08);
    --shadow-success: 0 4px 12px rgba(34, 84, 61, 0.15);
    --shadow-error: 0 4px 12px rgba(197, 48, 48, 0.15);
    --shadow-warning: 0 4px 12px rgba(116, 66, 16, 0.15);
    --shadow-active: 0 4px 12px rgba(14, 102, 85, 0.15);
    --shadow-inactive: 0 4px 12px rgba(139, 69, 19, 0.15);
    
    /* 焦点阴影 */
    --shadow-focus: 0 0 0 3px rgba(255, 145, 164, 0.25);
    --shadow-focus-light: 0 0 0 3px rgba(255, 145, 164, 0.18);
    
    /* 按钮激活背景 */
    --bg-tab-active: linear-gradient(135deg, rgba(255, 145, 164, 0.18) 0%, rgba(255, 105, 180, 0.18) 100%);
    
    /* 背景装饰渐变色 */
    --decoration-primary: rgba(255, 145, 164, 0.35);
    --decoration-white: rgba(255, 255, 255, 0.4);
    --decoration-light-pink: rgba(255, 145, 164, 0.15);
    --decoration-light-bg: rgba(255, 235, 241, 0.9);
    
    /* 圆角 */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 24px;
    --radius-round: 50%;
    
    /* 间距 */
    --spacing-xs: 8px;
    --spacing-sm: 12px;
    --spacing-md: 16px;
    --spacing-lg: 20px;
    --spacing-xl: 24px;
    --spacing-2xl: 32px;
    --spacing-3xl: 40px;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Inter', sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Consolas', monospace;
}

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    overflow: hidden;
    /* viewport-fit 应该在 meta 标签中设置，不是 CSS 属性 */
}

body {
    font-family: var(--font-family);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* 全屏模式支持 - 使用TG官方推荐的视口变量 */
    height: calc(var(--tg-viewport-height, 100vh));
    overflow-x: hidden;
    overflow-y: auto;
    
    /* 禁用滚动弹跳 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    
    /* 移动端触摸优化 */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    
    /* iOS安全区域处理 - 使用2024年最新的TG官方变量 */
    padding-top: var(--tg-content-safe-area-inset-top, var(--tg-safe-area-inset-top, env(safe-area-inset-top, 0px)));
    padding-bottom: var(--dynamic-safe-area-bottom, var(--tg-content-safe-area-inset-bottom, var(--tg-safe-area-inset-bottom, env(safe-area-inset-bottom, 0px))));
    padding-left: var(--tg-content-safe-area-inset-left, var(--tg-safe-area-inset-left, env(safe-area-inset-left, 0px)));
    padding-right: var(--tg-content-safe-area-inset-right, var(--tg-safe-area-inset-right, env(safe-area-inset-right, 0px)));
}

/* 通用背景装饰 - 白桃粉&樱花粉系 */
.bg-decoration::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, var(--decoration-primary) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, var(--decoration-white) 0%, transparent 50%);
    pointer-events: none;
}

.bg-decoration-light::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 10%, var(--decoration-light-pink) 0%, transparent 50%),
                radial-gradient(circle at 70% 90%, var(--decoration-light-bg) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 通用卡片样式 */
.card {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl) var(--spacing-2xl);
    box-shadow: var(--shadow-heavy), var(--shadow-card);
    border: 1px solid var(--border-white);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.card-compact {
    background: var(--bg-white-transparent);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
}

/* 通用按钮样式 */
.btn {
    border: none;
    border-radius: var(--radius-medium);
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-family: inherit;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary-hover);
}

.btn-secondary {
    background: white;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-light);
}

.btn-secondary:hover {
    background: var(--bg-gradient-card);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-sm {
    padding: 10px 20px;
    font-size: 14px;
}

.btn-full {
    width: 100%;
}

/* 通用标题样式 */
.title {
    font-weight: 700;
    background: var(--bg-gradient-dark);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.title-lg {
    font-size: 28px;
    margin-bottom: var(--spacing-sm);
}

.title-md {
    font-size: 24px;
    margin-bottom: var(--spacing-lg);
}

.title-sm {
    font-size: 20px;
}

/* 通用加载和状态样式 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--bg-gradient-card);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-color);
}

.spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: var(--radius-round);
    animation: spin 1s linear infinite;
}

.status-success {
    background: var(--success-gradient);
    color: var(--success-color);
    border: 1px solid var(--success-border);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-success);
}

.status-error {
    background: var(--error-gradient);
    color: var(--error-color);
    border: 1px solid var(--error-border);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-error);
}

.status-warning {
    background: var(--warning-gradient);
    color: var(--warning-color);
    border: 1px solid var(--warning-border);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-warning);
}

.status-active {
    background: var(--active-gradient);
    color: var(--active-color);
    border: 1px solid var(--active-border);
    border-radius: var(--radius-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-active);
}

.status-inactive {
    background: var(--inactive-gradient);
    color: var(--inactive-color);
    border: 1px solid var(--inactive-border);
    border-radius: var(--radius-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-inactive);
}

/* 通用表单样式 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 10px 14px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: white;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    font-family: inherit;
    transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus);
}

/* 通用动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 通用工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 通用响应式断点 */
@media (max-width: 768px) {
    .card {
        padding: var(--spacing-2xl) var(--spacing-xl);
        margin: var(--spacing-md);
    }
    
    .title-lg {
        font-size: 24px;
    }
    
    .title-md {
        font-size: 20px;
    }
    
    .btn {
        padding: 12px 24px;
        font-size: 14px;
    }
    
    /* 移动端的全屏优化和安全区域智能处理 */
    body {
        /* 使用现代视口单位，防止iOS Safari的地址栏问题 */
        height: calc(var(--tg-viewport-height, 100vh));
        
        /* 移动端专用安全区域处理 */
        padding-top: var(--content-safe-area-inset-top);
        /* 键盘感知的底部安全区域 */
        padding-bottom: var(--dynamic-safe-area-bottom, var(--content-safe-area-inset-bottom));
        padding-left: var(--content-safe-area-inset-left);
        padding-right: var(--content-safe-area-inset-right);
    }
    
    /* 固定元素的安全区域处理 */
    .fixed-top {
        top: var(--content-safe-area-inset-top);
    }
    
    .fixed-bottom {
        bottom: var(--dynamic-safe-area-bottom, var(--content-safe-area-inset-bottom));
    }
}

/* iPhone特定的安全区域优化 - 使用现代CSS特性 */
@supports (padding: max(0px)) {
    :root {
        /* 键盘感知的安全区域处理 - 社区最佳实践 */
        --keyboard-aware-safe-area-bottom: var(--dynamic-safe-area-bottom, max(env(safe-area-inset-bottom, 0px), 0px));
    }
    
    /* 键盘显示时的特殊处理 */
    :root:not(:has(input:focus-visible, textarea:focus-visible, [contenteditable]:focus-visible)) {
        --dynamic-safe-area-bottom: max(env(safe-area-inset-bottom, 0px), 0px);
    }
    
    /* 键盘隐藏时使用默认安全区域 */
    :root:has(input:focus-visible, textarea:focus-visible, [contenteditable]:focus-visible) {
        --dynamic-safe-area-bottom: 0px;
    }
}

/* 全屏模式特殊样式 */
@media (display-mode: fullscreen) {
    body {
        /* 在全屏模式下使用更精确的高度控制 */
        height: var(--tg-viewport-height);
        max-height: var(--tg-viewport-height);
    }
}

/* iOS设备特定优化 */
@supports (-webkit-touch-callout: none) {
    html {
        /* 确保iOS设备上的视口适配 */
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
    }
    
    body {
        /* iOS上的额外安全区域处理 */
        position: relative;
        /* 防止Safari地址栏导致的布局问题 */
        min-height: -webkit-fill-available;
    }
}

@media (max-width: 480px) {
    .card {
        padding: var(--spacing-2xl) var(--spacing-xl);
        margin: var(--spacing-md);
    }
    
    .title-lg {
        font-size: 20px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
}