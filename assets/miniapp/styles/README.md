# Telegram Bot 管理后台 - 样式文件说明

## 📁 文件结构

```
assets/miniapp/styles/
├── common.css        # 通用样式文件（新增）
├── index.css         # 认证页面专用样式（新增）
├── admin.css         # 管理页面样式（重构）
├── unauthorized.css  # 未授权页面样式（重构）
└── README.md         # 本说明文档（新增）
```

## 🎨 设计系统变量

### 主题色

```css
--primary-gradient: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
--primary-color: #FFB6C1;
--primary-dark: #FF69B4;
```

### 状态色

```css
--success-gradient: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
--error-gradient: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
--warning-gradient: linear-gradient(135deg, #faf089 0%, #f6e05e 100%);
```

### 间距系统

```css
--spacing-xs: 8px;
--spacing-sm: 12px;
--spacing-md: 16px;
--spacing-lg: 20px;
--spacing-xl: 24px;
--spacing-2xl: 32px;
--spacing-3xl: 40px;
```

### 圆角系统

```css
--radius-small: 8px;
--radius-medium: 12px;
--radius-large: 16px;
--radius-xl: 24px;
--radius-round: 50%;
```

## 📱 使用方式

### HTML 文件引用顺序

```html
<link rel="stylesheet" href="styles/common.css" /> <link rel="stylesheet" href="styles/[页面专用].css" />
```

### 通用类的使用

```html
<!-- 卡片样式 -->
<div class="card">内容</div>
<div class="card-compact">紧凑卡片</div>

<!-- 按钮样式 -->
<button class="btn btn-primary">主要按钮</button>
<button class="btn btn-secondary">次要按钮</button>

<!-- 标题样式 -->
<h1 class="title title-lg">大标题</h1>
<h2 class="title title-md">中标题</h2>

<!-- 状态样式 -->
<div class="status-success">成功状态</div>
<div class="status-error">错误状态</div>

<!-- 背景装饰 -->
<body class="bg-decoration">
	主题背景
</body>
<body class="bg-decoration-light">
	浅色背景
</body>
```

## 🔧 维护指南

### 添加新样式

1. 优先使用通用变量和类
2. 页面特有样式放在对应的 CSS 文件中
3. 可复用的组件考虑添加到 `common.css`

### 修改主题色

只需在 `common.css` 中修改 CSS 变量即可全局生效

### 响应式适配

使用统一的断点：768px（平板）和 480px（手机）

## 📋 文件对应关系

| HTML 文件           | CSS 文件                          | 说明       |
| ------------------- | --------------------------------- | ---------- |
| `index.html`        | `common.css` + `index.css`        | 认证页面   |
| `admin.html`        | `common.css` + `admin.css`        | 管理后台   |
| `unauthorized.html` | `common.css` + `unauthorized.css` | 未授权页面 |
