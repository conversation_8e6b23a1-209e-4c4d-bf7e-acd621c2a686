/* ==========================================================================
   Admin页面样式 - Telegram Bot 管理后台
   ========================================================================== */

body {
    background: var(--bg-gradient-light);
    /* 使用现代视口单位和智能回退 */
    height: calc(var(--tg-viewport-height, 100vh));
    
    /* 使用Telegram官方的contentSafeAreaInset */
    /* body基础padding: iOS=68px, Android=88px (根据平台动态设置) */
    /* user-header高度: 48px(头像) + 12px(上下padding) + 动态徽章空间 */
    /* nav高度: 42px(按钮) + 14px(上下padding) ≈ 56px */
    padding-top: calc(var(--body-base-padding, 68px) + var(--content-safe-area-inset-top) + var(--header-top-adjustment, 0px) + var(--spacing-md));
    /* 使用键盘感知的底部安全区域 */
    padding-bottom: calc(44px + var(--dynamic-safe-area-bottom, var(--content-safe-area-inset-bottom)) + var(--spacing-md));
    
    /* 左右边距 - 与fixed元素的padding保持一致 */
    padding-left: calc(var(--spacing-md) + var(--content-safe-area-inset-left));
    padding-right: calc(var(--spacing-md) + var(--content-safe-area-inset-right));
}

/* Telegram安全区域 */
.tg-status-badge {
    /* 根据JS设置的变量显示/隐藏和定位 */
    display: none;
    position: fixed;
    top: var(--tg-badge-top, calc(var(--content-safe-area-inset-top) + var(--spacing-sm)));
    left: 50%;
    height: 36px;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-dark);
    padding: 0 14px;
    border-radius: 999px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
    box-shadow: var(--shadow-primary);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 1002;
    /* 添加flexbox来垂直居中文字 */
    align-items: center;
    justify-content: center;
}

/* 通过JS动态设置class来控制显示 */
body.show-tg-badge .tg-status-badge {
    display: flex;
}

/* 用户头部区域 - 使用正确的安全区域 */
.user-header {
    position: fixed; 
    top: 0;
    left: 0; 
    right: 0;
    background: var(--bg-white);
    border-bottom: 1px solid var(--border-light);
    /* 使用JS动态计算的header调整值 */
    padding: var(--spacing-sm) var(--spacing-md); 
    padding-top: calc(var(--spacing-sm) + var(--content-safe-area-inset-top) + var(--header-top-adjustment, 0px));
    padding-left: calc(var(--spacing-md) + var(--content-safe-area-inset-left));
    padding-right: calc(var(--spacing-md) + var(--content-safe-area-inset-right));
    display: flex; 
    align-items: center; 
    gap: var(--spacing-sm);
    z-index: 1000; 
    box-shadow: var(--shadow-light);
}

/* user-header始终从顶部开始，Telegram会自动处理安全区域 */

.user-avatar {
    width: 48px; 
    height: 48px; 
    border-radius: var(--radius-round);
    background: var(--primary-gradient);
    color: white; 
    display: flex; 
    align-items: center;
    justify-content: center; 
    font-weight: bold; 
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-primary);
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-primary-hover);
}

.user-info { 
    flex: 1; 
}

.user-name { 
    font-size: 16px; 
    font-weight: 600; 
    color: var(--text-primary);
    margin-bottom: 2px;
    justify-content: flex-end;
}

.user-username { 
    font-size: 14px; 
    color: var(--text-muted); 
}

.version-badge {
    background: var(--primary-gradient);
    color: white;
    padding: 6px var(--spacing-sm);
    border-radius: var(--radius-large);
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.version-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-primary-hover);
}

/* 主容器 */
.container { 
    padding: var(--spacing-xl); 
    max-width: 1200px; 
    margin: 0 auto; 
}

.section-title { 
    font-size: 24px; 
    font-weight: 700; 
    margin-bottom: var(--spacing-lg); 
    color: var(--text-primary);
    background: var(--bg-gradient-dark);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 统计卡片区域 */
.stats-cards {
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm); 
    margin-bottom: var(--spacing-md);
}

.stat-card {
    background: var(--bg-white-transparent);
    padding: var(--spacing-md) var(--spacing-sm); 
    border-radius: var(--radius-medium); 
    text-align: center;
    /* border: 2px solid var(--border-light); */
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center; 
    box-shadow: 
    inset 0 -2px 0 0 var(--primary-color), /* 装饰条 */
    var(--shadow-light); /* 普通外阴影 */
}

/* .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
} */

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 
    inset 0 -2px 0 0 var(--primary-color), /* 装饰条 */
    var(--shadow-medium); /* 普通外阴影 */
}

.stat-number { 
    font-size: 24px; 
    font-weight: 700; 
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 6px;
    line-height: 1.1;
}

.stat-label { 
    font-size: 12px; 
    color: var(--text-muted); 
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.4px;
    line-height: 1.2;
}

/* 过滤器区域 */
.filters {
    background: var(--bg-white-transparent);
    padding: var(--spacing-lg); 
    border-radius: var(--radius-large); 
    margin-bottom: var(--spacing-lg);
    display: flex; 
    gap: var(--spacing-md); 
    flex-wrap: wrap; 
    align-items: center;
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-light);
}

.filter-group { 
    display: flex; 
    flex-direction: column; 
    gap: 6px; 
}

.filter-label { 
    font-size: 12px; 
    color: var(--text-secondary); 
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-input, .filter-select {
    padding: 10px 14px; 
    border: 2px solid var(--border-color);
    border-radius: 10px; 
    background: white;
    color: var(--text-primary); 
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-input:focus, .filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus);
}

/* 表格容器 */
.table-container {
    /* background: var(--bg-white-transparent);
    border-radius: var(--radius-large); 
    overflow: hidden; 
    box-shadow: var(--shadow-table);
    border: 1px solid var(--border-light); */
}

/* 卡片布局默认显示，表格默认隐藏 */
.mobile-cards {
    display: block;
    width: 100%;
    box-sizing: border-box;
}

/* 只对媒体组页面的表格应用隐藏 */
#mediaTab .table-container .table,
#mediaTable {
    display: none;
}



/* 桌面端卡片多列布局 - 类似 stats-cards 的响应式布局 */
@media (min-width: 769px) {
    .mobile-cards {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
        gap: var(--spacing-lg) !important;
        /* padding: var(--spacing-lg) !important; */
        margin: 0 !important;
    }
    
    .mobile-card {
        margin-bottom: 0 !important; /* 网格布局时不需要底部间距 */
        width: 100% !important;
        box-sizing: border-box !important;
    }
}

/* 卡片基础样式 - 全局可用 */
.mobile-card {
    background: var(--bg-white);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.mobile-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-lighter);
}

.mobile-card-id {
    background: var(--primary-gradient);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: var(--radius-small);
}

.mobile-card-date {
    font-size: 12px;
    color: var(--text-muted);
    text-align: right;
}

.mobile-card-content {
    display: grid;
    gap: var(--spacing-sm);
}

.mobile-card-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 24px;
}

.mobile-card-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 60px;
}

.mobile-card-value {
    flex: 1;
    text-align: right;
    font-size: 14px;
    color: var(--text-primary);
}

.mobile-card-media {
    position: relative;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
}

.media-type-overlay {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 6px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.media-type-overlay .media-type-badge {
    font-size: 10px;
    padding: 0;
    margin: 0;
    background: transparent !important;
    color: white !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
}

/* 用户和群组一行显示样式 */
.mobile-card-user-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.mobile-card-user-group .user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex: 1;
    min-width: 0; /* 允许文本截断 */
    max-width: 45%; /* 限制最大宽度 */
}

.mobile-card-user-group .user-text {
    display: flex;
    flex-direction: column;
    min-width: 0; /* 允许文本截断 */
    flex: 1;
    overflow: hidden;
}


.mobile-card-user-group .user-username {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

.mobile-card-user-group .user-badge {
    color: var(--primary-dark);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    flex-shrink: 0;
    float: left;
    margin-right: 6px;
}

.mobile-card-user-group .group-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex: 1;
    min-width: 0; /* 允许文本截断 */
    max-width: 45%; /* 限制最大宽度 */
    justify-content: flex-start;
}

.mobile-card-user-group .group-text {
    display: flex;
    flex-direction: column;
    min-width: 0; /* 允许文本截断 */
    flex: 1;
    text-align: left;
    overflow: hidden;
}

/* .group-name 样式已合并到 .group-name,.user-name 组合规则中 */

.mobile-card-user-group .group-id {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

.mobile-card-user-group .group-badge {
    color: var(--primary-dark);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    flex-shrink: 0;
    float: left;
    margin-right: 6px;
}
/* 用户名和群组名的文本容器 - 确保省略号正常工作 */
.mobile-card-user-group .group-name,
.mobile-card-user-group .user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%; /* 明确设置宽度 */
    min-width: 0;
}
.user-group-divider {
    width: 1px;
    height: 40px;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        rgba(255, 182, 193, 0.2) 10%,
        rgba(255, 182, 193, 0.6) 50%,
        rgba(255, 182, 193, 0.2) 90%,
        transparent 100%
    );
    flex-shrink: 0;
    margin: 0 var(--spacing-xs);
}

.mobile-card-actions {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-lighter);
    text-align: center;
}

.table { 
    display: block;
    width: 100%; 
    border-collapse: collapse; 
    overflow: auto;
}
.table thead tr {
    background: var(--primary-gradient);
}
.table th {
    
    color: white; 
    padding: 16px 12px; 
    text-align: left;
    font-weight: 600; 
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td { 
    padding: 16px 12px; 
    border-bottom: 1px solid var(--border-lighter); 
    font-size: 14px;
    font-weight: 500;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover { 
    background: var(--bg-neutral);
    transform: scale(1.001);
}

.table tbody tr:last-child td { 
    border-bottom: none; 
}

.media-type-badge {
    display: inline-block; 
    padding: 6px 12px; 
    border-radius: 20px;
    font-size: 11px; 
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.media-type-photo { 
    background: var(--badge-photo-bg); 
    color: var(--badge-photo-color); 
}

.media-type-video { 
    background: var(--badge-video-bg); 
    color: var(--badge-video-color); 
}

.media-type-document { 
    background: var(--badge-document-bg); 
    color: var(--badge-document-color); 
}

.loading {
    display: flex; 
    justify-content: center; 
    align-items: center;
    padding: 60px; 
    color: var(--text-muted);
}

.loading-spinner {
    width: 32px; 
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%; 
    animation: spin 1s linear infinite; 
    margin-right: 16px;
}

@keyframes spin { 
    0% { transform: rotate(0deg); } 
    100% { transform: rotate(360deg); } 
}

.error { 
    background: var(--error-gradient); 
    color: var(--error-color); 
    padding: var(--spacing-lg); 
    border-radius: var(--radius-medium); 
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--error-border);
    box-shadow: var(--shadow-error);
}

.empty-state { 
    text-align: center; 
    padding: 60px; 
    color: var(--text-light);
    font-weight: 500;
}

/* 底部标签栏样式 - 紧凑设计 + 智能安全区域处理 */
.tab-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    background: var(--bg-white);
    border-top: 1px solid var(--border-modal);
    /* 减少padding，使标签栏更紧凑 */
    padding: 8px;
    padding-bottom: calc(8px + var(--dynamic-safe-area-bottom, var(--content-safe-area-inset-bottom)));
    padding-left: calc(8px + var(--content-safe-area-inset-left));
    padding-right: calc(8px + var(--content-safe-area-inset-right));
    z-index: 1000;
    box-shadow: var(--shadow-tab);
    
    /* 键盘显示时的特殊处理 */
    transition: transform 0.3s ease;
}

.tab-button {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6px var(--spacing-xs);
    border: none;
    background: transparent;
    color: var(--text-light);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    min-height: 42px;
    border-radius: var(--radius-medium);
}

.tab-button.active {
    color: var(--primary-dark);
    background: var(--bg-tab-active);
}

.tab-button.active .tab-icon {
    transform: scale(1.1);
}

.tab-button.active .tab-label {
    font-weight: 600;
}

.tab-button:hover:not(.active) {
    color: var(--text-secondary);
    transform: translateY(-2px);
    background: var(--bg-hover-light);
}

.tab-icon {
    font-size: 20px;
    margin-bottom: 2px;
    line-height: 1;
    transition: transform 0.3s ease;
}

.tab-label {
    font-size: 10px;
    line-height: 1;
    font-weight: 500;
    transition: font-weight 0.3s ease;
}

/* 页面内容样式 */
.tab-content {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

.tab-content.hidden {
    display: none;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 广告管理样式 */
.primary-button {
    background: var(--primary-gradient);
    color: white; 
    border: none; 
    padding: var(--spacing-sm) var(--spacing-xl); 
    border-radius: var(--radius-medium);
    font-size: 14px; 
    font-weight: 600; 
    cursor: pointer; 
    transition: all 0.3s ease;
    box-shadow: var(--shadow-primary);
}

.primary-button:hover { 
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary-hover);
}

.campaign-card {
    background: var(--bg-white-transparent);
    border-radius: var(--radius-large); 
    padding: var(--spacing-lg); 
    margin-bottom: var(--spacing-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 
    inset 0 -2px 0 0 var(--primary-color), /* 装饰条 */
    var(--shadow-light); /* 普通外阴影 */
}

.campaign-card:hover {
    transform: translateY(-2px);
    box-shadow: 
    inset 0 -2px 0 0 var(--primary-color), /* 装饰条 */
    var(--shadow-medium); /* 普通外阴影 */
}

.campaign-header {
    display: flex; 
    justify-content: space-between; 
    align-items: flex-start;
    margin-bottom: 12px;
}

.campaign-title { 
    font-size: 16px; 
    font-weight: 600; 
    margin-bottom: 4px;
    color: var(--text-primary);
}

.campaign-status {
    padding: 6px var(--spacing-sm); 
    border-radius: 20px; 
    font-size: 11px;
    font-weight: 600; 
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    min-width: fit-content;
}

.status-active { 
    background: var(--active-gradient); 
    color: var(--active-color);
    border: 1px solid var(--active-border);
    padding: 4px 8px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-inactive { 
    background: var(--inactive-gradient); 
    color: var(--inactive-color);
    border: 1px solid var(--inactive-border);
    padding: 4px 8px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badges-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
}

.timezone-badge {
    background: linear-gradient(135deg, var(--text-light) 0%, var(--text-muted) 100%);
    color: white;
    padding: 4px var(--spacing-xs);
    border-radius: var(--radius-medium);
    font-size: 10px;
    font-weight: 600;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.secondary-button {
    background: var(--bg-white-transparent);
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    padding: 10px var(--spacing-lg);
    border-radius: var(--radius-medium);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-button:hover {
    background: var(--bg-light-gray);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-modal-overlay);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: var(--bg-white);
    border-radius: var(--spacing-lg);
    position: relative;
    color: var(--text-primary);
    display: flex;
    flex-direction: column;
    width: 90vw;
    max-width: 600px;
    max-height: 90vh;
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--border-white);
    animation: slideUp 0.3s ease-out;
}

.modal-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid var(--border-modal);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    background: var(--bg-gradient-dark);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close {
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: var(--text-light);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-small);
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--bg-hover-modal);
    color: var(--text-secondary);
}

.modal-body {
    padding: 20px 24px;
    flex: 1;
    overflow: auto;
}

.modal-footer {
    padding: 16px 24px 24px 24px;
    border-top: 1px solid var(--border-modal);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-medium);
    background: white;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus-light);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* 自定义勾选框样式 */
.form-checkbox {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: white;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    vertical-align: middle;
}

.form-checkbox:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.1);
}

.form-checkbox:checked {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.2);
}

.form-checkbox:checked::after {
    content: '✓';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
    font-weight: bold;
}

.form-checkbox:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.2);
}

/* 勾选框标签容器优化 */
.form-group label:has(.form-checkbox) {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 12px 2px;
    margin: 0;
    user-select: none;
    border-radius: 8px;
    transition: background 0.2s ease;
    gap: 8px;
    width: 100%;
}

.form-group label:has(.form-checkbox):hover {
    /* padding-left: 8px;
    padding-right: 8px; */
}

.form-group label:has(.form-checkbox) .form-label {
    margin: 0;
    font-weight: 500;
    flex: 1;
}

.form-group label:has(.form-checkbox) small {
    font-size: 11px;
    color: var(--text-muted);
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.form-group label:has(.form-checkbox):hover small {
    opacity: 1;
}

/* 输入组合样式 */
.input-group {
    display: flex;
    align-items: stretch;
    border-radius: var(--radius-medium);
    overflow: hidden;
    border: 2px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.1);
}

.input-group .form-input {
    flex: 1;
    border: none;
    border-radius: 0;
    margin: 0;
}

.input-group .form-input:focus {
    box-shadow: none;
    border: none;
}

/* 粘贴按钮样式 */
.paste-button {
    background: var(--bg-light-gray);
    border: none;
    padding: 0 16px;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-left: 1px solid var(--border-color);
}

.paste-button:hover {
    background: var(--primary-color);
    color: white;
    border-left-color: var(--primary-color);
}

.paste-button:active {
    transform: scale(0.95);
}

.paste-button:disabled {
    background: var(--bg-disabled);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.paste-button:disabled:hover {
    background: var(--bg-disabled);
    color: var(--text-muted);
    transform: none;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.campaign-details {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
}

.campaign-actions {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    flex-wrap: wrap;
}

.action-button {
    padding: var(--spacing-xs) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-small);
    background: white;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-button:hover {
    background: var(--bg-light-gray);
    border-color: var(--border-secondary);
}

.action-button.danger {
    border-color: var(--error-border);
    color: var(--error-color);
}

.action-button.danger:hover {
    background: var(--error-gradient);
}

/* 按钮布局样式 */
.action-buttons-container {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.secondary-buttons-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* 广告规则模态框特定样式 */
.campaign-modal-content {
    max-height: 90vh;
    width: 90vw;
    max-width: 500px;
}

.campaign-modal-body {
    max-height: calc(90vh - 180px);
    overflow-y: auto;
    padding: 24px;
}

.campaign-modal-body .form-group:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }
    
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        margin-bottom: var(--spacing-sm);
    }
    
    .stat-card {
        min-height: 75px;
        padding: 12px 8px;
        margin: 0;
    }
    
    .stat-number {
        font-size: 18px;
        margin-bottom: 4px;
    }
    
    .stat-label {
        letter-spacing: 0.3px;
    }
    
    .filters {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: var(--spacing-md);
        background: var(--bg-white);
        border-radius: var(--radius-large);
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--shadow-light);
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-label {
        font-size: 12px;
        margin-bottom: 6px;
    }
    
    .filter-input, .filter-select {
        padding: 12px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    /* 移动端表格优化 - 卡片式布局 */
    #mediaTab .table-container {
        background: transparent;
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    
    #mediaTab .table-container .table,
    #mediaTable {
        display: none !important; /* 移动端强制隐藏表格 */
    }
    
    .mobile-cards {
        display: block !important; /* 移动端强制显示卡片 */
        grid-template-columns: 1fr !important; /* 移动端单列显示 */
        padding: 0 !important;
        gap: 0 !important; /* 移动端不使用网格间距 */
    }
    
    /* 保持模态框内表格正常显示 */
    .modal .table-container,
    .modal .table,
    #dataTableContainer,
    #dataTable {
        display: block !important;
    }
    
    /* 确保弹窗表格容器正常显示 */
    #dataTableModal .modal-body,
    #dataTableModal .table-container {
        display: block !important;
    }
    
    /* 移动端卡片布局 - 强制单列显示，恢复margin */
    .mobile-card {
        margin-bottom: var(--spacing-md) !important;
    }
    
    .section-title {
        font-size: 20px;
        margin-bottom: var(--spacing-sm);
    }
    
    /* 移动端隐藏桌面端专用元素 */
    .desktop-only {
        display: none !important;
    }
    
    /* 移动端按钮优化 */
    .action-buttons-container {
        gap: 16px;
    }
    
    .secondary-buttons-group {
        flex-direction: column;
        gap: 8px;
    }
    
    .secondary-buttons-group .secondary-button {
        text-align: center;
    }
    
    /* 移动端输入组合优化 */
    .input-group {
        border-radius: var(--radius-medium);
    }
    
    .input-group .form-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px 16px;
    }
    
    .paste-button {
        min-width: 52px;
        font-size: 18px;
        padding: 0 12px;
    }
    
    /* 移动端模态框优化 */
    .campaign-modal-content {
        width: 95vw;
        max-width: none;
        margin: 10px;
    }
    
    .campaign-modal-body {
        padding: 20px;
        max-height: calc(90vh - 160px);
    }
    
    .modal-header {
        padding: 20px 20px 12px 20px;
    }
    
    .modal-footer {
        padding: 12px 20px 20px 20px;
        flex-direction: column;
        gap: 8px;
    }
    
    .modal-footer .secondary-button,
    .modal-footer .primary-button {
        width: 100%;
        justify-content: center;
    }
}

/* 媒体弹窗样式 */
.media-modal-content {
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    height: auto;
}

.media-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.media-detail-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.media-preview-large {
    text-align: center;
    flex-shrink: 0;
}

.media-info {
    background: var(--bg-neutral);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md);
    border: 1px solid var(--border-lighter);
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    font-size: 14px;
    line-height: 1.4;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item strong {
    min-width: 80px;
    color: var(--text-primary);
    font-weight: 600;
    margin-right: var(--spacing-sm);
    flex-shrink: 0;
}

.info-item code {
    background: var(--bg-code);
    color: var(--text-code);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    word-break: break-all;
}

/* 移动端媒体弹窗优化 */
@media (max-width: 768px) {
    .media-modal-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: 20px 10px;
    }
    
    .media-modal-body {
        padding: var(--spacing-md);
        max-height: 75vh;
    }
    
    .media-detail-container {
        gap: var(--spacing-md);
    }
    
    .info-item {
        flex-direction: column;
        gap: 4px;
    }
    
    .info-item strong {
        min-width: auto;
        margin-right: 0;
    }
}

/* ==========================================================================
   群组管理样式
   ========================================================================== */

/* 单选按钮组 */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.radio-option {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-card);
}

.radio-option:hover {
    border-color: var(--primary-color);
    background: var(--bg-hover);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
    background: var(--bg-card);
    transition: all 0.2s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary-color);
    background: var(--primary-color);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.option-content {
    flex: 1;
}

.option-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.option-desc {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 开关样式 */
.toggles-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.toggle-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
}

.toggle-content {
    flex: 1;
}

.toggle-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.toggle-desc {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 28px;
    flex-shrink: 0;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: 0.3s;
    border-radius: 28px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 规则和目标列表 */
.rules-container, .targets-list, .admins-list {
    margin-top: var(--spacing-sm);
}

.rule-item, .target-item, .admin-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    margin-bottom: var(--spacing-xs);
}

.rule-info, .target-info, .admin-info {
    flex: 1;
    min-width: 0;
}

.rule-title, .target-title, .admin-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.rule-url, .target-id, .admin-id {
    font-size: 14px;
    color: var(--text-secondary);
    word-break: break-all;
}

.admin-granted, .admin-time {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 2px;
}

.rule-actions, .admin-actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-shrink: 0;
}

.status-icon {
    font-size: 14px;
}

.status-icon.success {
    color: var(--success-color);
}

.status-icon.error {
    color: var(--error-color);
}

/* 小按钮样式 */
.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    min-height: unset;
}

/* 子标题 */
.sub-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

/* 操作按钮区域 */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* 空状态 */
.empty-state {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-lg);
    font-style: italic;
}

/* 区段样式 */
.section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.section-desc {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.sub-section {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-light);
}

/* 群组选择容器 */
.group-select-container {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.group-select-container .filter-select {
    flex: 1;
}

.group-action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

/* 信息提示框 */
.alert {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    margin: var(--spacing-sm) 0;
    border: 1px solid;
}

.alert-info {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1565c0;
}

.alert strong {
    display: block;
    margin-bottom: var(--spacing-xs);
}

.alert ul {
    margin: 0;
    padding-left: var(--spacing-md);
    font-size: 14px;
    line-height: 1.4;
}

.alert li {
    margin-bottom: 4px;
}

/* 权限徽章 */
.permission-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: var(--spacing-xs);
}

.permission-badge.super-admin {
    background-color: #ff5722;
    color: white;
}

.permission-badge.admin {
    background-color: #2196f3;
    color: white;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .rule-item, .target-item, .admin-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .rule-actions, .admin-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .toggles-grid {
        grid-template-columns: 1fr;
    }
    
    .group-select-container {
        flex-direction: column;
        align-items: stretch;
    }
}

/* #region 🍞 Toast 通知 */
.toast {
	position: fixed;
	top: 20px;
	left: 50%;
	transform: translateX(-50%);
	background: var(--bg-secondary);
	color: var(--text-primary);
	padding: 12px 24px;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	z-index: 1001;
	display: none;
	border: 1px solid var(--border-color);
}

.toast.show {
	display: block;
	animation: slideDown 0.3s ease;
}

.toast.success {
	background: #48bb78;
	color: white;
	border-color: #38a169;
}

.toast.error {
	background: #f56565;
	color: white;
	border-color: #e53e3e;
}

.toast.warning {
	background: #f59e0b;
	color: white;
	border-color: #d97706;
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(-10px);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}
/* #endregion */

/* #region 📋 权限要求模态框 */
.alert-error {
	background-color: #fee;
	border-color: #f56565;
	color: #e53e3e;
	margin-bottom: var(--spacing-md);
}

.requirements-section {
	margin: var(--spacing-md) 0;
}

.requirements-section h4 {
	color: var(--text-primary);
	margin-bottom: var(--spacing-sm);
	font-size: 16px;
	border-bottom: 2px solid var(--border-color);
	padding-bottom: var(--spacing-xs);
}

.requirement-group {
	margin-bottom: var(--spacing-md);
	padding: var(--spacing-sm);
	background: var(--bg-card);
	border-radius: var(--border-radius);
	border-left: 4px solid #2196f3;
}

.requirement-group h5 {
	color: var(--text-primary);
	margin-bottom: var(--spacing-xs);
	font-size: 14px;
	font-weight: 600;
}

.requirement-list {
	margin: 0;
	padding-left: var(--spacing-md);
}

.requirement-list li {
	margin-bottom: var(--spacing-xs);
	line-height: 1.5;
	color: var(--text-secondary);
}

.requirement-list strong {
	color: var(--text-primary);
}

.requirement-list code {
	background: var(--bg-tertiary);
	padding: 2px 6px;
	border-radius: 4px;
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	font-size: 13px;
	color: var(--text-primary);
}

.help-section {
	margin-top: var(--spacing-md);
	padding: var(--spacing-sm);
	background: #f8f9fa;
	border-radius: var(--border-radius);
	border-left: 4px solid #17a2b8;
}

.help-section h4 {
	color: #17a2b8;
	margin-bottom: var(--spacing-xs);
	font-size: 14px;
}

.help-list {
	margin: 0;
	padding-left: var(--spacing-md);
}

.help-list li {
	margin-bottom: var(--spacing-xs);
	line-height: 1.5;
	color: #495057;
}

.help-list strong {
	color: #212529;
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	background: #e9ecef;
	padding: 1px 4px;
	border-radius: 3px;
}

/* 移动端适配 */
@media (max-width: 480px) {
	#permissionRequirementModal .modal-content {
		margin: 10px;
		max-height: 90vh;
		overflow-y: auto;
	}
	
	.requirement-group {
		padding: var(--spacing-xs);
		margin-bottom: var(--spacing-sm);
	}
	
	.requirements-section h4 {
		font-size: 14px;
	}
	
	.requirement-group h5 {
		font-size: 13px;
	}
	
	.requirement-list li,
	.help-list li {
		font-size: 13px;
	}
	
	.alert-error {
		padding: var(--spacing-xs);
	}
	
	.help-section {
		padding: var(--spacing-xs);
	}
}
/* #endregion */

/* #region 🗑️ 删除群组模态框 */
.delete-group-info {
	margin: var(--spacing-md) 0;
}

.group-info-card {
	background: var(--bg-card);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius);
	padding: var(--spacing-sm);
	margin-top: var(--spacing-xs);
}

.group-name {
	font-weight: 600;
	font-size: 16px;
	color: var(--text-primary);
	margin-bottom: var(--spacing-xs);
}

.group-id {
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	font-size: 14px;
	color: var(--text-secondary);
	background: var(--bg-tertiary);
	padding: 4px 8px;
	border-radius: 4px;
	display: inline-block;
}

.delete-consequences {
	margin: var(--spacing-md) 0;
	padding: var(--spacing-sm);
	background: #fff5f5;
	border: 1px solid #fca5a5;
	border-radius: var(--border-radius);
}

.delete-consequences h4 {
	color: #dc2626;
	margin-bottom: var(--spacing-xs);
	font-size: 14px;
}

.consequence-list {
	margin: 0;
	padding-left: var(--spacing-md);
}

.consequence-list li {
	margin-bottom: var(--spacing-xs);
	color: #7f1d1d;
	font-size: 14px;
	line-height: 1.4;
}

.confirm-text {
	margin-top: var(--spacing-md);
	padding: var(--spacing-sm);
	background: #fefefe;
	border: 2px dashed #d1d5db;
	border-radius: var(--border-radius);
}

.confirm-text p {
	margin-bottom: var(--spacing-xs);
	color: var(--text-primary);
	font-weight: 600;
}

.btn-danger {
	background-color: #dc2626;
	color: white;
	border: 1px solid #dc2626;
}

.btn-danger:hover:not(:disabled) {
	background-color: #b91c1c;
	border-color: #b91c1c;
}

.btn-danger:disabled {
	background-color: #f3f4f6;
	color: #9ca3af;
	border-color: #d1d5db;
	cursor: not-allowed;
}

/* 移动端适配 */
@media (max-width: 480px) {
	.group-action-buttons {
		flex-direction: column;
	}
	
	.delete-consequences {
		padding: var(--spacing-xs);
	}
	
	.consequence-list li {
		font-size: 13px;
	}
	
	#deleteGroupModal .modal-content {
		margin: 10px;
		max-height: 90vh;
		overflow-y: auto;
	}
}
/* #endregion */
