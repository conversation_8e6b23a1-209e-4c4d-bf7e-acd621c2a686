/**
 * 群配置管理页面 JavaScript
 * 处理群组配置的交互逻辑和API调用
 */

// #region 🏗️ 全局变量和初始化
let currentConfig = null;
let selectedGroupId = null;
let isLoading = false;

// DOM 元素引用
const elements = {
	loadingIndicator: document.getElementById('loadingIndicator'),
	mainContent: document.getElementById('mainContent'),
	groupSelect: document.getElementById('groupSelect'),
	configPanel: document.getElementById('configPanel'),
	actionButtons: document.getElementById('actionButtons'),
	statsPanel: document.getElementById('statsPanel'),
	reportTargetsSection: document.getElementById('reportTargetsSection'),
	targetsList: document.getElementById('targetsList'),
	addTargetBtn: document.getElementById('addTargetBtn'),
	saveConfigBtn: document.getElementById('saveConfigBtn'),
	resetConfigBtn: document.getElementById('resetConfigBtn'),
	addTargetModal: document.getElementById('addTargetModal'),
	targetChatId: document.getElementById('targetChatId'),
	targetTitle: document.getElementById('targetTitle'),
	toast: document.getElementById('toast'),
	toastMessage: document.getElementById('toastMessage')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', initializeApp);

async function initializeApp() {
	try {
		logger.info('群配置管理页面初始化开始');
		
		// 检查认证状态
		if (!checkAuth()) {
			logger.warn('用户未认证，跳转到认证页面');
			return;
		}
		
		// 绑定事件监听器
		bindEventListeners();
		
		// 加载群组列表
		await loadGroupsList();
		
		// 隐藏加载指示器，显示主要内容
		elements.loadingIndicator.style.display = 'none';
		elements.mainContent.style.display = 'block';
		
		logger.info('群配置管理页面初始化完成');
	} catch (error) {
		logger.error('初始化失败:', error);
		showToast('初始化失败: ' + error.message, 'error');
	}
}

// #endregion 🏗️ 全局变量和初始化

// #region 🎯 事件监听器
function bindEventListeners() {
	// 群组选择变化
	elements.groupSelect.addEventListener('change', handleGroupSelectionChange);
	
	// 举报模式选择
	document.querySelectorAll('input[name="reportMode"]').forEach(radio => {
		radio.addEventListener('change', handleReportModeChange);
	});
	
	// 功能开关
	document.getElementById('newMemberVerification').addEventListener('change', handleConfigChange);
	document.getElementById('cloudFilterEnabled').addEventListener('change', handleConfigChange);
	document.getElementById('autoForwardEnabled').addEventListener('change', handleConfigChange);
	
	// 按钮点击
	elements.addTargetBtn.addEventListener('click', showAddTargetModal);
	elements.saveConfigBtn.addEventListener('click', saveConfiguration);
	elements.resetConfigBtn.addEventListener('click', resetConfiguration);
	
	// 模态框相关
	document.getElementById('closeModal').addEventListener('click', hideAddTargetModal);
	document.getElementById('cancelAddTarget').addEventListener('click', hideAddTargetModal);
	document.getElementById('confirmAddTarget').addEventListener('click', addReportTarget);
	
	// 点击模态框外部关闭
	elements.addTargetModal.addEventListener('click', (e) => {
		if (e.target === elements.addTargetModal) {
			hideAddTargetModal();
		}
	});
	
	// 绑定群规管理事件监听器
	bindRuleEventListeners();
}

async function handleGroupSelectionChange() {
	const groupId = elements.groupSelect.value;
	if (!groupId) {
		hideConfigPanel();
		return;
	}
	
	selectedGroupId = groupId;
	await loadGroupConfiguration(groupId);
	showConfigPanel();
}

function handleReportModeChange() {
	const selectedMode = document.querySelector('input[name="reportMode"]:checked')?.value;
	
	if (selectedMode === 'group') {
		elements.reportTargetsSection.style.display = 'block';
	} else {
		elements.reportTargetsSection.style.display = 'none';
	}
	
	handleConfigChange();
}

function handleConfigChange() {
	// 启用保存按钮，表示有更改
	elements.saveConfigBtn.disabled = false;
}

// #endregion 🎯 事件监听器

// #region 📊 数据加载
async function loadGroupsList() {
	try {
		logger.info('加载群组列表...');
		
		const response = await fetch('/api/group-config/groups', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			}
		});
		
		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}
		
		const data = await response.json();
		
		// 清空现有选项
		elements.groupSelect.innerHTML = '<option value="">请选择要配置的群组...</option>';
		
		// 添加群组选项
		data.groups.forEach(group => {
			const option = document.createElement('option');
			option.value = group.chat_id;
			option.textContent = `${group.chat_title || group.chat_id} (${group.chat_id})`;
			elements.groupSelect.appendChild(option);
		});
		
		logger.info(`加载了 ${data.groups.length} 个群组`);
	} catch (error) {
		logger.error('加载群组列表失败:', error);
		showToast('加载群组列表失败: ' + error.message, 'error');
	}
}

async function loadGroupConfiguration(groupId) {
	try {
		setLoading(true);
		logger.info('加载群组配置:', groupId);
		
		const response = await fetch(`/api/group-config/config/${encodeURIComponent(groupId)}`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			}
		});
		
		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}
		
		const data = await response.json();
		currentConfig = data.config;
		
		// 更新UI
		updateConfigurationUI(currentConfig);
		
		// 加载统计信息
		await loadReportStats(groupId);
		
		logger.info('群组配置加载完成');
	} catch (error) {
		logger.error('加载群组配置失败:', error);
		showToast('加载群组配置失败: ' + error.message, 'error');
	} finally {
		setLoading(false);
	}
}

async function loadReportStats(groupId) {
	try {
		const response = await fetch(`/api/group-config/stats/${encodeURIComponent(groupId)}`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${getAuthToken()}`
			}
		});
		
		if (response.ok) {
			const stats = await response.json();
			updateStatsUI(stats);
			elements.statsPanel.style.display = 'block';
		}
	} catch (error) {
		logger.warn('加载统计信息失败:', error);
		// 不显示错误，统计信息是可选的
	}
}

// #endregion 📊 数据加载

// #region 🎨 UI 更新
function updateConfigurationUI(config) {
	if (!config) {
		// 使用默认配置
		config = {
			report_mode: 'disabled',
			report_target_groups: [],
			new_member_verification: false,
			cloud_filter_enabled: false,
			auto_forward_enabled: false
		};
	}
	
	// 更新举报模式
	const reportModeRadio = document.querySelector(`input[name="reportMode"][value="${config.report_mode}"]`);
	if (reportModeRadio) {
		reportModeRadio.checked = true;
		handleReportModeChange();
	}
	
	// 更新目标群组列表
	updateTargetGroupsList(config.report_target_groups || []);
	
	// 更新功能开关
	document.getElementById('newMemberVerification').checked = config.new_member_verification || false;
	document.getElementById('cloudFilterEnabled').checked = config.cloud_filter_enabled || false;
	document.getElementById('autoForwardEnabled').checked = config.auto_forward_enabled || false;
	
	// 加载群规配置
	loadGroupRules(config);
	
	// 重置保存按钮状态
	elements.saveConfigBtn.disabled = true;
}

function updateTargetGroupsList(targets) {
	elements.targetsList.innerHTML = '';
	
	if (!targets || targets.length === 0) {
		elements.targetsList.innerHTML = `
			<div class="empty-targets">
				<div class="empty-icon">📭</div>
				<p>暂无推送目标群组</p>
			</div>
		`;
		return;
	}
	
	targets.filter(target => target.is_active !== false).forEach(target => {
		const targetElement = createTargetElement(target);
		elements.targetsList.appendChild(targetElement);
	});
}

function createTargetElement(target) {
	const div = document.createElement('div');
	div.className = 'target-item';
	div.innerHTML = `
		<div class="target-info">
			<div class="target-title">${escapeHtml(target.title || '未知群组')}</div>
			<div class="target-id">${escapeHtml(target.chat_id)}</div>
		</div>
		<button class="remove-target" onclick="removeReportTarget('${target.chat_id}')">
			🗑️ 移除
		</button>
	`;
	return div;
}

function updateStatsUI(stats) {
	document.getElementById('totalReports').textContent = stats.total_reports || 0;
	document.getElementById('pendingReports').textContent = stats.pending_reports || 0;
	document.getElementById('handledReports').textContent = stats.handled_reports || 0;
}

function showConfigPanel() {
	elements.configPanel.style.display = 'block';
	elements.actionButtons.style.display = 'flex';
}

function hideConfigPanel() {
	elements.configPanel.style.display = 'none';
	elements.actionButtons.style.display = 'none';
	elements.statsPanel.style.display = 'none';
}

// #endregion 🎨 UI 更新

// #region 🎯 目标群组管理
function showAddTargetModal() {
	elements.targetChatId.value = '';
	elements.targetTitle.value = '';
	elements.addTargetModal.style.display = 'flex';
}

function hideAddTargetModal() {
	elements.addTargetModal.style.display = 'none';
}

async function addReportTarget() {
	const chatId = elements.targetChatId.value.trim();
	const title = elements.targetTitle.value.trim();
	
	if (!chatId) {
		showToast('请输入群组ID', 'error');
		return;
	}
	
	if (!title) {
		showToast('请输入群组名称', 'error');
		return;
	}
	
	// 验证群组ID格式
	if (!chatId.match(/^-100\d+$/)) {
		showToast('群组ID格式错误，应以-100开头', 'error');
		return;
	}
	
	try {
		// 添加到当前配置
		if (!currentConfig.report_target_groups) {
			currentConfig.report_target_groups = [];
		}
		
		// 检查是否已存在
		const exists = currentConfig.report_target_groups.some(target => target.chat_id === chatId);
		if (exists) {
			showToast('该群组已存在', 'error');
			return;
		}
		
		currentConfig.report_target_groups.push({
			chat_id: chatId,
			title: title,
			is_active: true,
			added_at: new Date().toISOString()
		});
		
		// 更新UI
		updateTargetGroupsList(currentConfig.report_target_groups);
		hideAddTargetModal();
		handleConfigChange();
		
		showToast('管理群添加成功', 'success');
	} catch (error) {
		logger.error('添加目标群组失败:', error);
		showToast('添加失败: ' + error.message, 'error');
	}
}

function removeReportTarget(chatId) {
	if (!confirm('确定要移除这个管理群吗？')) {
		return;
	}
	
	try {
		// 标记为非活跃状态（软删除）
		if (currentConfig.report_target_groups) {
			currentConfig.report_target_groups = currentConfig.report_target_groups.map(target => {
				if (target.chat_id === chatId) {
					return { ...target, is_active: false };
				}
				return target;
			});
		}
		
		// 更新UI
		updateTargetGroupsList(currentConfig.report_target_groups);
		handleConfigChange();
		
		showToast('管理群已移除', 'success');
	} catch (error) {
		logger.error('移除目标群组失败:', error);
		showToast('移除失败: ' + error.message, 'error');
	}
}

// #endregion 🎯 目标群组管理

// #region 💾 配置保存
async function saveConfiguration() {
	if (!selectedGroupId) {
		showToast('请先选择群组', 'error');
		return;
	}
	
	try {
		setLoading(true);
		logger.info('保存群组配置:', selectedGroupId);
		
		// 收集当前配置
		const config = collectCurrentConfiguration();
		
		const response = await fetch('/api/group-config/save', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({
				chat_id: selectedGroupId,
				...config
			})
		});
		
		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
		}
		
		const result = await response.json();
		
		if (result.success) {
			// 显示保存结果，包含群规验证信息
			let toastMessage = result.message || '配置保存成功';
			let toastType = 'success';
			
			// 如果有群规验证结果，显示详细信息
			if (result.rulesResult && result.rulesResult.summary) {
				const { total, accessible, failed } = result.rulesResult.summary;
				if (failed > 0) {
					toastType = 'warning';
					toastMessage += `\n群规验证: ${accessible}/${total} 可访问`;
					
					// 显示错误详情
					if (result.rulesResult.errors && result.rulesResult.errors.length > 0) {
						console.warn('群规验证错误:', result.rulesResult.errors);
					}
				}
			}
			
			showToast(toastMessage, toastType);
			elements.saveConfigBtn.disabled = true;
			
			// 更新当前配置
			currentConfig = { ...currentConfig, ...config };
		} else {
			throw new Error(result.error || '保存失败');
		}
		
		logger.info('群组配置保存成功');
	} catch (error) {
		logger.error('保存群组配置失败:', error);
		showToast('保存失败: ' + error.message, 'error');
	} finally {
		setLoading(false);
	}
}

function collectCurrentConfiguration() {
	const reportMode = document.querySelector('input[name="reportMode"]:checked')?.value || 'disabled';
	
	return {
		report_mode: reportMode,
		report_target_groups: currentConfig?.report_target_groups || [],
		new_member_verification: document.getElementById('newMemberVerification').checked,
		cloud_filter_enabled: document.getElementById('cloudFilterEnabled').checked,
		auto_forward_enabled: document.getElementById('autoForwardEnabled').checked,
		rules: getGroupRulesForSave()
	};
}

async function resetConfiguration() {
	if (!confirm('确定要重置配置吗？这将恢复到默认设置。')) {
		return;
	}
	
	try {
		// 重新加载配置
		if (selectedGroupId) {
			await loadGroupConfiguration(selectedGroupId);
		}
		
		showToast('配置已重置', 'success');
	} catch (error) {
		logger.error('重置配置失败:', error);
		showToast('重置失败: ' + error.message, 'error');
	}
}

// #endregion 💾 配置保存

// #region 🔧 工具函数
function setLoading(loading) {
	isLoading = loading;
	
	if (loading) {
		elements.saveConfigBtn.disabled = true;
		elements.resetConfigBtn.disabled = true;
	} else {
		elements.resetConfigBtn.disabled = false;
		// saveConfigBtn 的状态由配置变化决定
	}
}

function showToast(message, type = 'info') {
	elements.toastMessage.textContent = message;
	elements.toast.className = `toast ${type} show`;
	
	setTimeout(() => {
		elements.toast.classList.remove('show');
	}, 3000);
}

function escapeHtml(text) {
	const div = document.createElement('div');
	div.textContent = text;
	return div.innerHTML;
}

function getAuthToken() {
	// 从 Telegram WebApp 获取认证令牌
	if (!window.Telegram?.WebApp?.initData) {
		throw new Error('无法获取Telegram认证数据');
	}
	return window.Telegram.WebApp.initData;
}

function checkAuth() {
	// 从 auth.js 检查认证状态
	return window.isAuthenticated || false;
}

// #endregion 🔧 工具函数

// #region 📝 群规管理功能
let currentRules = [];
let editingRuleIndex = -1;

// 群规管理相关DOM元素
const ruleElements = {
	rulesList: document.getElementById('rulesList'),
	addRuleBtn: document.getElementById('addRuleBtn'),
	ruleModal: document.getElementById('ruleModal'),
	ruleModalTitle: document.getElementById('ruleModalTitle'),
	ruleUrl: document.getElementById('ruleUrl'),
	ruleTitle: document.getElementById('ruleTitle'),
	saveRuleBtn: document.getElementById('saveRuleBtn'),
	cancelRuleBtn: document.getElementById('cancelRuleBtn'),
	closeRuleModal: document.getElementById('closeRuleModal')
};

// 绑定群规管理事件监听器
function bindRuleEventListeners() {
	ruleElements.addRuleBtn.addEventListener('click', showAddRuleModal);
	ruleElements.saveRuleBtn.addEventListener('click', saveRule);
	ruleElements.cancelRuleBtn.addEventListener('click', hideRuleModal);
	ruleElements.closeRuleModal.addEventListener('click', hideRuleModal);
	
	// 点击模态框外部关闭
	ruleElements.ruleModal.addEventListener('click', (e) => {
		if (e.target === ruleElements.ruleModal) {
			hideRuleModal();
		}
	});
}

// 显示添加群规模态框
function showAddRuleModal() {
	editingRuleIndex = -1;
	ruleElements.ruleModalTitle.textContent = '添加群规';
	ruleElements.ruleUrl.value = '';
	ruleElements.ruleTitle.value = '';
	ruleElements.ruleModal.style.display = 'flex';
}

// 显示编辑群规模态框
function showEditRuleModal(index) {
	if (index < 0 || index >= currentRules.length) return;
	
	editingRuleIndex = index;
	const rule = currentRules[index];
	ruleElements.ruleModalTitle.textContent = '编辑群规';
	ruleElements.ruleUrl.value = rule.url;
	ruleElements.ruleTitle.value = rule.title || '';
	ruleElements.ruleModal.style.display = 'flex';
}

// 隐藏群规模态框
function hideRuleModal() {
	ruleElements.ruleModal.style.display = 'none';
	editingRuleIndex = -1;
}

// 保存群规
async function saveRule() {
	const url = ruleElements.ruleUrl.value.trim();
	const title = ruleElements.ruleTitle.value.trim() || '群规';
	
	// 验证URL
	if (!url) {
		showToast('请输入群规链接', 'error');
		return;
	}
	
	if (!isValidTelegramUrl(url)) {
		showToast('请输入有效的Telegram链接', 'error');
		return;
	}
	
	// 检查重复链接
	const existingIndex = currentRules.findIndex(rule => rule.url === url);
	if (existingIndex !== -1 && existingIndex !== editingRuleIndex) {
		showToast('该链接已存在', 'error');
		return;
	}
	
	// 验证访问权限
	ruleElements.saveRuleBtn.disabled = true;
	ruleElements.saveRuleBtn.textContent = '验证中...';
	
	try {
		const verifyResult = await verifyRuleAccess([{ url, title }]);
		
		if (verifyResult.success && verifyResult.results.length > 0) {
			const ruleResult = verifyResult.results[0];
			let newRule = { url, title };
			
			// 添加验证状态信息
			if (ruleResult.status === 'accessible') {
				newRule.canAccess = true;
				newRule.chatTitle = ruleResult.chatInfo?.title;
			} else {
				newRule.canAccess = false;
				newRule.accessError = ruleResult.error;
				
				// 询问是否仍要添加
				if (!confirm(`该链接无法访问: ${ruleResult.error}\n\n是否仍要添加？`)) {
					return;
				}
			}
			
			if (editingRuleIndex === -1) {
				// 添加新规则
				if (currentRules.length >= 10) {
					showToast('最多只能添加10条群规', 'error');
					return;
				}
				currentRules.push(newRule);
			} else {
				// 编辑现有规则
				currentRules[editingRuleIndex] = newRule;
			}
			
			renderRulesList();
			hideRuleModal();
			handleConfigChange(); // 标记为已修改
			
			const statusText = newRule.canAccess ? '✅ 可访问' : '❌ 无法访问';
			showToast(`${editingRuleIndex === -1 ? '群规添加成功' : '群规修改成功'} (${statusText})`, 
					  newRule.canAccess ? 'success' : 'warning');
		} else {
			showToast('验证失败，请检查网络连接', 'error');
		}
	} catch (error) {
		console.error('验证群规访问权限失败:', error);
		showToast('验证失败，但仍可保存', 'warning');
		
		// 验证失败时仍允许保存
		const newRule = { url, title, canAccess: null };
		
		if (editingRuleIndex === -1) {
			if (currentRules.length >= 10) {
				showToast('最多只能添加10条群规', 'error');
				return;
			}
			currentRules.push(newRule);
		} else {
			currentRules[editingRuleIndex] = newRule;
		}
		
		renderRulesList();
		hideRuleModal();
		handleConfigChange();
	} finally {
		ruleElements.saveRuleBtn.disabled = false;
		ruleElements.saveRuleBtn.textContent = '保存';
	}
}

// 删除群规
function removeRule(index) {
	if (index < 0 || index >= currentRules.length) return;
	
	if (confirm('确定要删除这条群规吗？')) {
		currentRules.splice(index, 1);
		renderRulesList();
		handleConfigChange(); // 标记为已修改
		showToast('群规删除成功', 'success');
	}
}

// 渲染群规列表
function renderRulesList() {
	if (!ruleElements.rulesList) return;
	
	if (currentRules.length === 0) {
		ruleElements.rulesList.innerHTML = `
			<div class="empty-rules">
				<div class="empty-rules-icon">📝</div>
				<p>暂无群规配置</p>
				<p>点击"添加群规"开始配置</p>
			</div>
		`;
		return;
	}
	
	ruleElements.rulesList.innerHTML = currentRules.map((rule, index) => {
		// 访问状态显示
		let statusIcon = '';
		let statusText = '';
		let statusClass = '';
		
		if (rule.canAccess === true) {
			statusIcon = '✅';
			statusText = '可访问';
			statusClass = 'status-accessible';
		} else if (rule.canAccess === false) {
			statusIcon = '❌';
			statusText = `无法访问: ${rule.accessError || '未知错误'}`;
			statusClass = 'status-inaccessible';
		} else {
			statusIcon = '❓';
			statusText = '未验证';
			statusClass = 'status-unknown';
		}
		
		// 显示聊天标题（如果有）
		const chatTitle = rule.chatTitle ? ` (${rule.chatTitle})` : '';
		
		return `
			<div class="rule-item">
				<div class="rule-content">
					<div class="rule-title">${escapeHtml(rule.title)}</div>
					<a href="${rule.url}" target="_blank" class="rule-url">${rule.url}</a>
					<div class="rule-status ${statusClass}">
						<span class="status-icon">${statusIcon}</span>
						<span class="status-text">${statusText}${chatTitle}</span>
					</div>
				</div>
				<div class="rule-actions">
					<button class="edit-rule" onclick="showEditRuleModal(${index})">编辑</button>
					<button class="remove-rule" onclick="removeRule(${index})">删除</button>
				</div>
			</div>
		`;
	}).join('');
}

// 验证Telegram URL
function isValidTelegramUrl(url) {
	try {
		const urlObj = new URL(url);
		return urlObj.hostname === 't.me' && urlObj.pathname.length > 1;
	} catch (error) {
		return false;
	}
}

// 加载群规配置
function loadGroupRules(groupConfig) {
	currentRules = [];
	
	if (groupConfig && groupConfig.rules && Array.isArray(groupConfig.rules)) {
		currentRules = groupConfig.rules.map(rule => ({
			url: rule.url,
			title: rule.title || '群规'
		}));
	}
	
	renderRulesList();
}

// 获取群规配置用于保存
function getGroupRulesForSave() {
	return currentRules;
}

// 验证群规访问权限
async function verifyRuleAccess(rules) {
	try {
		const response = await fetch('/api/group-config/verify-rules', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({ rules })
		});
		
		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
		}
		
		return await response.json();
	} catch (error) {
		console.error('验证群规访问权限失败:', error);
		throw error;
	}
}

// 暴露函数到全局作用域（用于onclick事件）
window.showEditRuleModal = showEditRuleModal;
window.removeRule = removeRule;

// #endregion 📝 群规管理功能

// #region 📱 Telegram WebApp 集成
// 初始化 Telegram WebApp
if (window.Telegram && window.Telegram.WebApp) {
	const tg = window.Telegram.WebApp;
	
	// 展开 WebApp
	tg.expand();
	
	// 设置主按钮
	tg.MainButton.text = "保存配置";
	tg.MainButton.onClick(() => {
		if (!elements.saveConfigBtn.disabled) {
			saveConfiguration();
		}
	});
	
	// 根据保存按钮状态更新主按钮
	const observer = new MutationObserver(() => {
		if (elements.saveConfigBtn.disabled) {
			tg.MainButton.hide();
		} else {
			tg.MainButton.show();
		}
	});
	
	observer.observe(elements.saveConfigBtn, {
		attributes: true,
		attributeFilter: ['disabled']
	});
}

// #endregion �� Telegram WebApp 集成 