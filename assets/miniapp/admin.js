// Telegram环境检测 - 简化版本
function isTelegramEnvironment() {
	return !!window.Telegram?.WebApp;
}

// 平台检测函数 - 使用现代方法，避免弃用的navigator.platform
function detectPlatform() {
	const userAgent = navigator.userAgent.toLowerCase();

	// 优先使用现代的navigator.userAgentData (如果可用)
	if (navigator.userAgentData && navigator.userAgentData.platform) {
		const platform = navigator.userAgentData.platform.toLowerCase();
		if (platform.includes('ios') || platform.includes('iphone') || platform.includes('ipad')) {
			return 'ios';
		}
		if (platform.includes('android')) {
			return 'android';
		}
		if (platform.includes('macos') || platform.includes('mac')) {
			return 'macos';
		}
		if (platform.includes('windows') || platform.includes('win')) {
			return 'windows';
		}
		if (platform.includes('linux')) {
			return 'linux';
		}
	}

	// 后备方案：基于userAgent的精确检测
	// 检测iOS (包括iPad在iPadOS 13+上伪装成macOS的情况)
	if (/iphone|ipod/.test(userAgent) || /ipad/.test(userAgent) || (/macintosh/.test(userAgent) && 'ontouchend' in document)) {
		return 'ios';
	}

	// 检测Android
	if (/android/.test(userAgent)) {
		return 'android';
	}

	// 检测macOS (真正的macOS，不是iOS伪装的)
	if (/macintosh|mac os x/.test(userAgent) && !('ontouchend' in document)) {
		return 'macos';
	}

	// 检测Windows
	if (/windows|win32|win64|wow32|wow64/.test(userAgent)) {
		return 'windows';
	}

	// 检测Linux (排除Android)
	if (/linux/.test(userAgent) && !/android/.test(userAgent)) {
		return 'linux';
	}

	return 'other';
}

// 设置平台类名到user-header元素
function setPlatformClassToUserHeader() {
	const platform = detectPlatform();
	const userHeader = document.querySelector('body');

	if (userHeader) {
		// 移除所有平台类名
		userHeader.classList.remove(
			'platform-ios',
			'platform-android',
			'platform-macos',
			'platform-windows',
			'platform-linux',
			'platform-other'
		);

		// 添加当前平台类名
		userHeader.classList.add(`platform-${platform}`);

		console.log('🔍 检测到平台:', platform, '已设置到 body');
	}

	return platform;
}

// 检测Telegram环境
const isTgEnv = isTelegramEnvironment();

const tg = window.Telegram?.WebApp;

// 只在TG环境下初始化TG相关功能
if (isTgEnv && tg) {
	tg.ready();
	tg.expand();

	// 初始化Telegram Mini App
	initializeTelegramMiniApp();
} else {
	console.log('ℹ️ 非TG环境，跳过TG API初始化');

	// 非TG环境下设置默认状态
	const root = document.documentElement;
	const body = document.body;

	// 确保不显示badge
	body.classList.remove('show-tg-badge');

	// 设置默认CSS变量
	const platform = detectPlatform();
	const bodyBasePadding = platform === 'android' ? 88 : 68;

	root.style.setProperty('--body-base-padding', `${bodyBasePadding}px`);
	root.style.setProperty('--header-top-adjustment', '0px');
	root.style.setProperty('--tg-badge-top', '0px');
	root.style.setProperty('--content-safe-area-inset-top', '0px');
	root.style.setProperty('--content-safe-area-inset-bottom', '0px');
	root.style.setProperty('--content-safe-area-inset-left', '0px');
	root.style.setProperty('--content-safe-area-inset-right', '0px');
}

// 初始化Telegram Mini App
async function initializeTelegramMiniApp() {
	if (!isTgEnv || !tg) {
		console.log('ℹ️ 非TG环境，跳过初始化');
		return;
	}

	try {
		// 设置主题色
		if (tg.setHeaderColor) {
			tg.setHeaderColor(tg.themeParams?.header_bg_color || tg.themeParams?.bg_color || '#ffffff');
		}

		// 监听安全区域变化
		if (tg.onEvent) {
			tg.onEvent('safeAreaChanged', updateSafeAreaInsets);
			tg.onEvent('contentSafeAreaChanged', updateSafeAreaInsets);
		}

		// 初始化安全区域
		updateSafeAreaInsets();

		console.log('✅ Telegram Mini App初始化完成');
	} catch (error) {
		console.warn('Telegram Mini App初始化失败:', error);
	}
}

// 更新安全区域插入 - 使用Telegram官方提供的值
function updateSafeAreaInsets() {
	if (!isTgEnv || !tg) return;

	const root = document.documentElement;

	// 直接使用Telegram提供的安全区域值
	if (tg.contentSafeAreaInset) {
		root.style.setProperty('--content-safe-area-inset-top', `${tg.contentSafeAreaInset.top || 0}px`);
		root.style.setProperty('--content-safe-area-inset-bottom', `${tg.contentSafeAreaInset.bottom || 0}px`);
		root.style.setProperty('--content-safe-area-inset-left', `${tg.contentSafeAreaInset.left || 0}px`);
		root.style.setProperty('--content-safe-area-inset-right', `${tg.contentSafeAreaInset.right || 0}px`);

		console.log('✅ 更新了contentSafeAreaInset:', tg.contentSafeAreaInset);
	}

	// 设置视口高度
	if (tg.viewportHeight) {
		root.style.setProperty('--tg-viewport-height', `${tg.viewportHeight}px`);
	}
	if (tg.viewportStableHeight) {
		root.style.setProperty('--tg-viewport-stable-height', `${tg.viewportStableHeight}px`);
	}

	// 检测是否需要显示tg-status-badge
	const shouldShowBadge = isTgEnv && tg.contentSafeAreaInset?.top > 0;

	// 设置badge显示状态 - 通过body class控制
	const body = document.body;
	if (shouldShowBadge) {
		body.classList.add('show-tg-badge');
	} else {
		body.classList.remove('show-tg-badge');
	}

	// 根据badge显示状态和平台设置调整值
	const platform = detectPlatform();
	let headerTopAdjustment = 0;
	let bodyBasePadding = 68; // 默认基础值
	let badgeTopPosition = 'var(--content-safe-area-inset-top)';

	// 根据平台设置body基础padding
	if (shouldShowBadge) {
		switch (platform) {
			case 'ios':
				bodyBasePadding = 68; // iOS基础值
				break;
			case 'android':
				bodyBasePadding = 92; // Android基础值，需要更多空间
				break;
			default:
				bodyBasePadding = 68; // 桌面平台使用默认值
		}
	} else {
		bodyBasePadding = 68;
	}

	if (shouldShowBadge) {
		switch (platform) {
			case 'ios':
				headerTopAdjustment = 48; // 36px badge + 12px spacing
				badgeTopPosition = 'calc(var(--content-safe-area-inset-top) + var(--spacing-sm))';
				break;
			case 'android':
				headerTopAdjustment = 36; // 36px badge only
				badgeTopPosition = 'var(--content-safe-area-inset-top)';
				break;
			default:
				headerTopAdjustment = 12; // minimal spacing for desktop platforms
				badgeTopPosition = 'calc(var(--content-safe-area-inset-top) + var(--spacing-sm))';
		}
	} else {
		headerTopAdjustment = 0; // 不显示badge时无需额外调整
	}

	root.style.setProperty('--body-base-padding', `${bodyBasePadding}px`);
	root.style.setProperty('--header-top-adjustment', `${headerTopAdjustment}px`);
	root.style.setProperty('--tg-badge-top', badgeTopPosition);

	console.log('📱 TG环境检测:', {
		shouldShowBadge,
		platform,
		bodyBasePadding: `${bodyBasePadding}px`,
		headerTopAdjustment: `${headerTopAdjustment}px`,
		badgeTopPosition,
		contentSafeAreaTop: tg.contentSafeAreaInset?.top || 0,
	});
}

// Telegram环境初始化已在前面完成

// DOM加载完成后设置平台类名
document.addEventListener('DOMContentLoaded', () => {
	setPlatformClassToUserHeader();
});

// 如果DOM已经加载完成，立即执行
if (document.readyState === 'loading') {
	document.addEventListener('DOMContentLoaded', setPlatformClassToUserHeader);
} else {
	setPlatformClassToUserHeader();
}

// 监听窗口大小变化 - 简化版本
window.addEventListener('resize', () => {
	if (isTgEnv) updateSafeAreaInsets();
});

window.addEventListener('orientationchange', () => {
	if (isTgEnv) setTimeout(updateSafeAreaInsets, 100);
});

// 全局变量
let isMobileView = true; // 默认为卡片视图

// 视图切换功能
function toggleView() {
	const tableContainer = document.querySelector('#mediaTab .table-container');
	const table = document.querySelector('#mediaTable');
	const mobileContainer = document.getElementById('mobileCardsContainer');
	const toggleBtn = document.getElementById('viewToggleBtn');

	isMobileView = !isMobileView;

	if (isMobileView) {
		// 切换到卡片视图
		if (table) {
			table.style.display = 'none';
			table.style.setProperty('display', 'none', 'important');
		}
		if (mobileContainer) {
			mobileContainer.style.display = 'block';
			mobileContainer.style.setProperty('display', 'block', 'important');
		}
		toggleBtn.textContent = '📊 切换到表格视图';
	} else {
		// 切换到表格视图
		if (table) {
			table.style.display = 'table';
			table.style.setProperty('display', 'table', 'important');
		}
		if (mobileContainer) {
			mobileContainer.style.display = 'none';
			mobileContainer.style.setProperty('display', 'none', 'important');
		}
		toggleBtn.textContent = '📱 切换到卡片视图';
	}
}

function initUserInfo() {
	const user = tg.initDataUnsafe?.user;
	if (user) {
		const avatar = document.getElementById('userAvatar');
		const name = document.getElementById('userName');
		const username = document.getElementById('userUsername');

		if (user.photo_url) {
			avatar.innerHTML = `<img src="${user.photo_url}" alt="Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
		} else {
			avatar.textContent = (user.first_name || user.username || '?').charAt(0).toUpperCase();
		}

		const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || user.username || '未知用户';
		name.textContent = fullName;

		if (user.username) {
			username.textContent = `@${user.username}`;
		} else {
			username.textContent = `ID: ${user.id}`;
		}
	} else {
		document.getElementById('userName').textContent = '开发者';
		document.getElementById('userUsername').textContent = '@developer';
		document.getElementById('userAvatar').textContent = 'D';
	}
}

// 显示用户信息弹窗
function showUserInfoModal() {
	const user = tg.initDataUnsafe?.user;
	const initData = tg.initData;
	const initDataUnsafe = tg.initDataUnsafe;

	// 构建完整的用户信息对象
	const userInfo = {
		telegram_user: user || null,
		init_data: initData || null,
		init_data_unsafe: initDataUnsafe || null,
		web_app_info: {
			version: tg.version || null,
			platform: tg.platform || null,
			color_scheme: tg.colorScheme || null,
			theme_params: tg.themeParams || null,
			is_expanded: tg.isExpanded || false,
			viewport_height: tg.viewportHeight || null,
			viewport_stable_height: tg.viewportStableHeight || null,
			header_color: tg.headerColor || null,
			background_color: tg.backgroundColor || null,
			is_closing_confirmation_enabled: tg.isClosingConfirmationEnabled || false,
			safe_area_inset_top: tg.safeAreaInset?.top || null,
			safe_area_inset_bottom: tg.safeAreaInset?.bottom || null,
			safe_area_inset_left: tg.safeAreaInset?.left || null,
			safe_area_inset_right: tg.safeAreaInset?.right || null,
		},
		timestamp: Date.now(),
		url: window.location.href,
	};

	// 创建模态窗口
	const modal = document.createElement('div');
	modal.className = 'user-info-modal';
	modal.style.cssText = `
        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
        background: var(--bg-modal-overlay); z-index: 10000;
        display: flex; align-items: center; justify-content: center;
        padding: 20px; box-sizing: border-box;
    `;

	// 创建内容容器
	const content = document.createElement('div');
	content.className = 'user-info-modal-content';
	content.style.cssText = `
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px; max-width: 95vw; max-height: 90vh;
        overflow: hidden; position: relative;
        color: #2d3748;
        display: flex; flex-direction: column;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
    `;

	// 格式化JSON
	const formattedJson = JSON.stringify(userInfo, null, 2);

	// 构建用户显示信息
	const userDisplay = user ? [user.first_name, user.last_name].filter(Boolean).join(' ') || user.username || `ID:${user.id}` : '开发者模式';

	content.innerHTML = `
        <div style="padding: 24px 24px 16px 24px; border-bottom: 1px solid rgba(226, 232, 240, 0.8); flex-shrink: 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 18px; font-weight: 700; color: var(--text-primary);">👤 用户信息</h3>
                <button onclick="this.closest('.user-info-modal').remove()" 
                        style="background: none; border: none; font-size: 24px; cursor: pointer; color: var(--text-light); border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">×</button>
            </div>
            
            <div style="display: grid; gap: 8px; font-size: 14px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); padding: 16px; border-radius: 12px; border: 1px solid #e2e8f0;">
                <div><strong>用户:</strong> ${userDisplay}</div>
                ${user?.username ? `<div><strong>用户名:</strong> @${user.username}</div>` : ''}
                ${user?.id ? `<div><strong>用户ID:</strong> ${user.id}</div>` : ''}
                ${user?.language_code ? `<div><strong>语言:</strong> ${user.language_code}</div>` : ''}
                ${user?.is_premium ? `<div><strong>Premium:</strong> ✅</div>` : ''}
                <div><strong>平台:</strong> ${tg.platform || '未知'}</div>
                <div><strong>版本:</strong> ${tg.version || '未知'}</div>
                <div><strong>主题:</strong> ${tg.colorScheme || '未知'}</div>
            </div>
        </div>
        
        <div style="flex: 1; overflow: auto; padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h4 style="margin: 0; font-size: 16px; color: var(--text-muted); font-weight: 600;">完整用户信息JSON:</h4>
                <button onclick="copyUserInfoToClipboard()" 
                        style="
                            background: linear-gradient(135deg, #ff8fa3 0%, #d63384 100%);
                            color: white; border: none; padding: 8px 12px;
                            border-radius: 8px; font-size: 12px; cursor: pointer; font-weight: 600;
                            transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(255, 182, 193, 0.3);
                        ">📋 复制</button>
            </div>
            
            <pre id="user-info-json-content" style="
                background: var(--text-primary); color: var(--text-white); padding: 16px; 
                border-radius: 8px; overflow: auto; margin: 0;
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 12px; line-height: 1.4;
                white-space: pre-wrap; word-wrap: break-word;
            ">${syntaxHighlightJson(formattedJson)}</pre>
        </div>
    `;

	modal.appendChild(content);
	document.body.appendChild(modal);

	// 点击背景关闭模态窗口
	modal.addEventListener('click', (e) => {
		if (e.target === modal) {
			modal.remove();
		}
	});

	// 存储用户信息JSON数据供复制使用
	window.userInfoJson = formattedJson;
}

// 复制用户信息JSON到剪贴板
function copyUserInfoToClipboard() {
	const jsonText = window.userInfoJson;
	if (!jsonText) {
		tg.showAlert('❌ 无法获取用户信息数据');
		return;
	}

	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(jsonText)
			.then(() => {
				tg.showAlert('✅ 用户信息JSON已复制到剪贴板');
			})
			.catch(() => {
				// 降级到传统方法
				fallbackCopyText(jsonText);
			});
	} else {
		// 降级到传统方法
		fallbackCopyText(jsonText);
	}
}

async function loadMediaGroupData(filters = {}) {
	const loadingIndicator = document.getElementById('loadingIndicator');
	const errorMessage = document.getElementById('errorMessage');
	const mediaTable = document.getElementById('mediaTable');
	const emptyState = document.getElementById('emptyState');

	try {
		loadingIndicator.style.display = 'flex';
		errorMessage.style.display = 'none';
		emptyState.style.display = 'none';

		const queryParams = new URLSearchParams();
		if (filters.search) queryParams.append('search', filters.search);
		if (filters.mediaType) queryParams.append('media_type', filters.mediaType);
		if (filters.dateFrom) queryParams.append('date_from', filters.dateFrom);
		if (filters.dateTo) queryParams.append('date_to', filters.dateTo);

		const response = await AuthManager.secureApiRequest(`/api/media-groups?${queryParams.toString()}`);

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const data = await response.json();
		loadingIndicator.style.display = 'none';

		if (data.records && data.records.length > 0) {
			// 存储当前记录到全局变量
			window.currentRecords = data.records;
			renderTable(data.records);
			updateStats(data.stats || {});
			// 不在这里强制显示表格，让 initializeViewState 控制显示状态
		} else {
			window.currentRecords = [];
			emptyState.style.display = 'block';
		}
	} catch (error) {
		console.error('加载数据失败:', error);
		loadingIndicator.style.display = 'none';
		errorMessage.textContent = `加载数据失败: ${error.message}`;
		errorMessage.style.display = 'block';
		window.currentRecords = [];
	}
}

function renderTable(records) {
	const tbody = document.getElementById('tableBody');
	tbody.innerHTML = '';

	// 为移动端创建卡片容器
	let mobileContainer = document.getElementById('mobileCardsContainer');
	if (!mobileContainer) {
		mobileContainer = document.createElement('div');
		mobileContainer.id = 'mobileCardsContainer';
		mobileContainer.className = 'mobile-cards';
		// 插入到table-container内，但在table后面
		const tableContainer = document.querySelector('#mediaTab .table-container');
		if (tableContainer) {
			tableContainer.appendChild(mobileContainer);
		}
	}
	mobileContainer.innerHTML = '';

	records.forEach((record) => {
		const date = new Date(record.date * 1000);
		const formattedDate = date.toLocaleString('zh-CN');
		const mediaTypeBadge = `<span class="media-type-badge media-type-${record.media_type || 'unknown'}">${getMediaTypeLabel(
			record.media_type
		)}</span>`;
		const userDisplay = [record.first_name, record.last_name].filter(Boolean).join(' ') || record.username || `ID:${record.user_id}`;

		// 生成媒体预览内容
		let mediaPreview = '';
		let cardMediaPreview = '';
		try {
			if (record.raw_json) {
				const messageData = JSON.parse(record.raw_json);
				mediaPreview = generateMediaPreview(messageData, record.media_type, false); // 表格用
				cardMediaPreview = generateMediaPreview(messageData, record.media_type, true); // 卡片用
			}
		} catch (error) {
			console.error('解析媒体数据失败:', error);
		}

		// 如果没有媒体预览，显示caption作为后备
		if (!mediaPreview) {
			const caption = record.caption ? (record.caption.length > 30 ? record.caption.substring(0, 30) + '...' : record.caption) : '-';
			mediaPreview = `<div style="color: var(--text-light); font-style: italic;">${caption}</div>`;
		}
		if (!cardMediaPreview) {
			const caption = record.caption ? (record.caption.length > 50 ? record.caption.substring(0, 50) + '...' : record.caption) : '-';
			cardMediaPreview = `<div style="color: var(--text-light); font-style: italic; padding: 20px; text-align: center; background: var(--bg-light-gray); border-radius: 8px; aspect-ratio: 1; display: flex; align-items: center; justify-content: center;">${caption}</div>`;
		}

		// 桌面端表格行
		const row = document.createElement('tr');
		row.innerHTML = `
            <td>${record.id}</td>
            <td>
                <div>${userDisplay}</div>
                ${record.username ? `<small style="color: var(--text-light);">@${record.username}</small>` : ''}
            </td>
            <td>
                <div>${record.group_title || '私聊'}</div>
                <small style="color: var(--text-light);">ID: ${record.group_id}</small>
            </td>
            <td>${mediaTypeBadge}</td>
            <td>${mediaPreview}</td>
            <td>${formattedDate}</td>
            <td>
                <button onclick="viewDetails(${record.id})" style="
                    background: var(--primary-color);
                    color: white; border: none; padding: 4px 8px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                ">详情</button>
            </td>
        `;
		tbody.appendChild(row);

		// 移动端卡片
		const card = document.createElement('div');
		card.className = 'mobile-card';
		card.innerHTML = `
            <div class="mobile-card-header">
                <span class="mobile-card-id">#${record.id}</span>
                <span class="mobile-card-date">${formattedDate}</span>
            </div>
            
            <div class="mobile-card-content">
                <div class="mobile-card-user-group">
                    <div class="group-info">
                        <div class="group-text">
                            <div class="group-name"><div class="group-badge">from</div>${record.group_title || '私聊'}</div>
                            <small class="group-id">ID: ${record.group_id}</small>
                        </div>
                    </div>
                    
                    <div class="user-group-divider"></div>
                    
                    <div class="user-info">
                        <div class="user-text">
                            <div class="user-name"><div class="user-badge">via</div>${userDisplay}</div>
                            ${record.username ? `<small class="user-username">@${record.username}</small>` : ''}
                        </div>
                    </div>
                </div>
                
                ${
									cardMediaPreview
										? `
                <div class="mobile-card-media">
                    <div class="media-type-overlay">${mediaTypeBadge}</div>
                    ${cardMediaPreview}
                </div>
                `
										: `
                <div class="mobile-card-row">
                    <span class="mobile-card-label">类型</span>
                    <div class="mobile-card-value">${mediaTypeBadge}</div>
                </div>
                `
								}
            </div>
            
            <div class="mobile-card-actions">
                <button onclick="viewDetails(${record.id})" class="btn btn-primary btn-sm">
                    📋 查看详情
                </button>
            </div>
        `;
		mobileContainer.appendChild(card);
	});

	// 初始化显示状态
	initializeViewState();
}

// 初始化显示状态
function initializeViewState() {
	const table = document.querySelector('#mediaTable');
	const mobileContainer = document.getElementById('mobileCardsContainer');
	const toggleBtn = document.getElementById('viewToggleBtn');

	if (isMobileView) {
		// 默认显示卡片视图
		if (table) {
			table.style.display = 'none';
			table.style.setProperty('display', 'none', 'important');
		}
		if (mobileContainer) {
			mobileContainer.style.display = 'grid';
			mobileContainer.style.setProperty('display', 'grid', 'important');
		}
		if (toggleBtn) toggleBtn.textContent = '📊 切换到表格视图';
	} else {
		// 显示表格视图
		if (table) {
			table.style.display = 'table';
			table.style.setProperty('display', 'table', 'important');
		}
		if (mobileContainer) {
			mobileContainer.style.display = 'none';
			mobileContainer.style.setProperty('display', 'none', 'important');
		}
		if (toggleBtn) toggleBtn.textContent = '📱 切换到卡片视图';
	}
}

function generateMediaPreview(messageData, mediaType, isCard = false) {
	// 生成唯一的数据标识符
	const mediaDataId = `media-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

	// 将媒体数据存储到全局对象中以供弹窗使用
	if (!window.mediaDataStore) {
		window.mediaDataStore = {};
	}
	window.mediaDataStore[mediaDataId] = { messageData, mediaType };

	// 添加点击事件属性
	const clickHandler = `onclick="showMediaModal('${mediaDataId}')"`;

	// 根据上下文生成不同的样式
	const previewStyle = isCard
		? `
        width: 100%; aspect-ratio: 1; border-radius: 8px;
        object-fit: cover; cursor: pointer;
        display: block;
    `
		: `
        width: 60px; height: 60px; border-radius: 6px; 
        object-fit: cover; box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        cursor: pointer;
    `;

	const iconStyle = isCard
		? `
        width: 100%; aspect-ratio: 1; border-radius: 8px;
        background: var(--bg-light-gray);
        display: flex; align-items: center; justify-content: center;
        font-size: 48px; cursor: pointer;
        border: 1px solid var(--border-color);
    `
		: `
        width: 60px; height: 60px; border-radius: 6px;
        background: var(--bg-light-gray);
        display: flex; align-items: center; justify-content: center;
        font-size: 24px; cursor: pointer;
        border: 1px solid var(--text-light);
    `;

	switch (mediaType) {
		case 'photo':
			if (messageData.photo && messageData.photo.length > 0) {
				const photo = messageData.photo[messageData.photo.length - 1];
				return `<img src="/api/file/${photo.file_id}" alt="图片预览" style="${previewStyle}" loading="lazy" title="点击查看大图" ${clickHandler}>`;
			}
			break;

		case 'video':
			if (messageData.video) {
				if (messageData.video.thumb) {
					const playButtonSize = isCard ? '32px' : '16px';
					return `
                        <div style="position: relative; display: ${isCard ? 'block' : 'inline-block'}; width: ${
						isCard ? '100%' : 'auto'
					}; cursor: pointer;" ${clickHandler}>
                            <img src="/api/file/${
															messageData.video.thumb.file_id
														}" alt="视频缩略图" style="${previewStyle}" loading="lazy" title="点击播放视频">
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: ${playButtonSize}; text-shadow: 1px 1px 2px rgba(0,0,0,0.7); background: rgba(0,0,0,0.5); border-radius: 50%; width: ${
						isCard ? '48px' : '24px'
					}; height: ${isCard ? '48px' : '24px'}; display: flex; align-items: center; justify-content: center;">▶️</div>
                        </div>
                    `;
				} else {
					return `<div style="${iconStyle}" title="视频文件" ${clickHandler}>🎬</div>`;
				}
			}
			break;

		case 'animation':
			if (messageData.animation) {
				if (messageData.animation.thumb) {
					const gifLabelSize = isCard ? '12px' : '10px';
					const gifLabelPadding = isCard ? '4px 8px' : '2px 4px';
					return `
                        <div style="position: relative; display: ${isCard ? 'block' : 'inline-block'}; width: ${
						isCard ? '100%' : 'auto'
					}; cursor: pointer;" ${clickHandler}>
                            <img src="/api/file/${
															messageData.animation.thumb.file_id
														}" alt="动画缩略图" style="${previewStyle}" loading="lazy" title="点击查看动画">
                            <div style="position: absolute; top: ${isCard ? '8px' : '4px'}; right: ${
						isCard ? '8px' : '4px'
					}; background: rgba(0,0,0,0.7); color: white; padding: ${gifLabelPadding}; border-radius: 4px; font-size: ${gifLabelSize}; font-weight: 600;">GIF</div>
                        </div>
                    `;
				} else {
					return `<div style="${iconStyle}" title="动画文件" ${clickHandler}>🎞️</div>`;
				}
			}
			break;

		case 'audio':
			if (messageData.audio) {
				const title = messageData.audio.title || '未知音频';
				const performer = messageData.audio.performer || '未知艺术家';
				const titleLength = isCard ? 20 : 8;
				const fontSize = isCard ? '24px' : '14px';
				const iconSize = isCard ? '32px' : '18px';
				const textSize = isCard ? '14px' : '10px';
				return `
                    <div style="${iconStyle.replace(
											isCard ? 'font-size: 48px' : 'font-size: 24px',
											`font-size: ${fontSize}; flex-direction: column; text-align: center; padding: ${isCard ? '12px' : '4px'}`
										)}" title="${title} - ${performer}" ${clickHandler}>
                        <div style="font-size: ${iconSize};">🎵</div>
                        <div style="font-size: ${textSize}; line-height: 1.2; color: var(--text-light); overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 100%; margin-top: ${
					isCard ? '8px' : '4px'
				};">${title.length > titleLength ? title.substring(0, titleLength) + '...' : title}</div>
                    </div>
                `;
			}
			break;

		case 'voice':
			if (messageData.voice) {
				const duration = messageData.voice.duration ? formatDuration(messageData.voice.duration) : '';
				const fontSize = isCard ? '24px' : '14px';
				const iconSize = isCard ? '32px' : '18px';
				const textSize = isCard ? '14px' : '10px';
				return `
                    <div style="${iconStyle.replace(
											isCard ? 'font-size: 48px' : 'font-size: 24px',
											`font-size: ${fontSize}; flex-direction: column; align-items: center; justify-content: center; padding: ${
												isCard ? '12px' : '4px'
											}`
										)}" title="语音消息 ${duration}" ${clickHandler}>
                        <div style="font-size: ${iconSize};">🎤</div>
                        <div style="font-size: ${textSize}; color: var(--text-light); margin-top: ${
					isCard ? '8px' : '4px'
				};">${duration}</div>
                    </div>
                `;
			}
			break;

		case 'document':
			if (messageData.document) {
				const fileName = messageData.document.file_name || '文档';
				const fileSize = messageData.document.file_size ? formatFileSize(messageData.document.file_size) : '';

				// 如果是图片文档且有缩略图，显示缩略图
				if (messageData.document.thumb && (messageData.document.mime_type || '').startsWith('image/')) {
					const docLabelSize = isCard ? '10px' : '8px';
					const docLabelPadding = isCard ? '2px 6px' : '1px 3px';
					return `
                        <div style="position: relative; display: ${isCard ? 'block' : 'inline-block'}; width: ${
						isCard ? '100%' : 'auto'
					}; cursor: pointer;" ${clickHandler}>
                            <img src="/api/file/${
															messageData.document.thumb.file_id
														}" alt="文档缩略图" style="${previewStyle}" loading="lazy" title="${fileName}">
                            <div style="position: absolute; bottom: ${isCard ? '8px' : '2px'}; right: ${
						isCard ? '8px' : '2px'
					}; background: rgba(0,0,0,0.7); color: white; padding: ${docLabelPadding}; border-radius: 3px; font-size: ${docLabelSize}; font-weight: 600;">DOC</div>
                        </div>
                    `;
				} else {
					const fileNameLength = isCard ? 20 : 10;
					const fontSize = isCard ? '20px' : '12px';
					const iconSize = isCard ? '28px' : '16px';
					const textSize = isCard ? '12px' : '9px';
					const fileSizeTextSize = isCard ? '11px' : '8px';
					return `
                        <div style="${iconStyle.replace(
													isCard ? 'font-size: 48px' : 'font-size: 24px',
													`font-size: ${fontSize}; flex-direction: column; text-align: center; padding: ${isCard ? '12px' : '4px'}`
												)}" title="${fileName} ${fileSize}" ${clickHandler}>
                            <div style="font-size: ${iconSize};">📄</div>
                            <div style="font-size: ${textSize}; line-height: 1.1; color: var(--text-light); overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 100%; margin-top: ${
						isCard ? '8px' : '4px'
					};">${fileName.length > fileNameLength ? fileName.substring(0, fileNameLength) + '...' : fileName}</div>
                            ${
															fileSize
																? `<div style="font-size: ${fileSizeTextSize}; color: var(--text-light); margin-top: 2px;">${fileSize}</div>`
																: ''
														}
                        </div>
                    `;
				}
			}
			break;

		case 'video_note':
			if (messageData.video_note) {
				if (messageData.video_note.thumb) {
					const playButtonSize = isCard ? '32px' : '16px';
					const borderRadius = isCard ? '50%' : '50%';
					return `
                        <div style="position: relative; display: ${isCard ? 'block' : 'inline-block'}; width: ${
						isCard ? '100%' : 'auto'
					}; cursor: pointer;" ${clickHandler}>
                            <img src="/api/file/${messageData.video_note.thumb.file_id}" alt="视频笔记缩略图" style="${previewStyle.replace(
						'border-radius: 8px',
						`border-radius: ${borderRadius}`
					)}" loading="lazy" title="视频笔记">
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: ${playButtonSize}; text-shadow: 1px 1px 2px rgba(0,0,0,0.7); background: rgba(0,0,0,0.5); border-radius: 50%; width: ${
						isCard ? '48px' : '24px'
					}; height: ${isCard ? '48px' : '24px'}; display: flex; align-items: center; justify-content: center;">▶️</div>
                        </div>
                    `;
				} else {
					return `<div style="${iconStyle.replace('border-radius: 8px', 'border-radius: 50%')}" title="视频笔记" ${clickHandler}>📹</div>`;
				}
			}
			break;
	}

	// 默认返回媒体类型图标
	const typeIcons = {
		photo: '📷',
		video: '🎬',
		audio: '🎵',
		voice: '🎤',
		document: '📄',
		animation: '🎞️',
		video_note: '📹',
	};

	const icon = typeIcons[mediaType] || '📁';
	return `<div style="${iconStyle}" title="${getMediaTypeLabel(mediaType)}" ${clickHandler}>${icon}</div>`;
}

function getMediaTypeLabel(mediaType) {
	const labels = {
		photo: '图片',
		video: '视频',
		document: '文档',
		audio: '音频',
		voice: '语音',
		animation: '动画',
		video_note: '视频消息',
	};
	return labels[mediaType] || '未知';
}

function showMediaModal(mediaDataId) {
	const mediaData = window.mediaDataStore[mediaDataId];
	if (!mediaData) {
		console.error('媒体数据未找到:', mediaDataId);
		return;
	}

	const { messageData, mediaType } = mediaData;
	const mediaTypeLabel = getMediaTypeLabel(mediaType);

	// 创建弹窗HTML
	const modalHtml = `
        <div class="modal" id="mediaModal" style="display: flex;">
            <div class="modal-content media-modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">📱 ${mediaTypeLabel}详情</h3>
                    <button class="modal-close" onclick="closeMediaModal()">&times;</button>
                </div>
                <div class="modal-body media-modal-body">
                    ${generateMediaDetailView(messageData, mediaType)}
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-button" onclick="closeMediaModal()">关闭</button>
                    ${generateMediaActionButtons(messageData, mediaType)}
                </div>
            </div>
        </div>
    `;

	// 添加到页面
	document.body.insertAdjacentHTML('beforeend', modalHtml);

	// 为关闭按钮和背景添加事件监听
	const modal = document.getElementById('mediaModal');
	modal.addEventListener('click', (e) => {
		if (e.target === modal) {
			closeMediaModal();
		}
	});
}

function closeMediaModal() {
	const modal = document.getElementById('mediaModal');
	if (modal) {
		modal.remove();
	}
}

function generateMediaDetailView(messageData, mediaType) {
	switch (mediaType) {
		case 'photo':
			if (messageData.photo && messageData.photo.length > 0) {
				const photo = messageData.photo[messageData.photo.length - 1];
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            <img src="/api/file/${
															photo.file_id
														}" alt="图片预览" style="max-width: 100%; max-height: 70vh; object-fit: contain; border-radius: 8px;">
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> 图片</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${photo.file_id}</code></div>
                            <div class="info-item"><strong>尺寸:</strong> ${photo.width} × ${photo.height}</div>
                            ${
															photo.file_size
																? `<div class="info-item"><strong>大小:</strong> ${formatFileSize(photo.file_size)}</div>`
																: ''
														}
                        </div>
                    </div>
                `;
			}
			break;

		case 'video':
			if (messageData.video) {
				const video = messageData.video;
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            ${
															video.thumb
																? `<img src="/api/file/${video.thumb.file_id}" alt="视频缩略图" style="max-width: 100%; max-height: 50vh; object-fit: contain; border-radius: 8px;">`
																: `<div style="width: 300px; height: 200px; background: var(--bg-light-gray); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 48px;">🎬</div>`
														}
                            <div style="text-align: center; margin-top: 16px;">
                                <a href="/api/file/${video.file_id}" download class="primary-button">📥 下载视频</a>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> 视频</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${video.file_id}</code></div>
                            <div class="info-item"><strong>时长:</strong> ${formatDuration(video.duration)}</div>
                            <div class="info-item"><strong>尺寸:</strong> ${video.width} × ${video.height}</div>
                            <div class="info-item"><strong>大小:</strong> ${formatFileSize(video.file_size)}</div>
                            ${video.mime_type ? `<div class="info-item"><strong>格式:</strong> ${video.mime_type}</div>` : ''}
                        </div>
                    </div>
                `;
			}
			break;

		case 'animation':
			if (messageData.animation) {
				const animation = messageData.animation;
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            <img src="/api/file/${
															animation.file_id
														}" alt="动画预览" style="max-width: 100%; max-height: 60vh; object-fit: contain; border-radius: 8px;">
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> GIF动画</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${animation.file_id}</code></div>
                            <div class="info-item"><strong>时长:</strong> ${formatDuration(animation.duration)}</div>
                            <div class="info-item"><strong>尺寸:</strong> ${animation.width} × ${animation.height}</div>
                            <div class="info-item"><strong>大小:</strong> ${formatFileSize(animation.file_size)}</div>
                            ${animation.file_name ? `<div class="info-item"><strong>文件名:</strong> ${animation.file_name}</div>` : ''}
                        </div>
                    </div>
                `;
			}
			break;

		case 'audio':
			if (messageData.audio) {
				const audio = messageData.audio;
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            <div style="text-align: center; padding: 40px;">
                                <div style="font-size: 80px; margin-bottom: 20px;">🎵</div>
                                <audio controls style="width: 100%; max-width: 400px;">
                                    <source src="/api/file/${audio.file_id}" type="${audio.mime_type || 'audio/mpeg'}">
                                    您的浏览器不支持音频播放
                                </audio>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> 音频</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${audio.file_id}</code></div>
                            <div class="info-item"><strong>标题:</strong> ${audio.title || '未知'}</div>
                            <div class="info-item"><strong>艺术家:</strong> ${audio.performer || '未知'}</div>
                            <div class="info-item"><strong>时长:</strong> ${formatDuration(audio.duration)}</div>
                            <div class="info-item"><strong>大小:</strong> ${formatFileSize(audio.file_size)}</div>
                            ${audio.mime_type ? `<div class="info-item"><strong>格式:</strong> ${audio.mime_type}</div>` : ''}
                        </div>
                    </div>
                `;
			}
			break;

		case 'voice':
			if (messageData.voice) {
				const voice = messageData.voice;
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            <div style="text-align: center; padding: 40px;">
                                <div style="font-size: 80px; margin-bottom: 20px;">🎤</div>
                                <audio controls style="width: 100%; max-width: 400px;">
                                    <source src="/api/file/${voice.file_id}" type="${voice.mime_type || 'audio/ogg'}">
                                    您的浏览器不支持音频播放
                                </audio>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> 语音消息</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${voice.file_id}</code></div>
                            <div class="info-item"><strong>时长:</strong> ${formatDuration(voice.duration)}</div>
                            <div class="info-item"><strong>大小:</strong> ${formatFileSize(voice.file_size)}</div>
                            ${voice.mime_type ? `<div class="info-item"><strong>格式:</strong> ${voice.mime_type}</div>` : ''}
                        </div>
                    </div>
                `;
			}
			break;

		case 'document':
			if (messageData.document) {
				const document = messageData.document;
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            ${
															document.thumb
																? `<img src="/api/file/${document.thumb.file_id}" alt="文档缩略图" style="max-width: 100%; max-height: 50vh; object-fit: contain; border-radius: 8px;">`
																: `<div style="width: 300px; height: 200px; background: var(--bg-light-gray); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 80px;">📄</div>`
														}
                            <div style="text-align: center; margin-top: 16px;">
                                <a href="/api/file/${document.file_id}" download="${
					document.file_name || 'document'
				}" class="primary-button">📥 下载文档</a>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> 文档</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${document.file_id}</code></div>
                            <div class="info-item"><strong>文件名:</strong> ${document.file_name || '未知'}</div>
                            <div class="info-item"><strong>大小:</strong> ${formatFileSize(document.file_size)}</div>
                            ${document.mime_type ? `<div class="info-item"><strong>格式:</strong> ${document.mime_type}</div>` : ''}
                        </div>
                    </div>
                `;
			}
			break;

		case 'video_note':
			if (messageData.video_note) {
				const videoNote = messageData.video_note;
				return `
                    <div class="media-detail-container">
                        <div class="media-preview-large">
                            ${
															videoNote.thumb
																? `<img src="/api/file/${videoNote.thumb.file_id}" alt="视频笔记缩略图" style="max-width: 100%; max-height: 50vh; object-fit: contain; border-radius: 50%;">`
																: `<div style="width: 300px; height: 300px; background: var(--bg-light-gray); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 80px;">📹</div>`
														}
                            <div style="text-align: center; margin-top: 16px;">
                                <a href="/api/file/${videoNote.file_id}" download class="primary-button">📥 下载视频消息</a>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="info-item"><strong>类型:</strong> 视频消息</div>
                            <div class="info-item"><strong>文件ID:</strong> <code>${videoNote.file_id}</code></div>
                            <div class="info-item"><strong>时长:</strong> ${formatDuration(videoNote.duration)}</div>
                            <div class="info-item"><strong>长度:</strong> ${videoNote.length}px</div>
                            <div class="info-item"><strong>大小:</strong> ${formatFileSize(videoNote.file_size)}</div>
                        </div>
                    </div>
                `;
			}
			break;
	}

	return `
        <div class="media-detail-container">
            <div style="text-align: center; padding: 40px; color: var(--text-light);">
                <div style="font-size: 80px; margin-bottom: 20px;">❓</div>
                <p>无法预览此媒体类型</p>
            </div>
        </div>
    `;
}

function generateMediaActionButtons(messageData, mediaType) {
	let fileId = '';
	let fileName = '';

	switch (mediaType) {
		case 'photo':
			if (messageData.photo && messageData.photo.length > 0) {
				fileId = messageData.photo[messageData.photo.length - 1].file_id;
				fileName = `photo_${fileId}.jpg`;
			}
			break;
		case 'video':
			if (messageData.video) {
				fileId = messageData.video.file_id;
				fileName = `video_${fileId}.mp4`;
			}
			break;
		case 'animation':
			if (messageData.animation) {
				fileId = messageData.animation.file_id;
				fileName = messageData.animation.file_name || `animation_${fileId}.gif`;
			}
			break;
		case 'audio':
			if (messageData.audio) {
				fileId = messageData.audio.file_id;
				fileName = messageData.audio.title || `audio_${fileId}.mp3`;
			}
			break;
		case 'voice':
			if (messageData.voice) {
				fileId = messageData.voice.file_id;
				fileName = `voice_${fileId}.ogg`;
			}
			break;
		case 'document':
			if (messageData.document) {
				fileId = messageData.document.file_id;
				fileName = messageData.document.file_name || `document_${fileId}`;
			}
			break;
		case 'video_note':
			if (messageData.video_note) {
				fileId = messageData.video_note.file_id;
				fileName = `video_note_${fileId}.mp4`;
			}
			break;
	}

	if (fileId) {
		return `
            <a href="/api/file/${fileId}" download="${fileName}" class="primary-button" style="text-align: center;text-decoration: none;">
                📥 下载文件
            </a>
        `;
	}

	return '';
}

function updateStats(stats) {
	document.getElementById('totalCount').textContent = stats.total || 0;
	document.getElementById('groupCount').textContent = stats.groups || 0;
	document.getElementById('todayCount').textContent = stats.today || 0;
}

function viewDetails(recordId) {
	// 从当前加载的记录中找到对应的数据
	const tableBody = document.getElementById('tableBody');
	const rows = tableBody.getElementsByTagName('tr');

	let targetRecord = null;
	for (let row of rows) {
		const firstCell = row.cells[0];
		if (firstCell && firstCell.textContent == recordId) {
			// 从全局记录中找到对应的数据
			targetRecord = window.currentRecords?.find((r) => r.id == recordId);
			break;
		}
	}

	if (!targetRecord) {
		tg.showAlert('未找到记录数据');
		return;
	}

	showJsonModal(targetRecord);
}

function showJsonModal(record) {
	// 创建模态窗口
	const modal = document.createElement('div');
	modal.className = 'json-modal';
	modal.style.cssText = `
        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
        background: var(--bg-modal-overlay); z-index: 10000;
        display: flex; align-items: center; justify-content: center;
        padding: 20px; box-sizing: border-box;
    `;

	// 创建内容容器
	const content = document.createElement('div');
	content.className = 'json-modal-content';
	content.style.cssText = `
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px; max-width: 95vw; max-height: 90vh;
        overflow: hidden; position: relative;
        color: #2d3748;
        display: flex; flex-direction: column;
    `;

	// 构建用户信息
	const userDisplay = [record.first_name, record.last_name].filter(Boolean).join(' ') || record.username || `ID:${record.user_id}`;
	const date = new Date(record.date * 1000);
	const formattedDate = date.toLocaleString('zh-CN');

	// 解析并格式化JSON
	let formattedJson = '';
	let jsonObject = null;

	try {
		if (record.raw_json) {
			jsonObject = JSON.parse(record.raw_json);
			formattedJson = JSON.stringify(jsonObject, null, 2);
		} else {
			formattedJson = '{ "error": "无原始JSON数据" }';
		}
	} catch (error) {
		formattedJson = `{ "error": "JSON解析失败: ${error.message}" }`;
	}

	content.innerHTML = `
                    <div style="padding: 20px; border-bottom: 1px solid var(--text-light); flex-shrink: 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 18px;">📋 原始消息数据</h3>
                <button onclick="this.closest('.json-modal').remove()" 
                        style="background: none; border: none; font-size: 24px; cursor: pointer; color: var(--text-light);">×</button>
            </div>
            
            <div style="display: grid; gap: 8px; font-size: 14px; background: var(--bg-light-gray); padding: 12px; border-radius: 8px;">
                <div><strong>记录ID:</strong> ${record.id}</div>
                <div><strong>用户:</strong> ${userDisplay}</div>
                ${record.username ? `<div><strong>用户名:</strong> @${record.username}</div>` : ''}
                <div><strong>群组:</strong> ${record.group_title || '私聊'}</div>
                <div><strong>媒体类型:</strong> ${getMediaTypeLabel(record.media_type)}</div>
                <div><strong>时间:</strong> ${formattedDate}</div>
                ${record.media_group_id ? `<div><strong>媒体组ID:</strong> ${record.media_group_id}</div>` : ''}
            </div>
        </div>
        
        <div style="flex: 1; overflow: auto; padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h4 style="margin: 0; font-size: 16px; color: var(--text-muted);">原始Telegram消息JSON:</h4>
                <button onclick="copyJsonToClipboard('${record.id}')" 
                        style="
                            background: var(--primary-color);
                            color: white; border: none; padding: 6px 12px;
                            border-radius: 6px; font-size: 12px; cursor: pointer;
                        ">📋 复制</button>
            </div>
            
            <pre id="json-content-${record.id}" style="
                background: var(--text-primary); color: var(--text-white); padding: 16px; 
                border-radius: 8px; overflow: auto; margin: 0;
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 12px; line-height: 1.4;
                white-space: pre-wrap; word-wrap: break-word;
            ">${syntaxHighlightJson(formattedJson)}</pre>
        </div>
    `;

	modal.appendChild(content);
	document.body.appendChild(modal);

	// 点击背景关闭模态窗口
	modal.addEventListener('click', (e) => {
		if (e.target === modal) {
			modal.remove();
		}
	});

	// 存储JSON数据供复制使用
	window.jsonData = window.jsonData || {};
	window.jsonData[record.id] = formattedJson;
}

function syntaxHighlightJson(json) {
	return json
		.replace(/&/g, '&amp;')
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;')
		.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
			let cls = 'json-number';
			if (/^"/.test(match)) {
				if (/:$/.test(match)) {
					cls = 'json-key';
				} else {
					cls = 'json-string';
				}
			} else if (/true|false/.test(match)) {
				cls = 'json-boolean';
			} else if (/null/.test(match)) {
				cls = 'json-null';
			}

			const colors = {
				'json-key': '#79b8ff',
				'json-string': '#9ecbff',
				'json-number': '#79b8ff',
				'json-boolean': '#ffab70',
				'json-null': '#f97583',
			};

			return `<span style="color: ${colors[cls]}">${match}</span>`;
		});
}

function copyJsonToClipboard(recordId) {
	const jsonText = window.jsonData && window.jsonData[recordId];
	if (!jsonText) {
		tg.showAlert('❌ 无法获取JSON数据');
		return;
	}

	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(jsonText)
			.then(() => {
				tg.showAlert('✅ JSON已复制到剪贴板');
			})
			.catch(() => {
				// 降级到传统方法
				fallbackCopyText(jsonText);
			});
	} else {
		// 降级到传统方法
		fallbackCopyText(jsonText);
	}
}

function fallbackCopyText(text) {
	const textArea = document.createElement('textarea');
	textArea.value = text;
	textArea.style.position = 'fixed';
	textArea.style.opacity = '0';
	document.body.appendChild(textArea);
	textArea.select();

	try {
		document.execCommand('copy');
		tg.showAlert('✅ JSON已复制到剪贴板');
	} catch (err) {
		tg.showAlert('❌ 复制失败，请手动选择复制');
	}

	document.body.removeChild(textArea);
}

function formatFileSize(bytes) {
	if (!bytes) return '未知大小';
	const units = ['B', 'KB', 'MB', 'GB'];
	let size = bytes;
	let unitIndex = 0;

	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}

	return `${size.toFixed(1)} ${units[unitIndex]}`;
}

function formatDuration(seconds) {
	if (!seconds) return '0:00';
	const mins = Math.floor(seconds / 60);
	const secs = seconds % 60;
	return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function initFilters() {
	const searchInput = document.getElementById('searchInput');
	const mediaTypeFilter = document.getElementById('mediaTypeFilter');
	const dateFrom = document.getElementById('dateFrom');
	const dateTo = document.getElementById('dateTo');
	let filterTimeout;

	function applyFilters() {
		clearTimeout(filterTimeout);
		filterTimeout = setTimeout(() => {
			const filters = {
				search: searchInput.value.trim(),
				mediaType: mediaTypeFilter.value,
				dateFrom: dateFrom.value,
				dateTo: dateTo.value,
			};
			loadMediaGroupData(filters);
		}, 500);
	}

	searchInput.addEventListener('input', applyFilters);
	mediaTypeFilter.addEventListener('change', applyFilters);
	dateFrom.addEventListener('change', applyFilters);
	dateTo.addEventListener('change', applyFilters);
}

// 广告管理相关变量
let currentEditingCampaign = null;

// 广告管理功能
async function loadAdCampaigns() {
	const loadingIndicator = document.getElementById('adsLoadingIndicator');
	const emptyState = document.getElementById('adsEmptyState');
	const campaignsList = document.getElementById('campaignsList');

	try {
		loadingIndicator.style.display = 'flex';
		emptyState.style.display = 'none';
		campaignsList.style.display = 'none';

		const response = await AuthManager.secureApiRequest('/api/ads');

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const data = await response.json();
		loadingIndicator.style.display = 'none';

		if (data.campaigns && data.campaigns.length > 0) {
			renderCampaigns(data.campaigns);
			updateAdStats(data.campaigns);
			campaignsList.style.display = 'block';
		} else {
			emptyState.style.display = 'block';
			// 即使没有规则也要更新统计数据
			updateAdStats([]);
		}
	} catch (error) {
		console.error('加载广告规则失败:', error);
		loadingIndicator.style.display = 'none';
		campaignsList.innerHTML = `<div class="error">加载广告规则失败: ${error.message}</div>`;
		campaignsList.style.display = 'block';
	}
}

function renderCampaigns(campaigns) {
	const campaignsList = document.getElementById('campaignsList');

	campaignsList.innerHTML = campaigns
		.map((campaign) => {
			const statusClass = campaign.is_active ? 'status-active' : 'status-inactive';
			const statusText = campaign.is_active ? '活跃' : '停用';
			const frequencyText = getFrequencyText(campaign.frequency_days);

			return `
            <div class="campaign-card">
                <div class="campaign-header">
                    <div>
                        <div class="campaign-title">${campaign.name}</div>
                        <div class="campaign-details">
                            📺 ${campaign.target_channel_id} | 
                            ⏰ ${campaign.publish_time} | 
                            📅 ${frequencyText}
                        </div>
                        <div class="campaign-details">
                            📅 ${campaign.start_date} ~ ${campaign.end_date}
                        </div>
                    </div>
                    <div class="campaign-status ${statusClass}">${statusText}</div>
                </div>
                
                <div class="campaign-actions">
                    <button class="action-button" onclick="editCampaign(${campaign.id})">编辑</button>
                    <button class="action-button" onclick="triggerCampaign(${campaign.id})">立即发送</button>
                    <button class="action-button" onclick="toggleCampaign(${campaign.id}, ${!campaign.is_active})">
                        ${campaign.is_active ? '停用' : '启用'}
                    </button>
                    <button class="action-button danger" onclick="deleteCampaign(${campaign.id})">删除</button>
                </div>
            </div>
        `;
		})
		.join('');
}

function getFrequencyText(days) {
	if (days === 0) {
		return '每天';
	} else if (days === 1) {
		return '隔1天发送';
	} else if (days === 7) {
		return '每周 (隔7天)';
	} else if (days === 14) {
		return '每两周 (隔14天)';
	} else if (days === 30) {
		return '每月 (隔30天)';
	} else {
		return `隔${days}天发送`;
	}
}

function updateAdStats(campaigns) {
	const activeCampaigns = campaigns.filter((c) => c.is_active).length;
	document.getElementById('activeCampaigns').textContent = activeCampaigns;

	// 从后端获取真实的统计数据
	loadAdStats();
}

// 加载广告统计数据
async function loadAdStats() {
	try {
		const response = await AuthManager.secureApiRequest('/api/ads/stats');

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const stats = await response.json();

		// 更新统计数据
		document.getElementById('activeCampaigns').textContent = stats.activeCampaigns;
		document.getElementById('totalPosts').textContent = stats.totalPosts;
		document.getElementById('todayPosts').textContent = stats.todayPosts;
	} catch (error) {
		console.error('加载统计数据失败:', error);
		// 如果加载失败，保持现有数据不变
	}
}

function showCampaignModal(campaign = null) {
	currentEditingCampaign = campaign;
	const modal = document.getElementById('campaignModal');
	const title = document.getElementById('modalTitle');
	const form = document.getElementById('campaignForm');

	title.textContent = campaign ? '编辑广告规则' : '新建广告规则';

	if (campaign) {
		document.getElementById('campaignName').value = campaign.name || '';
		document.getElementById('targetChannelId').value = campaign.target_channel_id || '';
		document.getElementById('sourceMessageId').value = campaign.source_message_id || '';
		document.getElementById('startDate').value = campaign.start_date || '';
		document.getElementById('endDate').value = campaign.end_date || '';
		document.getElementById('frequencyDays').value = campaign.frequency_days || '0';
		// 数据库存储北京时间，直接显示
		document.getElementById('publishTime').value = campaign.publish_time || '14:00';
		document.getElementById('isPin').checked = campaign.is_pin || false;
	} else {
		form.reset();
		document.getElementById('publishTime').value = '14:00';
		document.getElementById('frequencyDays').value = '0'; // 默认每天发送
		const today = new Date().toISOString().split('T')[0];
		document.getElementById('startDate').value = today;
		const nextWeek = new Date();
		nextWeek.setDate(nextWeek.getDate() + 7);
		document.getElementById('endDate').value = nextWeek.toISOString().split('T')[0];
	}

	modal.style.display = 'flex';
}

// 编辑广告规则
async function editCampaign(campaignId) {
	try {
		const response = await AuthManager.secureApiRequest('/api/ads');
		const data = await response.json();
		const campaign = data.campaigns.find((c) => c.id === campaignId);
		if (campaign) {
			showCampaignModal(campaign);
		}
	} catch (error) {
		tg.showAlert('获取广告规则失败');
	}
}

// 立即发送广告
async function triggerCampaign(campaignId) {
	try {
		const response = await AuthManager.secureApiRequest('/api/ads/trigger', {
			method: 'POST',
			body: JSON.stringify({ campaignId }),
		});

		const result = await response.json();
		if (response.ok) {
			tg.showAlert('广告发送成功！');
			// 刷新统计数据
			loadAdStats();
		} else {
			tg.showAlert(`发送失败: ${result.error}`);
		}
	} catch (error) {
		tg.showAlert('发送广告失败');
	}
}

// 启用/停用广告规则
async function toggleCampaign(campaignId, isActive) {
	try {
		const response = await AuthManager.secureApiRequest(`/api/ads/campaigns/${campaignId}`, {
			method: 'PUT',
			body: JSON.stringify({ is_active: isActive }),
		});

		if (response.ok) {
			await loadAdCampaigns();
			tg.showAlert(isActive ? '规则已启用' : '规则已停用');
		} else {
			tg.showAlert('操作失败');
		}
	} catch (error) {
		tg.showAlert('操作失败');
	}
}

// 删除广告规则
async function deleteCampaign(campaignId) {
	if (!confirm('确定要删除这个广告规则吗？')) {
		return;
	}

	try {
		const response = await AuthManager.secureApiRequest(`/api/ads/campaigns/${campaignId}`, {
			method: 'DELETE',
		});

		if (response.ok) {
			await loadAdCampaigns();
			tg.showAlert('规则已删除');
		} else {
			tg.showAlert('删除失败');
		}
	} catch (error) {
		tg.showAlert('删除失败');
	}
}

// 数据表查看功能
let currentTableData = null;
let currentTableType = null;

// 查看广告规则表
async function viewCampaignsTable() {
	await viewDataTable('campaigns', '广告规则表 (ad_campaigns)');
}

// 查看广告发送记录表
async function viewPostsTable() {
	await viewDataTable('posts', '广告发送记录表 (ad_posts)');
}

// 通用数据表查看函数
async function viewDataTable(tableType, title) {
	currentTableType = tableType;
	const modal = document.getElementById('dataTableModal');
	const modalTitle = document.getElementById('dataTableTitle');
	const loading = document.getElementById('dataTableLoading');
	const error = document.getElementById('dataTableError');
	const container = document.getElementById('dataTableContainer');

	modalTitle.textContent = title;
	modal.style.display = 'flex';
	loading.style.display = 'flex';
	error.style.display = 'none';
	container.style.display = 'none';

	await loadTableData(tableType);
}

// 加载表数据
async function loadTableData(tableType) {
	const loading = document.getElementById('dataTableLoading');
	const error = document.getElementById('dataTableError');
	const container = document.getElementById('dataTableContainer');

	try {
		const response = await AuthManager.secureApiRequest(`/api/ads/tables/${tableType}`);

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const data = await response.json();
		const tableData = tableType === 'campaigns' ? data.campaigns : data.posts;
		currentTableData = tableData;

		loading.style.display = 'none';

		if (tableData && tableData.length > 0) {
			renderDataTable(tableData);
			container.style.display = 'block';
		} else {
			error.textContent = '暂无数据';
			error.style.display = 'block';
		}
	} catch (err) {
		console.error('加载表数据失败:', err);
		loading.style.display = 'none';
		error.textContent = `加载失败: ${err.message}`;
		error.style.display = 'block';
	}
}

// 渲染数据表
function renderDataTable(data) {
	const head = document.getElementById('dataTableHead');
	const body = document.getElementById('dataTableBody');

	if (!data || data.length === 0) return;

	// 生成表头
	const columns = Object.keys(data[0]);
	head.innerHTML = `<tr>${columns.map((col) => `<th>${col}</th>`).join('')}</tr>`;

	// 生成表体
	body.innerHTML = data
		.map((row) => {
			return `<tr>${columns
				.map((col) => {
					let value = row[col];
					// 格式化显示
					if (value === null || value === undefined) {
						value = '<span style="color: var(--text-light);">NULL</span>';
					} else if (typeof value === 'boolean') {
						value = value ? '✅' : '❌';
					} else if (typeof value === 'string' && value.length > 50) {
						value = `<span title="${value}">${value.substring(0, 50)}...</span>`;
					}
					return `<td>${value}</td>`;
				})
				.join('')}</tr>`;
		})
		.join('');
}

// 复制表数据为JSON
function copyTableData() {
	if (!currentTableData) {
		tg.showAlert('没有数据可复制');
		return;
	}

	const jsonText = JSON.stringify(currentTableData, null, 2);

	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(jsonText)
			.then(() => {
				tg.showAlert('✅ 数据已复制到剪贴板');
			})
			.catch(() => {
				fallbackCopyText(jsonText);
			});
	} else {
		fallbackCopyText(jsonText);
	}
}

// 关闭数据表模态框
function closeDataTableModal() {
	document.getElementById('dataTableModal').style.display = 'none';
	currentTableData = null;
	currentTableType = null;
}

// 选项卡切换
function initTabs() {
	const tabButtons = document.querySelectorAll('.tab-button');
	const mediaTab = document.getElementById('mediaTab');
	const adsTab = document.getElementById('adsTab');
	const groupsTab = document.getElementById('groupsTab');

	tabButtons.forEach((button) => {
		button.addEventListener('click', () => {
			const tabName = button.dataset.tab;

			// 更新按钮状态
			tabButtons.forEach((btn) => btn.classList.remove('active'));
			button.classList.add('active');

			// 隐藏所有标签页
			mediaTab.classList.add('hidden');
			adsTab.classList.add('hidden');
			groupsTab.classList.add('hidden');

			// 切换内容
			if (tabName === 'media') {
				mediaTab.classList.remove('hidden');
				loadMediaGroupData(); // 切换到媒体组时才加载数据
				// 确保视图状态正确初始化
				setTimeout(() => {
					initializeViewState();
				}, 100);
			} else if (tabName === 'ads') {
				adsTab.classList.remove('hidden');
				loadAdCampaigns(); // 加载广告规则
			} else if (tabName === 'groups') {
				groupsTab.classList.remove('hidden');
				loadGroupManagement(); // 加载群管理数据
			}
		});
	});
}

// 模态框事件
function initModal() {
	const modal = document.getElementById('campaignModal');
	const closeBtn = document.getElementById('modalClose');
	const cancelBtn = document.getElementById('cancelBtn');
	const form = document.getElementById('campaignForm');
	const addBtn = document.getElementById('addCampaignBtn');

	// 数据表查看按钮
	const viewCampaignsBtn = document.getElementById('viewCampaignsTableBtn');
	const viewPostsBtn = document.getElementById('viewPostsTableBtn');

	// 数据表模态框相关元素
	const dataTableModal = document.getElementById('dataTableModal');
	const dataTableClose = document.getElementById('dataTableClose');
	const copyTableDataBtn = document.getElementById('copyTableDataBtn');
	const refreshTableBtn = document.getElementById('refreshTableBtn');
	const closeTableBtn = document.getElementById('closeTableBtn');

	// 打开模态框
	if (addBtn) {
		addBtn.addEventListener('click', () => showCampaignModal());
	}

	// 数据表查看按钮事件
	if (viewCampaignsBtn) {
		viewCampaignsBtn.addEventListener('click', viewCampaignsTable);
	}
	if (viewPostsBtn) {
		viewPostsBtn.addEventListener('click', viewPostsTable);
	}

	// 数据表模态框事件
	if (dataTableClose) dataTableClose.addEventListener('click', closeDataTableModal);
	if (closeTableBtn) closeTableBtn.addEventListener('click', closeDataTableModal);
	if (copyTableDataBtn) copyTableDataBtn.addEventListener('click', copyTableData);
	if (refreshTableBtn) {
		refreshTableBtn.addEventListener('click', () => {
			if (currentTableType) {
				loadTableData(currentTableType);
			}
		});
	}

	// 点击背景关闭数据表模态框
	if (dataTableModal) {
		dataTableModal.addEventListener('click', (e) => {
			if (e.target === dataTableModal) closeDataTableModal();
		});
	}

	// 关闭模态框
	function closeModal() {
		modal.style.display = 'none';
		currentEditingCampaign = null;
	}

	if (closeBtn) closeBtn.addEventListener('click', closeModal);
	if (cancelBtn) cancelBtn.addEventListener('click', closeModal);

	// 点击背景关闭
	modal.addEventListener('click', (e) => {
		if (e.target === modal) closeModal();
	});

	// 表单提交
	if (form) {
		form.addEventListener('submit', async (e) => {
			e.preventDefault();

			// 获取用户输入的本地时间，直接存储为北京时间
			const beijingTime = document.getElementById('publishTime').value;

			const formData = {
				name: document.getElementById('campaignName').value,
				target_channel_id: document.getElementById('targetChannelId').value,
				source_message_id: parseInt(document.getElementById('sourceMessageId').value),
				start_date: document.getElementById('startDate').value,
				end_date: document.getElementById('endDate').value,
				frequency_days: parseInt(document.getElementById('frequencyDays').value),
				publish_time: beijingTime, // 直接存储北京时间
				is_pin: document.getElementById('isPin').checked,
			};

			try {
				const url = currentEditingCampaign ? `/api/ads/campaigns/${currentEditingCampaign.id}` : '/api/ads';
				const method = currentEditingCampaign ? 'PUT' : 'POST';

				const response = await AuthManager.secureApiRequest(url, {
					method,
					body: JSON.stringify(formData),
				});

				if (response.ok) {
					closeModal();
					await loadAdCampaigns();
					tg.showAlert(currentEditingCampaign ? '规则已更新' : '规则已创建');
				} else {
					const error = await response.json();
					tg.showAlert(`操作失败: ${error.error}`);
				}
			} catch (error) {
				tg.showAlert('操作失败，请重试');
			}
		});
	}
}

document.addEventListener('DOMContentLoaded', async function () {
	initUserInfo();
	initFilters();
	initTabs();
	initModal();
	initPasteButton();

	// 等待认证完成后再加载数据
	try {
		await waitForAuthentication();
		loadAdCampaigns(); // 认证完成后加载广告管理数据
	} catch (error) {
		console.error('认证失败，延迟加载数据:', error);
		// 认证失败时，延迟3秒后再尝试加载（此时用户可能已经手动触发了认证）
		setTimeout(() => {
			if (window.AuthManager && window.AuthManager.isAuthenticated) {
				loadAdCampaigns();
			}
		}, 3000);
	}

	const today = new Date().toISOString().split('T')[0];
	document.getElementById('dateTo').value = today;

	// 显示时区信息
	initTimezoneInfo();
});

// 等待认证完成的辅助函数
async function waitForAuthentication() {
	return new Promise((resolve, reject) => {
		// 如果已经认证完成，直接返回
		if (window.AuthManager && window.AuthManager.isAuthenticated) {
			console.log('✅ 认证已完成，可以安全调用API');
			resolve();
			return;
		}

		// 监听认证完成事件
		const authCompleteHandler = (event) => {
			console.log('✅ 收到认证完成事件，可以安全调用API');
			window.removeEventListener('authenticationComplete', authCompleteHandler);
			clearTimeout(timeoutId);
			resolve();
		};

		window.addEventListener('authenticationComplete', authCompleteHandler);

		// 设置超时保护（10秒）
		const timeoutId = setTimeout(() => {
			console.warn('⚠️ 认证等待超时，尝试手动认证');
			window.removeEventListener('authenticationComplete', authCompleteHandler);

			// 如果等待超时，尝试手动认证
			if (window.AuthManager) {
				window.AuthManager.authenticate().then(resolve).catch(reject);
			} else {
				reject(new Error('认证超时且AuthManager不可用'));
			}
		}, 10000);
	});
}

window.addEventListener('error', function (event) {
	console.error('应用错误:', event.error);
	tg.showAlert('应用发生错误，请刷新重试');
});

// 时区转换函数已移除
// 现在数据库直接存储北京时间，不再需要UTC转换
// 用户输入的北京时间直接存储和显示

function formatBeijingTime(timeString) {
	// 简单的时间格式化函数，确保格式为 HH:MM
	if (!timeString) return '00:00';
	const [hours, minutes] = timeString.split(':').map(Number);
	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

function initTimezoneInfo() {
	const timezoneInfo = document.getElementById('timezoneInfo');
	if (timezoneInfo) {
		timezoneInfo.textContent = `🕐 系统使用北京时间 (UTC+8)`;
	}
}

// 初始化粘贴按钮功能
function initPasteButton() {
	const pasteBtn = document.getElementById('pasteChannelId');
	if (!pasteBtn) return;

	// 检查是否支持剪切板API
	const isClipboardSupported = navigator.clipboard && navigator.clipboard.readText;

	if (!isClipboardSupported) {
		// 如果不支持现代剪切板API，禁用按钮并显示提示
		pasteBtn.disabled = true;
		pasteBtn.title = '该环境不支持剪切板功能';
		pasteBtn.style.opacity = '0.5';
		return;
	}

	// 添加点击事件监听器
	pasteBtn.addEventListener('click', async function () {
		const targetInput = document.getElementById('targetChannelId');

		try {
			// 开始粘贴动画
			pasteBtn.style.transform = 'scale(0.9)';
			pasteBtn.textContent = '⏳';
			pasteBtn.disabled = true;

			// 尝试读取剪切板内容
			const clipboardText = await navigator.clipboard.readText();

			if (clipboardText && clipboardText.trim()) {
				// 验证是否像频道ID（以-100开头的数字）
				const channelIdPattern = /^-?\d+$/;
				const trimmedText = clipboardText.trim();

				if (channelIdPattern.test(trimmedText)) {
					targetInput.value = trimmedText;
					// 触发input事件以便表单验证
					targetInput.dispatchEvent(new Event('input', { bubbles: true }));

					// 成功动画
					pasteBtn.textContent = '✅';
					tg.showAlert('✅ 频道ID已粘贴成功');

					setTimeout(() => {
						pasteBtn.textContent = '📋';
						pasteBtn.style.transform = 'scale(1)';
						pasteBtn.disabled = false;
					}, 1000);
				} else {
					// 格式不正确，但仍然粘贴
					targetInput.value = trimmedText;
					targetInput.dispatchEvent(new Event('input', { bubbles: true }));

					pasteBtn.textContent = '⚠️';
					tg.showAlert('⚠️ 已粘贴内容，但格式可能不是有效的频道ID');

					setTimeout(() => {
						pasteBtn.textContent = '📋';
						pasteBtn.style.transform = 'scale(1)';
						pasteBtn.disabled = false;
					}, 1500);
				}
			} else {
				// 剪切板为空
				pasteBtn.textContent = '❌';
				tg.showAlert('❌ 剪切板为空或无法读取');

				setTimeout(() => {
					pasteBtn.textContent = '📋';
					pasteBtn.style.transform = 'scale(1)';
					pasteBtn.disabled = false;
				}, 1000);
			}
		} catch (error) {
			console.error('粘贴失败:', error);

			// 错误处理
			pasteBtn.textContent = '❌';

			// 根据错误类型显示不同的提示
			let errorMessage = '❌ 无法访问剪切板';
			if (error.name === 'NotAllowedError') {
				errorMessage = '❌ 请授权访问剪切板，或手动粘贴';
			} else if (error.name === 'NotFoundError') {
				errorMessage = '❌ 剪切板为空';
			}

			tg.showAlert(errorMessage);

			setTimeout(() => {
				pasteBtn.textContent = '📋';
				pasteBtn.style.transform = 'scale(1)';
				pasteBtn.disabled = false;
			}, 1500);
		}
	});

	// 添加悬停效果
	pasteBtn.addEventListener('mouseenter', function () {
		if (!pasteBtn.disabled) {
			pasteBtn.style.transform = 'scale(1.05)';
		}
	});

	pasteBtn.addEventListener('mouseleave', function () {
		if (!pasteBtn.disabled) {
			pasteBtn.style.transform = 'scale(1)';
		}
	});
}

// #region 🏗️ 群组管理功能

// 群组管理相关变量
let currentGroupConfig = null;
let selectedGroupId = null;
let currentRules = [];
let editingRuleIndex = -1;
let currentUserPermission = null; // 当前用户在选中群组的权限级别
let currentAdmins = []; // 当前群组的管理员列表
let currentGroupTitle = null; // 当前选中群组的标题

// 群组管理主要加载函数
async function loadGroupManagement() {
	console.log('加载群组管理数据...');
	try {
		await loadGroupsList();
		await loadGroupManagementStats();
		
		// 初始化事件监听器（只初始化一次）
		if (!window.groupManagementInitialized) {
			initGroupManagementEventListeners();
			window.groupManagementInitialized = true;
		}
	} catch (error) {
		console.error('加载群组管理数据失败:', error);
		showToast('加载群组管理数据失败: ' + error.message, 'error');
	}
}

// 加载群组列表
async function loadGroupsList() {
	try {
		console.log('加载群组列表...');
		
		const response = await fetch('/api/group-config/groups', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			}
		});
		
		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}
		
		const data = await response.json();
		
		// 清空现有选项
		const groupSelect = document.getElementById('groupSelect');
		groupSelect.innerHTML = '<option value="">请选择要配置的群组...</option>';
		
		// 添加群组选项
		data.groups.forEach(group => {
			const option = document.createElement('option');
			option.value = group.chat_id;
			
			// 添加权限级别显示
			const permissionText = group.permission_level === 'super_admin' ? ' [超级管理]' : ' [管理员]';
			option.textContent = `${group.chat_title || group.chat_id}${permissionText}`;
			
			groupSelect.appendChild(option);
		});
		
		console.log(`加载了 ${data.groups.length} 个群组`);
	} catch (error) {
		console.error('加载群组列表失败:', error);
		showToast('加载群组列表失败: ' + error.message, 'error');
	}
}

// 加载群组管理统计
async function loadGroupManagementStats() {
	try {
		// 这里可以添加统计API调用
		// 暂时设置默认值
		document.getElementById('configuredGroupsCount').textContent = '-';
		document.getElementById('activeVerificationCount').textContent = '-';
		document.getElementById('totalRulesCount').textContent = '-';
	} catch (error) {
		console.error('加载群组管理统计失败:', error);
	}
}

// 群组选择处理
function handleGroupSelection() {
	const groupSelect = document.getElementById('groupSelect');
	
	groupSelect.addEventListener('change', async (e) => {
		const selectedValue = e.target.value;
		
		if (selectedValue) {
			selectedGroupId = selectedValue;
			
			// 获取群组标题和权限级别
			const selectedOption = groupSelect.options[groupSelect.selectedIndex];
			currentGroupTitle = selectedOption.textContent.replace(/ \[超级管理\]| \[管理员\]/g, '');
			currentUserPermission = selectedOption.textContent.includes('[超级管理]') ? 'super_admin' : 'admin';
			
			await loadGroupConfiguration(selectedValue);
			document.getElementById('configPanel').style.display = 'block';
			updateDeleteButtonVisibility();
		} else {
			selectedGroupId = null;
			currentGroupTitle = null;
			currentUserPermission = null;
			document.getElementById('configPanel').style.display = 'none';
			updateDeleteButtonVisibility();
		}
	});
}

// 更新删除按钮的显示状态
function updateDeleteButtonVisibility() {
	const deleteBtn = document.getElementById('deleteGroupBtn');
	if (!deleteBtn) return;
	
	// 只有选择了群组且用户是超级管理员时才显示删除按钮
	if (selectedGroupId && currentUserPermission === 'super_admin') {
		deleteBtn.style.display = 'inline-block';
	} else {
		deleteBtn.style.display = 'none';
	}
}

// 加载群组配置
async function loadGroupConfiguration(groupId) {
	try {
		console.log('加载群组配置:', groupId);
		
		const response = await fetch(`/api/group-config/config/${encodeURIComponent(groupId)}`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			}
		});
		
		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}
		
		const data = await response.json();
		currentGroupConfig = data.config;
		
		// 更新UI
		updateGroupConfigurationUI(currentGroupConfig);
		
		// 如果是超级管理员，加载管理员列表
		if (currentUserPermission === 'super_admin') {
			await loadGroupAdmins(groupId);
		}
		
		console.log('群组配置加载完成');
	} catch (error) {
		console.error('加载群组配置失败:', error);
		showToast('加载群组配置失败: ' + error.message, 'error');
	}
}

// 更新群组配置UI
function updateGroupConfigurationUI(config) {
	// 更新举报模式
	const reportMode = config.report_mode || 'disabled';
	const reportModeInput = document.querySelector(`input[name="reportMode"][value="${reportMode}"]`);
	if (reportModeInput) {
		reportModeInput.checked = true;
	}
	
	// 更新推送目标群组
	updateTargetGroupsList(config.report_target_groups || []);
	
	// 显示/隐藏推送目标配置
	const reportTargetsSection = document.getElementById('reportTargetsSection');
	if (reportTargetsSection) {
		reportTargetsSection.style.display = reportMode === 'group' ? 'block' : 'none';
	}
	
	// 更新功能开关
	const newMemberVerification = document.getElementById('newMemberVerification');
	const cloudFilterEnabled = document.getElementById('cloudFilterEnabled');
	const autoForwardEnabled = document.getElementById('autoForwardEnabled');
	
	if (newMemberVerification) newMemberVerification.checked = config.new_member_verification || false;
	if (cloudFilterEnabled) cloudFilterEnabled.checked = config.cloud_filter_enabled || false;
	if (autoForwardEnabled) autoForwardEnabled.checked = config.auto_forward_enabled || false;
	
	// 更新群规列表
	currentRules = config.rules || [];
	renderRulesList();
	
	// 显示/隐藏管理员管理区域（只有超级管理员可见）
	const adminManagementSection = document.getElementById('adminManagementSection');
	if (adminManagementSection) {
		adminManagementSection.style.display = currentUserPermission === 'super_admin' ? 'block' : 'none';
	}
	
	// 启用保存按钮
	const saveBtn = document.getElementById('saveConfigBtn');
	if (saveBtn) saveBtn.disabled = false;
}

// 更新推送目标群组列表
function updateTargetGroupsList(targets) {
	const targetsList = document.getElementById('targetsList');
	if (!targetsList) return;
	
	targetsList.innerHTML = '';
	
	if (!targets || targets.length === 0) {
		targetsList.innerHTML = '<div class="empty-state">暂无配置管理群</div>';
		return;
	}
	
	targets.forEach((target, index) => {
		const targetItem = document.createElement('div');
		targetItem.className = 'target-item';
		targetItem.innerHTML = `
			<div class="target-info">
				<div class="target-title">${target.title}</div>
				<div class="target-id">${target.chat_id}</div>
			</div>
			<button class="btn btn-sm btn-danger" onclick="removeTarget(${index})">
				🗑️ 删除
			</button>
		`;
		targetsList.appendChild(targetItem);
	});
}

// 删除推送目标
function removeTarget(index) {
	if (!currentGroupConfig.report_target_groups) return;
	
	currentGroupConfig.report_target_groups.splice(index, 1);
	updateTargetGroupsList(currentGroupConfig.report_target_groups);
	markConfigChanged();
}

// 渲染群规列表
function renderRulesList() {
	const rulesList = document.getElementById('rulesList');
	if (!rulesList) return;
	
	rulesList.innerHTML = '';
	
	if (!currentRules || currentRules.length === 0) {
		rulesList.innerHTML = '<div class="empty-state">暂无群规配置</div>';
		return;
	}
	
	currentRules.forEach((rule, index) => {
		const ruleItem = document.createElement('div');
		ruleItem.className = 'rule-item';
		
		let statusIcon = '';
		if (rule.canAccess === true) {
			statusIcon = '<span class="status-icon success" title="可访问">✅</span>';
		} else if (rule.canAccess === false) {
			statusIcon = '<span class="status-icon error" title="无法访问">❌</span>';
		}
		
		ruleItem.innerHTML = `
			<div class="rule-info">
				<div class="rule-title">${rule.title || '群规'} ${statusIcon}</div>
				<div class="rule-url">${rule.url}</div>
			</div>
			<div class="rule-actions">
				<button class="btn btn-sm btn-secondary" onclick="editRule(${index})">
					✏️ 编辑
				</button>
				<button class="btn btn-sm btn-danger" onclick="removeRule(${index})">
					🗑️ 删除
				</button>
			</div>
		`;
		rulesList.appendChild(ruleItem);
	});
}

// 编辑群规
function editRule(index) {
	editingRuleIndex = index;
	const rule = currentRules[index];
	
	const ruleModalTitle = document.getElementById('ruleModalTitle');
	const ruleUrl = document.getElementById('ruleUrl');
	const ruleTitle = document.getElementById('ruleTitle');
	
	if (ruleModalTitle) ruleModalTitle.textContent = '编辑群规';
	if (ruleUrl) ruleUrl.value = rule.url || '';
	if (ruleTitle) ruleTitle.value = rule.title || '';
	
	showModal('ruleModal');
}

// 删除群规
function removeRule(index) {
	if (confirm('确定要删除这条群规吗？')) {
		currentRules.splice(index, 1);
		renderRulesList();
		markConfigChanged();
	}
}

// 显示模态框
function showModal(modalId) {
	const modal = document.getElementById(modalId);
	if (modal) modal.style.display = 'flex';
}

// 隐藏模态框
function hideModal(modalId) {
	const modal = document.getElementById(modalId);
	if (modal) modal.style.display = 'none';
}

// 标记配置已修改
function markConfigChanged() {
	const saveBtn = document.getElementById('saveConfigBtn');
	if (saveBtn) saveBtn.disabled = false;
}

// 保存群组配置
async function saveGroupConfiguration() {
	if (!selectedGroupId) {
		showToast('请先选择群组', 'error');
		return;
	}
	
	try {
		console.log('保存群组配置:', selectedGroupId);
		
		// 收集当前配置
		const config = collectCurrentConfiguration();
		
		const response = await fetch('/api/group-config/save', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({
				chat_id: selectedGroupId,
				...config
			})
		});
		
		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
		}
		
		const result = await response.json();
		
		if (result.success) {
			showToast(result.message || '配置保存成功', 'success');
			const saveBtn = document.getElementById('saveConfigBtn');
			if (saveBtn) saveBtn.disabled = true;
			
			// 更新当前配置
			currentGroupConfig = { ...currentGroupConfig, ...config };
		} else {
			throw new Error(result.error || '保存失败');
		}
		
		console.log('群组配置保存成功');
	} catch (error) {
		console.error('保存群组配置失败:', error);
		showToast('保存失败: ' + error.message, 'error');
	}
}

// 收集当前配置
function collectCurrentConfiguration() {
	const reportModeInput = document.querySelector('input[name="reportMode"]:checked');
	const reportMode = reportModeInput ? reportModeInput.value : 'disabled';
	
	const newMemberVerification = document.getElementById('newMemberVerification');
	const cloudFilterEnabled = document.getElementById('cloudFilterEnabled');
	const autoForwardEnabled = document.getElementById('autoForwardEnabled');
	
	return {
		report_mode: reportMode,
		report_target_groups: currentGroupConfig?.report_target_groups || [],
		new_member_verification: newMemberVerification ? newMemberVerification.checked : false,
		cloud_filter_enabled: cloudFilterEnabled ? cloudFilterEnabled.checked : false,
		auto_forward_enabled: autoForwardEnabled ? autoForwardEnabled.checked : false,
		rules: currentRules
	};
}

// 初始化群组管理事件监听器
function initGroupManagementEventListeners() {
	// 群组选择
	handleGroupSelection();
	
	// 举报模式变化
	document.querySelectorAll('input[name="reportMode"]').forEach(radio => {
		radio.addEventListener('change', (e) => {
			const reportTargetsSection = document.getElementById('reportTargetsSection');
			if (reportTargetsSection) {
				reportTargetsSection.style.display = e.target.value === 'group' ? 'block' : 'none';
			}
			markConfigChanged();
		});
	});
	
	// 功能开关变化
	['newMemberVerification', 'cloudFilterEnabled', 'autoForwardEnabled'].forEach(id => {
		const element = document.getElementById(id);
		if (element) {
			element.addEventListener('change', markConfigChanged);
		}
	});
	
	// 添加群组按钮
	const addGroupBtn = document.getElementById('addGroupBtn');
	if (addGroupBtn) {
		addGroupBtn.addEventListener('click', () => {
			const groupChatId = document.getElementById('groupChatId');
			const groupChatTitle = document.getElementById('groupChatTitle');
			
			if (groupChatId) groupChatId.value = '';
			if (groupChatTitle) groupChatTitle.value = '';
			showModal('addGroupModal');
		});
	}
	
	// 删除群组按钮
	const deleteGroupBtn = document.getElementById('deleteGroupBtn');
	if (deleteGroupBtn) {
		deleteGroupBtn.addEventListener('click', () => {
			showDeleteGroupModal();
		});
	}
	
	// 添加管理群按钮
	const addTargetBtn = document.getElementById('addTargetBtn');
	if (addTargetBtn) {
		addTargetBtn.addEventListener('click', () => {
			const targetChatId = document.getElementById('targetChatId');
			const targetTitle = document.getElementById('targetTitle');
			
			if (targetChatId) targetChatId.value = '';
			if (targetTitle) targetTitle.value = '';
			showModal('addTargetModal');
		});
	}
	
	// 添加群规按钮
	const addRuleBtn = document.getElementById('addRuleBtn');
	if (addRuleBtn) {
		addRuleBtn.addEventListener('click', () => {
			editingRuleIndex = -1;
			const ruleModalTitle = document.getElementById('ruleModalTitle');
			const ruleUrl = document.getElementById('ruleUrl');
			const ruleTitle = document.getElementById('ruleTitle');
			
			if (ruleModalTitle) ruleModalTitle.textContent = '添加群规';
			if (ruleUrl) ruleUrl.value = '';
			if (ruleTitle) ruleTitle.value = '';
			showModal('ruleModal');
		});
	}
	
	// 添加管理员按钮
	const addAdminBtn = document.getElementById('addAdminBtn');
	if (addAdminBtn) {
		addAdminBtn.addEventListener('click', () => {
			const adminUserId = document.getElementById('adminUserId');
			const adminUserName = document.getElementById('adminUserName');
			
			if (adminUserId) adminUserId.value = '';
			if (adminUserName) adminUserName.value = '';
			showModal('addAdminModal');
		});
	}
	
	// 权限要求模态框事件监听器
	setupPermissionModalEvents();
	
	// 删除群组模态框事件监听器
	setupDeleteGroupModalEvents();
	
	// 保存配置按钮
	const saveConfigBtn = document.getElementById('saveConfigBtn');
	if (saveConfigBtn) {
		saveConfigBtn.addEventListener('click', saveGroupConfiguration);
	}
	
	// 重置配置按钮
	const resetConfigBtn = document.getElementById('resetConfigBtn');
	if (resetConfigBtn) {
		resetConfigBtn.addEventListener('click', () => {
			if (confirm('确定要重置配置吗？未保存的修改将丢失。')) {
				if (currentGroupConfig) {
					updateGroupConfigurationUI(currentGroupConfig);
				}
			}
		});
	}
	
	// 模态框关闭事件
	setupGroupModalEvents();
}

// 设置群组管理模态框事件
function setupGroupModalEvents() {
	// 添加群组模态框
	const closeAddGroupModal = document.getElementById('closeAddGroupModal');
	const cancelGroupBtn = document.getElementById('cancelGroupBtn');
	const saveGroupBtn = document.getElementById('saveGroupBtn');
	
	if (closeAddGroupModal) closeAddGroupModal.addEventListener('click', () => hideModal('addGroupModal'));
	if (cancelGroupBtn) cancelGroupBtn.addEventListener('click', () => hideModal('addGroupModal'));
	if (saveGroupBtn) saveGroupBtn.addEventListener('click', saveGroup);
	
	// 添加管理群模态框
	const closeAddTargetModal = document.getElementById('closeAddTargetModal');
	const cancelTargetBtn = document.getElementById('cancelTargetBtn');
	const saveTargetBtn = document.getElementById('saveTargetBtn');
	
	if (closeAddTargetModal) closeAddTargetModal.addEventListener('click', () => hideModal('addTargetModal'));
	if (cancelTargetBtn) cancelTargetBtn.addEventListener('click', () => hideModal('addTargetModal'));
	if (saveTargetBtn) saveTargetBtn.addEventListener('click', saveTarget);
	
	// 群规模态框
	const closeRuleModal = document.getElementById('closeRuleModal');
	const cancelRuleBtn = document.getElementById('cancelRuleBtn');
	const saveRuleBtn = document.getElementById('saveRuleBtn');
	
	if (closeRuleModal) closeRuleModal.addEventListener('click', () => hideModal('ruleModal'));
	if (cancelRuleBtn) cancelRuleBtn.addEventListener('click', () => hideModal('ruleModal'));
	if (saveRuleBtn) saveRuleBtn.addEventListener('click', saveRule);
	
	// 添加管理员模态框
	const closeAddAdminModal = document.getElementById('closeAddAdminModal');
	const cancelAdminBtn = document.getElementById('cancelAdminBtn');
	const saveAdminBtn = document.getElementById('saveAdminBtn');
	
	if (closeAddAdminModal) closeAddAdminModal.addEventListener('click', () => hideModal('addAdminModal'));
	if (cancelAdminBtn) cancelAdminBtn.addEventListener('click', () => hideModal('addAdminModal'));
	if (saveAdminBtn) saveAdminBtn.addEventListener('click', saveAdmin);
}

// 设置权限要求模态框事件
function setupPermissionModalEvents() {
	// 关闭模态框按钮
	const closePermissionModal = document.getElementById('closePermissionModal');
	const closePermissionBtn = document.getElementById('closePermissionBtn');
	
	if (closePermissionModal) {
		closePermissionModal.addEventListener('click', hidePermissionRequirementModal);
	}
	
	if (closePermissionBtn) {
		closePermissionBtn.addEventListener('click', hidePermissionRequirementModal);
	}
	
	// 重新尝试按钮
	const retryAddGroupBtn = document.getElementById('retryAddGroupBtn');
	if (retryAddGroupBtn) {
		retryAddGroupBtn.addEventListener('click', () => {
			hidePermissionRequirementModal();
			// 重新打开添加群组模态框
			showModal('addGroupModal');
		});
	}
	
	// 点击背景关闭模态框
	const permissionModal = document.getElementById('permissionRequirementModal');
	if (permissionModal) {
		permissionModal.addEventListener('click', (e) => {
			if (e.target === permissionModal) {
				hidePermissionRequirementModal();
			}
		});
	}
}

// 保存管理群
function saveTarget() {
	const targetChatId = document.getElementById('targetChatId');
	const targetTitle = document.getElementById('targetTitle');
	
	const chatId = targetChatId ? targetChatId.value.trim() : '';
	const title = targetTitle ? targetTitle.value.trim() : '';
	
	if (!chatId) {
		showToast('请输入群组ID', 'error');
		return;
	}
	
	if (!title) {
		showToast('请输入群组名称', 'error');
		return;
	}
	
	// 验证群组ID格式
	if (!chatId.match(/^-100\d+$/)) {
		showToast('群组ID格式错误，应以-100开头', 'error');
		return;
	}
	
	// 添加到当前配置
	if (!currentGroupConfig.report_target_groups) {
		currentGroupConfig.report_target_groups = [];
	}
	
	// 检查是否已存在
	const exists = currentGroupConfig.report_target_groups.some(target => target.chat_id === chatId);
	if (exists) {
		showToast('该群组已存在', 'error');
		return;
	}
	
	currentGroupConfig.report_target_groups.push({
		chat_id: chatId,
		title: title,
		is_active: true,
		added_at: new Date().toISOString()
	});
	
	updateTargetGroupsList(currentGroupConfig.report_target_groups);
	hideModal('addTargetModal');
	markConfigChanged();
	
	showToast('管理群添加成功', 'success');
}

// 保存群规
function saveRule() {
	const ruleUrlInput = document.getElementById('ruleUrl');
	const ruleTitleInput = document.getElementById('ruleTitle');
	
	const url = ruleUrlInput ? ruleUrlInput.value.trim() : '';
	const title = ruleTitleInput ? ruleTitleInput.value.trim() || '群规' : '群规';
	
	if (!url) {
		showToast('请输入群规链接', 'error');
		return;
	}
	
	if (!isValidTelegramUrl(url)) {
		showToast('请输入有效的Telegram链接', 'error');
		return;
	}
	
	// 检查重复链接
	const existingIndex = currentRules.findIndex(rule => rule.url === url);
	if (existingIndex !== -1 && existingIndex !== editingRuleIndex) {
		showToast('该链接已存在', 'error');
		return;
	}
	
	const newRule = { url, title };
	
	if (editingRuleIndex === -1) {
		// 添加新规则
		if (currentRules.length >= 10) {
			showToast('最多只能添加10条群规', 'error');
			return;
		}
		currentRules.push(newRule);
	} else {
		// 编辑现有规则
		currentRules[editingRuleIndex] = newRule;
	}
	
	renderRulesList();
	hideModal('ruleModal');
	markConfigChanged();
	
	showToast(`${editingRuleIndex === -1 ? '群规添加成功' : '群规修改成功'}`, 'success');
}

// 验证Telegram URL
function isValidTelegramUrl(url) {
	try {
		const urlObj = new URL(url);
		return urlObj.hostname === 't.me' && urlObj.pathname.length > 1;
	} catch {
		return false;
	}
}

// 保存群组
async function saveGroup() {
	const groupChatIdInput = document.getElementById('groupChatId');
	const groupChatTitleInput = document.getElementById('groupChatTitle');
	
	const chatId = groupChatIdInput ? groupChatIdInput.value.trim() : '';
	const chatTitle = groupChatTitleInput ? groupChatTitleInput.value.trim() : '';
	
	if (!chatId) {
		showToast('请输入群组ID', 'error');
		return;
	}
	
	// 验证群组ID格式
	if (!chatId.match(/^-100\d+$/)) {
		showToast('群组ID格式错误，应以-100开头', 'error');
		return;
	}
	
	// 检查是否已存在
	const groupSelect = document.getElementById('groupSelect');
	const existingOptions = Array.from(groupSelect.options);
	const exists = existingOptions.some(option => option.value === chatId);
	
	if (exists) {
		showToast('该群组已存在', 'error');
		return;
	}
	
	try {
		// 禁用按钮并显示加载状态
		const saveBtn = document.getElementById('saveGroupBtn');
		if (saveBtn) {
			saveBtn.disabled = true;
			saveBtn.textContent = '添加中...';
		}
		
		const response = await fetch('/api/group-config/add-group', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({
				chatId: chatId,
				chatTitle: chatTitle || undefined
			})
		});
		
		const result = await response.json();
		
		if (response.ok && result.success) {
			// 添加到下拉框
			const option = document.createElement('option');
			option.value = result.group.chat_id;
			const permissionText = result.group.permission_level === 'super_admin' ? ' [超级管理]' : ' [管理员]';
			option.textContent = `${result.group.chat_title}${permissionText}`;
			groupSelect.appendChild(option);
			
			// 自动选择新添加的群组
			groupSelect.value = result.group.chat_id;
			
			// 触发选择事件
			const changeEvent = new Event('change');
			groupSelect.dispatchEvent(changeEvent);
			
			hideModal('addGroupModal');
			showToast(result.message || '群组添加成功', 'success');
			
		} else {
			// 403 Forbidden - 显示专门的权限要求模态框
			if (response.status === 403) {
				console.warn('权限验证失败 - 403 Forbidden:', {
					status: response.status,
					error: result.error,
					chatId: chatId,
					suggestion: '请检查您在群组中的权限以及Bot的管理员状态'
				});
				showPermissionRequirementModal(result.error || '权限验证失败');
			} else {
				// 其他错误显示toast
				let errorMessage = result.error || '添加群组失败';
				
				if (response.status === 400) {
					errorMessage += '\n\n请检查群组ID格式是否正确';
				} else if (response.status === 401) {
					errorMessage += '\n\n认证失败，请重新登录';
				}
				
				showToast(errorMessage, 'error');
			}
		}
		
	} catch (error) {
		console.error('添加群组失败:', error);
		showToast('添加群组失败: ' + error.message, 'error');
	} finally {
		// 恢复按钮状态
		const saveBtn = document.getElementById('saveGroupBtn');
		if (saveBtn) {
			saveBtn.disabled = false;
			saveBtn.textContent = '添加群组';
		}
	}
}

// 加载群组管理员列表
async function loadGroupAdmins(groupId) {
	try {
		console.log('加载群组管理员列表:', groupId);
		
		const response = await fetch(`/api/group-config/admins/${encodeURIComponent(groupId)}`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			}
		});
		
		if (response.ok) {
			const data = await response.json();
			if (data.success) {
				currentAdmins = data.admins || [];
				renderAdminsList();
			} else {
				console.warn('获取管理员列表失败:', data.error);
				currentAdmins = [];
				renderAdminsList();
			}
		} else {
			console.warn('获取管理员列表API调用失败:', response.status);
			currentAdmins = [];
			renderAdminsList();
		}
	} catch (error) {
		console.error('加载群组管理员列表失败:', error);
		currentAdmins = [];
		renderAdminsList();
	}
}

// 渲染管理员列表
function renderAdminsList() {
	const adminsList = document.getElementById('adminsList');
	if (!adminsList) return;
	
	adminsList.innerHTML = '';
	
	if (!currentAdmins || currentAdmins.length === 0) {
		adminsList.innerHTML = '<div class="empty-state">暂无管理员</div>';
		return;
	}
	
	currentAdmins.forEach((admin, index) => {
		const adminItem = document.createElement('div');
		adminItem.className = 'admin-item';
		
		const permissionBadge = admin.permission_level === 'super_admin' 
			? '<span class="permission-badge super-admin">超级管理</span>'
			: '<span class="permission-badge admin">管理员</span>';
		
		const grantedByText = admin.granted_by ? 
			`<div class="admin-granted">由 ${admin.granted_by_name} 添加</div>` : '';
		
		const canDelete = admin.permission_level === 'admin'; // 只能删除普通管理员
		
		adminItem.innerHTML = `
			<div class="admin-info">
				<div class="admin-title">
					${admin.user_name || `用户 ${admin.user_id}`} ${permissionBadge}
				</div>
				<div class="admin-id">ID: ${admin.user_id}</div>
				${grantedByText}
				<div class="admin-time">添加时间: ${new Date(admin.created_at).toLocaleString()}</div>
			</div>
			${canDelete ? `
				<div class="admin-actions">
					<button class="btn btn-sm btn-danger" onclick="removeAdmin(${admin.user_id})">
						🗑️ 删除
					</button>
				</div>
			` : ''}
		`;
		adminsList.appendChild(adminItem);
	});
}

// 保存管理员
async function saveAdmin() {
	const adminUserIdInput = document.getElementById('adminUserId');
	const adminUserNameInput = document.getElementById('adminUserName');
	
	const userId = adminUserIdInput ? parseInt(adminUserIdInput.value.trim()) : 0;
	const userName = adminUserNameInput ? adminUserNameInput.value.trim() : '';
	
	if (!userId || isNaN(userId)) {
		showToast('请输入有效的用户ID', 'error');
		return;
	}
	
	if (!userName) {
		showToast('请输入用户名', 'error');
		return;
	}
	
	// 检查是否已存在
	const exists = currentAdmins.some(admin => admin.user_id === userId);
	if (exists) {
		showToast('该用户已是管理员', 'error');
		return;
	}
	
	try {
		// 禁用按钮并显示加载状态
		const saveBtn = document.getElementById('saveAdminBtn');
		if (saveBtn) {
			saveBtn.disabled = true;
			saveBtn.textContent = '添加中...';
		}
		
		const response = await fetch('/api/group-config/add-admin', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({
				chatId: selectedGroupId,
				targetUserId: userId,
				targetUserName: userName
			})
		});
		
		const result = await response.json();
		
		if (response.ok && result.success) {
			// 重新加载管理员列表
			await loadGroupAdmins(selectedGroupId);
			
			hideModal('addAdminModal');
			showToast(result.message || '管理员添加成功', 'success');
			
		} else {
			showToast(result.error || '添加管理员失败', 'error');
		}
		
	} catch (error) {
		console.error('添加管理员失败:', error);
		showToast('添加管理员失败: ' + error.message, 'error');
	} finally {
		// 恢复按钮状态
		const saveBtn = document.getElementById('saveAdminBtn');
		if (saveBtn) {
			saveBtn.disabled = false;
			saveBtn.textContent = '添加管理员';
		}
	}
}

// 删除管理员
async function removeAdmin(userId) {
	if (!confirm('确定要删除此管理员吗？')) {
		return;
	}
	
	try {
		const response = await fetch('/api/group-config/remove-admin', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({
				chatId: selectedGroupId,
				targetUserId: userId
			})
		});
		
		const result = await response.json();
		
		if (response.ok && result.success) {
			// 重新加载管理员列表
			await loadGroupAdmins(selectedGroupId);
			
			showToast(result.message || '管理员删除成功', 'success');
		} else {
			showToast(result.error || '删除管理员失败', 'error');
		}
		
	} catch (error) {
		console.error('删除管理员失败:', error);
		showToast('删除管理员失败: ' + error.message, 'error');
	}
}

// #region 🔧 辅助工具函数

/**
 * 显示Toast提示信息
 * @param {string} message 提示信息
 * @param {string} type 提示类型：'info'|'success'|'error'|'warning'
 */
function showToast(message, type = 'info') {
	const toast = document.getElementById('toast');
	const toastMessage = document.getElementById('toastMessage');
	
	if (!toast || !toastMessage) {
		console.error('Toast elements not found');
		return;
	}
	
	// 支持多行文本显示
	toastMessage.style.whiteSpace = 'pre-line';
	toastMessage.textContent = message;
	toast.className = `toast ${type} show`;
	
	// 错误信息显示时间更长
	const duration = type === 'error' ? 6000 : 3000;
	setTimeout(() => {
		toast.classList.remove('show');
	}, duration);
}

/**
 * 获取Telegram WebApp认证Token
 * @returns {string} 认证Token
 */
function getAuthToken() {
	// 从 Telegram WebApp 获取认证令牌
	if (!window.Telegram?.WebApp?.initData) {
		throw new Error('无法获取Telegram认证数据');
	}
	return window.Telegram.WebApp.initData;
}

/**
 * HTML转义函数
 * @param {string} text 原始文本
 * @returns {string} 转义后的HTML
 */
function escapeHtml(text) {
	const div = document.createElement('div');
	div.textContent = text;
	return div.innerHTML;
}

/**
 * 显示权限要求模态框
 * @param {string} errorMessage 错误信息
 */
function showPermissionRequirementModal(errorMessage) {
	const modal = document.getElementById('permissionRequirementModal');
	const errorMessageElement = document.getElementById('permissionErrorMessage');
	
	if (!modal || !errorMessageElement) {
		console.error('Permission requirement modal elements not found');
		showToast(errorMessage, 'error');
		return;
	}
	
	// 设置错误信息
	errorMessageElement.textContent = errorMessage;
	
	// 显示模态框
	modal.style.display = 'block';
	document.body.style.overflow = 'hidden';
}

/**
 * 隐藏权限要求模态框
 */
function hidePermissionRequirementModal() {
	const modal = document.getElementById('permissionRequirementModal');
	if (modal) {
		modal.style.display = 'none';
		document.body.style.overflow = '';
	}
}

// #endregion 🔧 辅助工具函数

// #region 🗑️ 删除群组功能

/**
 * 显示删除群组模态框
 */
function showDeleteGroupModal() {
	if (!selectedGroupId || !currentGroupTitle) {
		showToast('请先选择要删除的群组', 'error');
		return;
	}
	
	// 填充群组信息
	const deleteGroupName = document.getElementById('deleteGroupName');
	const deleteGroupId = document.getElementById('deleteGroupId');
	const confirmGroupName = document.getElementById('confirmGroupName');
	
	if (deleteGroupName) deleteGroupName.textContent = currentGroupTitle;
	if (deleteGroupId) deleteGroupId.textContent = selectedGroupId;
	if (confirmGroupName) {
		confirmGroupName.value = '';
		confirmGroupName.addEventListener('input', validateDeleteConfirmation);
	}
	
	// 重置确认按钮状态
	const confirmBtn = document.getElementById('confirmDeleteGroupBtn');
	if (confirmBtn) {
		confirmBtn.disabled = true;
	}
	
	// 显示模态框
	showModal('deleteGroupModal');
}

/**
 * 验证删除确认输入
 */
function validateDeleteConfirmation() {
	const confirmGroupName = document.getElementById('confirmGroupName');
	const confirmBtn = document.getElementById('confirmDeleteGroupBtn');
	
	if (!confirmGroupName || !confirmBtn) return;
	
	const inputValue = confirmGroupName.value.trim();
	const isValid = inputValue === currentGroupTitle;
	
	confirmBtn.disabled = !isValid;
}

/**
 * 执行删除群组操作
 */
async function executeDeleteGroup() {
	if (!selectedGroupId || !currentGroupTitle) {
		showToast('删除操作失败：缺少群组信息', 'error');
		return;
	}
	
	const confirmGroupName = document.getElementById('confirmGroupName');
	if (!confirmGroupName || confirmGroupName.value.trim() !== currentGroupTitle) {
		showToast('确认名称不匹配', 'error');
		return;
	}
	
	try {
		// 禁用确认按钮并显示加载状态
		const confirmBtn = document.getElementById('confirmDeleteGroupBtn');
		if (confirmBtn) {
			confirmBtn.disabled = true;
			confirmBtn.textContent = '删除中...';
		}
		
		const response = await fetch('/api/group-config/delete-group', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `tma ${getAuthToken()}`
			},
			body: JSON.stringify({
				chatId: selectedGroupId,
				confirmName: currentGroupTitle
			})
		});
		
		const result = await response.json();
		
		if (response.ok && result.success) {
			// 从下拉框中移除已删除的群组
			const groupSelect = document.getElementById('groupSelect');
			if (groupSelect) {
				const optionToRemove = Array.from(groupSelect.options).find(
					option => option.value === selectedGroupId
				);
				if (optionToRemove) {
					optionToRemove.remove();
				}
				
				// 重置选择
				groupSelect.value = '';
				selectedGroupId = null;
				currentGroupTitle = null;
				currentUserPermission = null;
			}
			
			// 隐藏配置面板和删除按钮
			const configPanel = document.getElementById('configPanel');
			if (configPanel) configPanel.style.display = 'none';
			updateDeleteButtonVisibility();
			
			hideModal('deleteGroupModal');
			showToast(result.message || '群组删除成功', 'success');
			
		} else {
			showToast(result.error || '删除群组失败', 'error');
		}
		
	} catch (error) {
		console.error('删除群组失败:', error);
		showToast('删除群组失败: ' + error.message, 'error');
	} finally {
		// 恢复按钮状态
		const confirmBtn = document.getElementById('confirmDeleteGroupBtn');
		if (confirmBtn) {
			confirmBtn.disabled = false;
			confirmBtn.textContent = '确认删除';
		}
	}
}

/**
 * 设置删除群组模态框事件
 */
function setupDeleteGroupModalEvents() {
	// 关闭模态框按钮
	const closeDeleteGroupModal = document.getElementById('closeDeleteGroupModal');
	const cancelDeleteGroupBtn = document.getElementById('cancelDeleteGroupBtn');
	
	if (closeDeleteGroupModal) {
		closeDeleteGroupModal.addEventListener('click', () => hideModal('deleteGroupModal'));
	}
	
	if (cancelDeleteGroupBtn) {
		cancelDeleteGroupBtn.addEventListener('click', () => hideModal('deleteGroupModal'));
	}
	
	// 确认删除按钮
	const confirmDeleteGroupBtn = document.getElementById('confirmDeleteGroupBtn');
	if (confirmDeleteGroupBtn) {
		confirmDeleteGroupBtn.addEventListener('click', executeDeleteGroup);
	}
	
	// 点击背景关闭模态框
	const deleteGroupModal = document.getElementById('deleteGroupModal');
	if (deleteGroupModal) {
		deleteGroupModal.addEventListener('click', (e) => {
			if (e.target === deleteGroupModal) {
				hideModal('deleteGroupModal');
			}
		});
	}
}

// #endregion 🗑️ 删除群组功能

// 将函数设为全局，以便HTML onclick调用
window.removeTarget = removeTarget;
window.editRule = editRule;
window.removeRule = removeRule;
window.saveGroup = saveGroup;
window.saveAdmin = saveAdmin;
window.removeAdmin = removeAdmin;

// #endregion 🏗️ 群组管理功能
