<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>群组配置管理</title>
	<link rel="stylesheet" href="styles/common.css?v=071">
	<link rel="stylesheet" href="styles/group-config.css?v=071">
	<script src="telegram-web-app.js?v=071"></script>
</head>
<body>
	<div class="container">
		<header class="header">
			<h1>⚙️ 群组配置管理</h1>
			<p class="subtitle">配置群组举报功能和开关选项</p>
		</header>
		
		<!-- 加载状态 -->
		<div id="loadingIndicator" class="loading">
			<div class="spinner"></div>
			<p>正在加载群组配置...</p>
		</div>
		
		<!-- 主要内容 -->
		<div id="mainContent" class="main-content" style="display: none;">
			<!-- 群组选择 -->
			<div class="section">
				<label class="section-title" for="groupSelect">
					🏷️ 选择群组
				</label>
				<select id="groupSelect" class="select-input">
					<option value="">请选择要配置的群组...</option>
				</select>
				<p class="help-text">选择要进行配置的群组</p>
			</div>
			
			<!-- 配置面板 -->
			<div id="configPanel" class="config-panel" style="display: none;">
				<!-- 举报功能配置 -->
				<div class="section">
					<h2 class="section-title">📢 举报功能配置</h2>
					<div class="radio-group">
						<label class="radio-option">
							<input type="radio" name="reportMode" value="disabled">
							<span class="radio-custom"></span>
							<div class="option-content">
								<div class="option-title">🚫 关闭举报功能</div>
								<div class="option-desc">群员无法使用/jb命令进行举报</div>
							</div>
						</label>
						
						<label class="radio-option">
							<input type="radio" name="reportMode" value="private">
							<span class="radio-custom"></span>
							<div class="option-content">
								<div class="option-title">👥 私聊通知所有管理员</div>
								<div class="option-desc">举报将通过私聊发送给所有群管理员</div>
							</div>
						</label>
						
						<label class="radio-option">
							<input type="radio" name="reportMode" value="group">
							<span class="radio-custom"></span>
							<div class="option-content">
								<div class="option-title">📢 推送到指定管理群</div>
								<div class="option-desc">举报将发送到预设的管理群组</div>
							</div>
						</label>
					</div>
					
					<!-- 推送目标群组配置 -->
					<div id="reportTargetsSection" class="sub-section" style="display: none;">
						<h3 class="sub-title">📋 推送目标群组</h3>
						<div id="targetsList" class="targets-list">
							<!-- 动态生成的目标群组列表 -->
						</div>
						<button id="addTargetBtn" class="btn-secondary">
							➕ 添加管理群
						</button>
					</div>
				</div>
				
				<!-- 功能开关 -->
				<div class="section">
					<h2 class="section-title">🔧 功能开关</h2>
					<div class="toggles-grid">
						<div class="toggle-item">
							<div class="toggle-content">
								<div class="toggle-title">🛡️ 新人验证</div>
								<div class="toggle-desc">新成员加群是否需要验证</div>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" id="newMemberVerification">
								<span class="slider"></span>
							</label>
						</div>
						
						<div class="toggle-item">
							<div class="toggle-content">
								<div class="toggle-title">☁️ 防恶俗云过滤</div>
								<div class="toggle-desc">启用AI智能内容过滤</div>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" id="cloudFilterEnabled">
								<span class="slider"></span>
							</label>
						</div>
						
						<div class="toggle-item">
							<div class="toggle-content">
								<div class="toggle-title">📤 自动转发</div>
								<div class="toggle-desc">启用媒体内容自动转发</div>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" id="autoForwardEnabled">
								<span class="slider"></span>
							</label>
						</div>
					</div>
				</div>
				
				<!-- 群规管理 -->
				<div class="section">
					<h2 class="section-title">📝 群规管理</h2>
					<div class="section-desc">配置群规链接，新成员验证通过后将自动发送</div>
					
					<div id="rulesContainer" class="rules-container">
						<div id="rulesList" class="rules-list">
							<!-- 动态生成的群规列表 -->
						</div>
						
						<button id="addRuleBtn" class="btn-secondary">
							➕ 添加群规
						</button>
					</div>
					
					<!-- 添加/编辑群规模态框 -->
					<div id="ruleModal" class="modal" style="display: none;">
						<div class="modal-content">
							<div class="modal-header">
								<h3 id="ruleModalTitle">添加群规</h3>
								<button id="closeRuleModal" class="close-btn">×</button>
							</div>
							<div class="modal-body">
								<div class="form-group">
									<label for="ruleUrl">群规链接</label>
									<input type="url" id="ruleUrl" placeholder="https://t.me/c/1234567890/123" class="input-field">
									<div class="input-help">请输入Telegram消息链接</div>
								</div>
								<div class="form-group">
									<label for="ruleTitle">标题（可选）</label>
									<input type="text" id="ruleTitle" placeholder="群规" class="input-field" maxlength="50">
									<div class="input-help">留空则使用默认标题"群规"</div>
								</div>
							</div>
							<div class="modal-footer">
								<button id="saveRuleBtn" class="btn-primary">保存</button>
								<button id="cancelRuleBtn" class="btn-secondary">取消</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- 操作按钮 -->
		<div id="actionButtons" class="action-buttons" style="display: none;">
			<button id="saveConfigBtn" class="btn-primary" disabled>
				💾 保存配置
			</button>
			<button id="resetConfigBtn" class="btn-secondary">
				🔄 重置
			</button>
		</div>
		
		<!-- 统计信息面板（可选显示） -->
		<div id="statsPanel" class="stats-panel" style="display: none;">
			<h3 class="stats-title">📊 举报统计 (近30天)</h3>
			<div class="stats-grid">
				<div class="stat-item">
					<div class="stat-number" id="totalReports">-</div>
					<div class="stat-label">总举报数</div>
				</div>
				<div class="stat-item">
					<div class="stat-number" id="pendingReports">-</div>
					<div class="stat-label">待处理</div>
				</div>
				<div class="stat-item">
					<div class="stat-number" id="handledReports">-</div>
					<div class="stat-label">已处理</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- 模态框：添加目标群组 -->
	<div id="addTargetModal" class="modal" style="display: none;">
		<div class="modal-content">
			<div class="modal-header">
				<h3>➕ 添加管理群</h3>
				<button id="closeModal" class="close-btn">&times;</button>
			</div>
			<div class="modal-body">
				<div class="input-group">
					<label for="targetChatId">群组ID:</label>
					<input type="text" id="targetChatId" placeholder="例如: -1001234567890" class="text-input">
					<p class="help-text">群组ID通常以-100开头的负数</p>
				</div>
				<div class="input-group">
					<label for="targetTitle">群组名称:</label>
					<input type="text" id="targetTitle" placeholder="例如: 管理群" class="text-input">
				</div>
			</div>
			<div class="modal-footer">
				<button id="confirmAddTarget" class="btn-primary">确认添加</button>
				<button id="cancelAddTarget" class="btn-secondary">取消</button>
			</div>
		</div>
	</div>
	
	<!-- Toast 通知 -->
	<div id="toast" class="toast">
		<span id="toastMessage"></span>
	</div>
	
	<script src="auth.js?v=071"></script>
	<script src="logger.js?v=071"></script>
	<script src="group-config.js?v=071"></script>
</body>
</html> 